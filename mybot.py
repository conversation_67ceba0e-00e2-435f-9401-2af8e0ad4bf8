# mybot.py
import os
import re
import asyncio
import logging
import shutil  # 修复: name 'shutil' is not defined

from pyrogram import Client as PyroClient, filters
from pyrogram.enums import ParseMode
from pyrogram.types import (
    CallbackQuery,
    Message,
    InlineKeyboardMarkup,
    InlineKeyboardButton
)

from supabase import create_client, Client as SupabaseClient

# ======= 导入 douyin.py 的逻辑 =======
from douyin import (
    logger,
    RETRY_CACHE,
    MUSIC_CACHE,
    AUTHOR_SEC_UID_MAP,
    call_hybrid_video_data,
    parse_douyin_work,
    build_caption_for_single,
    download_file,
    extract_douyin_user_url,
    extract_sec_user_id_from_user_url,
    extract_douyin_video_url,
    fetch_author_posts_and_send_all,
    send_media_files,
    save_douyin_to_database
)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("mybot")

# =============== Supabase 配置 ===============
SUPABASE_URL = "https://wjanjmsywbydjbfrdkaz.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndqYW5qbXN5d2J5ZGpiZnJka2F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAxMjc1OTEsImV4cCI6MjAzNTcwMzU5MX0.ny-61EeDBsVRUr6BdWiamt9oV-6z2TC_f01FJff-9fE"
supabase: SupabaseClient = create_client(SUPABASE_URL, SUPABASE_KEY)

# =============== 数据库相关 ===============
def get_or_create_user(chat_id: int, username: str = "") -> dict:
    """
    只插入 user_id, username，其他字段(如 send_file, send_any, today, already) 让数据库用默认值
    """
    try:
        result = supabase.table("users2").select("*").eq("user_id", chat_id).execute()
        rows = result.data
        if rows:
            existing_user = rows[0]
            # 如果 username 不一致，更新一下
            if username and existing_user.get("username") != username:
                supabase.table("users2").update({"username": username}).eq("user_id", chat_id).execute()
            return existing_user
        else:
            insert_data = {
                "user_id": chat_id,
                "username": username
            }
            insert_result = supabase.table("users2").insert(insert_data).execute()
            return insert_result.data[0]
    except Exception as e:
        logger.error(f"数据库查询/插入用户失败: {e}", exc_info=True)
        return {
            "user_id": chat_id,
            "username": username
        }

def update_user(chat_id: int, updates: dict):
    try:
        supabase.table("users2").update(updates).eq("user_id", chat_id).execute()
    except Exception as e:
        logger.error(f"更新用户 {chat_id} 信息失败: {e}", exc_info=True)

# =============== Bot 配置 ===============
api_id = '25432929'
api_hash = '965c5d22f0b9d1d0326e84bbb2bb18c1'
bot_token = '6563835132:AAFKsMY5_arWjw26YqbAA_eycJ6jhEwbrao'

app = PyroClient(
    "my_douyin_bot",
    api_id=api_id,
    api_hash=api_hash,
    bot_token=bot_token
)

# =============== 回调按钮处理 ===============
@app.on_callback_query()
async def handle_callback_query(client: PyroClient, callback_query: CallbackQuery):
    chat_id = callback_query.message.chat.id
    from_username = callback_query.from_user.username or ""
    user_record = get_or_create_user(chat_id, username=from_username)

    # 可能数据库里没该字段时，给默认
    send_any = user_record.get("send_any", True)
    today_limit = user_record.get("today", 10)
    already_used = user_record.get("already", 0)
    send_file = user_record.get("send_file", False)

    if not send_any or already_used >= today_limit:
        await callback_query.answer("你已达到每日配额上限，使用量将在下个23:00清空。", show_alert=True)
        return

    data = callback_query.data

    # ------ 获取更多作者作品 ------
    if data.startswith("author_more:"):
        # 新增判断：如果免费用户（fetchall==0），提示premium信息
        if user_record.get("fetchall", 0) == 0:
            premium_msg = '您好，获取全部作品功能已向premium用户开放，获取premium请联系<a href="https://t.me/juaer">@juaer</a>，premium用户每月可处理1000条作品链接并有15次获取全部作品按钮的使用机会。'
            await callback_query.message.reply_text(premium_msg, parse_mode=ParseMode.HTML)
            return

        short_id = data.split(":", 1)[1]
        sec_user_id = AUTHOR_SEC_UID_MAP.get(short_id)
        if not sec_user_id:
            await callback_query.answer("无效sec_user_id", show_alert=True)
            return

        await callback_query.answer("开始获取作者全部作品...", show_alert=False)

        async def progress_cb(info):
            # 以 Toast 形式更新进度
            try:
                await callback_query.answer(info, show_alert=False)
            except:
                pass

        # 将用户的send_file等传下去
        user_settings = {
            "send_file": send_file
        }
        result = await fetch_author_posts_and_send_all(
            client,
            chat_id,
            sec_user_id,
            progress_callback=lambda msg: asyncio.create_task(progress_cb(msg)),
            user_settings=user_settings
        )

        if not result.get("ok"):
            msg = result.get("msg", "获取失败")
            await callback_query.message.reply_text(f"发生错误: {msg}")
        else:
            total = result.get("total", 0)
            failed_num = result.get("failed_num", 0)
            if failed_num > 0:
                retry_key = result.get("retry_key")
                btn = InlineKeyboardButton("重试失败作品", callback_data=f"retry_failed:{retry_key}")
                markup = InlineKeyboardMarkup([[btn]])
                await callback_query.message.reply_text(
                    f"作品发送完毕！共处理 {total} 个，其中 {failed_num} 个失败。",
                    reply_markup=markup
                )
            else:
                await callback_query.message.reply_text(f"作品发送完毕！共处理 {total} 个。")

        # 更新配额
        update_user(chat_id, {"already": already_used + 1})

    # ------ 下载音乐 ------
    elif data.startswith("music_download:"):
        music_key = data.split(":", 1)[1]
        music_info = MUSIC_CACHE.get(music_key)
        if not music_info:
            await callback_query.answer("音乐链接已失效或不存在。", show_alert=True)
            return

        if not send_any or already_used >= today_limit:
            update_user(chat_id, {"send_any": False})
            await callback_query.answer("你已达到每日配额上限。", show_alert=True)
            return

        await callback_query.answer("正在下载音乐...", show_alert=False)

        music_url = music_info["url"]
        caption_text = music_info["caption"]
        
        # 提取音乐标题
        music_title = ""
        music_author = ""
        for line in caption_text.split('\n'):
            if line.startswith("音乐:"):
                try:
                    parts = line.split("音乐:", 1)[1].strip().split(" - ", 1)
                    music_title = parts[0]
                    if len(parts) > 1:
                        music_author = parts[1]
                except:
                    pass
        if not music_title:
            music_title = f"music_{music_key}"

        ddata = download_file(music_url)
        if not ddata:
            await callback_query.message.reply_text("音乐下载失败或已失效。")
            return

        ct = ddata.get("content_type", "").lower()
        bdata = ddata["binary_data"]

        dpath = os.path.join("downloads", f"music_{music_key}")
        os.makedirs(dpath, exist_ok=True)
        ext = ".mp3"
        if "aac" in ct:
            ext = ".aac"
        elif "m4a" in ct:
            ext = ".m4a"
        elif "wav" in ct:
            ext = ".wav"
        elif "mpeg" in ct:
            ext = ".mp3"

        safe_filename = re.sub(r'[<>:"/\\|?*]', '_', f"{music_title} - {music_author}".strip())
        music_file = os.path.join(dpath, f"{safe_filename}{ext}")
        with open(music_file, "wb") as f:
            f.write(bdata)

        await client.send_audio(chat_id, audio=music_file)

        update_user(chat_id, {"already": already_used + 1})
        if os.path.exists(dpath):
            shutil.rmtree(dpath)

    # ------ 重试失败的作品 ------
    elif data.startswith("retry_failed:"):
        retry_key = data.split(":", 1)[1]
        retry_info = RETRY_CACHE.get(retry_key)
        if not retry_info:
            await callback_query.answer("重试信息已过期", show_alert=True)
            return
        
        if not send_any or already_used >= today_limit:
            update_user(chat_id, {"send_any": False})
            await callback_query.answer("你已达到每日配额上限。", show_alert=True)
            return

        sec_user_id = retry_info["sec_user_id"]
        failed_ids = retry_info["videos"]

        await callback_query.answer("开始重试失败的作品...", show_alert=False)

        async def progress_cb(info):
            try:
                await callback_query.answer(info, show_alert=False)
            except:
                pass

        user_settings = {
            "send_file": send_file
        }
        result = await fetch_author_posts_and_send_all(
            client,
            chat_id,
            sec_user_id,
            retry_list=failed_ids,
            progress_callback=lambda msg: asyncio.create_task(progress_cb(msg)),
            user_settings=user_settings
        )
        RETRY_CACHE.pop(retry_key, None)

        if result.get("ok"):
            fn = result.get("failed_num", 0)
            tot = result.get("total", len(failed_ids))
            if fn > 0:
                await callback_query.message.reply_text(f"重试完成，处理 {tot} 个作品，仍有 {fn} 个失败。")
            else:
                await callback_query.message.reply_text(f"重试完成，共处理 {tot} 个，全部成功。")
        else:
            msg = result.get("msg", "重试失败")
            await callback_query.message.reply_text(f"重试出错: {msg}")

        update_user(chat_id, {"already": already_used + 1})

    else:
        await callback_query.answer("未知操作", show_alert=True)

# =============== 命令处理 ===============
@app.on_message(filters.command(["start"]))
async def start_command(client, message: Message):
    chat_id = message.chat.id
    username = message.from_user.username or ""
    user_record = get_or_create_user(chat_id, username=username)

    welcome_text = (
        f"👋 你好！这是抖音下载Bot。\n"
        f"你的 Chat ID: <code>{chat_id}</code>\n"
        f"当前数据库记录的用户名: @{user_record.get('username', '')}"
    )
    await message.reply_text(welcome_text, parse_mode=ParseMode.HTML)

@app.on_message(filters.command(["help"]))
async def help_command(client, message: Message):
    chat_id = message.chat.id
    user_record = get_or_create_user(chat_id, username=message.from_user.username or "")
    if not user_record.get("send_any", True):
        return

    await message.reply_text(
        "【使用指南】\n"
        "1) 直接发送抖音作品链接(视频/图集)给我。\n"
        "2) 或发送作者主页链接 (https://www.douyin.com/user/xxxx)，获取全部作品。\n"
        "3) 如看到「获取音乐」按钮，可点击获取背景音乐。\n"
        "4) 如果数据库中 send_file=true，则会额外打包原文件ZIP给你。\n"
        "5) 若超过每日配额，会被提示明日再来~"
    )

@app.on_message(filters.command(["id"]))
async def id_command(client, message: Message):
    chat_id = message.chat.id
    username = message.from_user.username or ""
    user_record = get_or_create_user(chat_id, username=username)

    await message.reply_text(
        f"你的 Chat ID: <code>{chat_id}</code>\n"
        f"数据库中的用户名: @{user_record.get('username', '')}",
        parse_mode=ParseMode.HTML
    )

@app.on_message(filters.command(["premium"]))
async def premium_command(client, message: Message):
    premium_msg = '您好，获取全部作品功能已向premium用户开放，获取premium请联系<a href="https://t.me/juaer">@juaer</a>，premium用户每月可处理1000条作品链接并有15次获取全部作品按钮的使用机会。'
    await message.reply_text(premium_msg, parse_mode=ParseMode.HTML)

# =============== 处理文本消息：抖音链接 ===============

# --- 新增/修改: 一个辅助异步函数，用来延迟删除消息 ---
async def cleanup_messages(user_msg: Message, process_msg: Message, delay: float = 5.0):
    """
    delay秒后依次删除提示消息和用户输入消息。
    """
    await asyncio.sleep(delay)
    # 尝试删除处理进度消息
    try:
        await process_msg.delete()
    except:
        pass
    # 尝试删除用户的原输入
    try:
        await user_msg.delete()
    except:
        pass

@app.on_message(filters.text & ~filters.command(["start", "help", "id"]))
async def handle_text_message(client: PyroClient, message: Message):
    try:
        chat_id = message.chat.id
        from_username = message.from_user.username or ""
        user_record = get_or_create_user(chat_id, from_username)

        send_any = user_record.get("send_any", True)
        today_limit = user_record.get("today", 10)
        already_used = user_record.get("already", 0)
        send_file = user_record.get("send_file", False)

        if not send_any or already_used >= today_limit:
            update_user(chat_id, {"send_any": False})
            await message.reply_text("你已达到每日配额上限，使用量将在下一个23:00清空。")
            return

        text = message.text.strip()
        if not text:
            return

        # 1) 如果是作者主页链接
        user_url = extract_douyin_user_url(text)
        if user_url:
            sec_user_id = extract_sec_user_id_from_user_url(user_url)
            if not sec_user_id:
                await message.reply_text("无法从链接中提取sec_user_id")
                return

            # 新增判断：如果免费用户（fetchall==0），提示premium信息
            if user_record.get("fetchall", 0) == 0:
                premium_msg = '您好，获取全部作品功能已向premium用户开放，获取premium请联系<a href="https://t.me/juaer">@juaer</a>，premium用户每月可处理1000条作品链接并有15次获取全部作品按钮的使用机会。'
                await message.reply_text(premium_msg, parse_mode=ParseMode.HTML)
                return

            processing_msg = await message.reply_text("正在获取作者全部作品，请稍候...")

            async def progress_cb(info):
                try:
                    await processing_msg.edit_text(info)
                except:
                    pass

            user_settings = {
                "send_file": send_file
            }
            result = await fetch_author_posts_and_send_all(
                client,
                chat_id,
                sec_user_id,
                progress_callback=lambda msg: asyncio.create_task(progress_cb(f"进度: {msg}")),
                user_settings=user_settings
            )

            if not result.get("ok"):
                msg = result.get("msg", "获取失败")
                await processing_msg.edit_text(f"错误: {msg}")
            else:
                tot = result.get("total", 0)
                fn = result.get("failed_num", 0)
                if fn > 0:
                    rk = result.get("retry_key")
                    btn = InlineKeyboardButton("重试失败作品", callback_data=f"retry_failed:{rk}")
                    markup = InlineKeyboardMarkup([[btn]])
                    await processing_msg.edit_text(
                        f"作品发送完毕！共处理 {tot} 个，其中 {fn} 个失败。",
                        reply_markup=markup
                    )
                else:
                    await processing_msg.edit_text(f"作品发送完毕！共处理 {tot} 个。")

            update_user(chat_id, {"already": already_used + 1})

            await cleanup_messages(message, processing_msg, delay=5.0)
            return

        # 2) 如果是单作品链接
        douyin_url = extract_douyin_video_url(text)
        if douyin_url:
            processing_msg = await message.reply_text("正在解析...")

            resp_data = call_hybrid_video_data(douyin_url)
            parsed_data = parse_douyin_work(resp_data)
            if not parsed_data or not parsed_data.get("base_info", {}).get("aweme_id"):
                await processing_msg.edit_text("解析失败，链接无效或视频无法获取。")
                await cleanup_messages(message, processing_msg, delay=5.0)
                return
                
            # 保存到数据库
            save_douyin_to_database(parsed_data)

            caption_text = build_caption_for_single(parsed_data)
            sec_uid = parsed_data["author_info"].get("sec_uid", "")
            aweme_id = parsed_data["base_info"]["aweme_id"]

            wdir = os.path.join("downloads", f"single_{chat_id}_{aweme_id}")
            os.makedirs(wdir, exist_ok=True)
            downloaded_files = []

            try:
                media_info = parsed_data["media_info"]
                images = media_info.get("images", [])
                play_url = media_info.get("play_url", "")

                if images:
                    await processing_msg.edit_text("检测到图集，正在下载...")
                    for idx, img_obj in enumerate(images):
                        img_url = img_obj.get("url")
                        if img_url:
                            ddata = download_file(img_url)
                            if ddata:
                                fpath = os.path.join(wdir, f"{aweme_id}_{idx}.jpg")
                                with open(fpath, "wb") as f:
                                    f.write(ddata["binary_data"])
                                downloaded_files.append(fpath)
                        vid = img_obj.get("video")
                        if vid and vid.get("url"):
                            ddata = download_file(vid["url"])
                            if ddata:
                                vpath = os.path.join(wdir, f"{aweme_id}_{idx}.mp4")
                                with open(vpath, "wb") as f:
                                    f.write(ddata["binary_data"])
                                downloaded_files.append(vpath)
                else:
                    if play_url:
                        await processing_msg.edit_text("检测到单视频，正在下载...")
                        ddata = download_file(play_url)
                        if ddata:
                            fpath = os.path.join(wdir, f"{aweme_id}.mp4")
                            with open(fpath, "wb") as f:
                                f.write(ddata["binary_data"])
                            downloaded_files.append(fpath)

                if downloaded_files:
                    music_url = parsed_data["music_info"].get("play_url", "")
                    user_settings = {"send_file": send_file}
                    await send_media_files(
                        client=client,
                        chat_id=chat_id,
                        media_files=downloaded_files,
                        douyin_url=douyin_url,
                        caption_text=caption_text,
                        sec_uid=sec_uid,
                        music_link=music_url,
                        from_author_posts=False,
                        user_settings=user_settings
                    )
                    await processing_msg.edit_text("发送完成。")
                    update_user(chat_id, {"already": already_used + 1})
                else:
                    await processing_msg.edit_text("下载失败或没有可下载的媒体。")
            finally:
                if os.path.exists(wdir):
                    shutil.rmtree(wdir)

            await cleanup_messages(message, processing_msg, delay=5.0)
            return

        await message.reply_text("未检测到有效的抖音链接。")

    except Exception as e:
        logger.error(f"处理文本消息出错: {e}", exc_info=True)
        await message.reply_text(f"❌ 出错: {e}")

# =============== 入口 ===============
def main():
    logger.info("Starting my_douyin_bot...")
    app.run()

if __name__ == "__main__":
    main()
