// bot.js - 抖音下载 Telegram Bot - 第一部分
const { Telegraf, Markup } = require('telegraf');
const { createClient } = require('@supabase/supabase-js');
const fetch = require('node-fetch');
const fs = require('fs/promises');
const fsSync = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const archiver = require('archiver');
const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);
// 添加dotenv加载环境变量
require('dotenv').config();

/**
 * 环境变量说明:
 * 
 * SUPABASE_URL - Supabase数据库URL
 * SUPABASE_KEY - Supabase匿名密钥
 * BUTHISBOT - Telegram Bot Token
 * TELEGRAM_CHAT_ID - 存储频道ID，用于保存媒体文件
 * API_BASE_URL - API基本URL，默认为http://localhost:8080
 * 
 * 其他可选的环境变量:
 * TIKHUB_API_KEY - TikHub API密钥
 */

// =============== 环境变量校验 ===============
function checkRequiredEnvVars() {
  const requiredVars = [
    'SUPABASE_URL', 
    'SUPABASE_KEY',
    'BUTHISBOT',
    'TELEGRAM_CHAT_ID'
  ];
  
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.error('错误: 以下必要的环境变量未设置:');
    missingVars.forEach(varName => console.error(`- ${varName}`));
    console.error('请在.env文件中设置这些变量');
    process.exit(1);
  }
}

// 执行环境变量检查
checkRequiredEnvVars();

// =============== 日志配置 ===============
const logger = {
  info: (message) => console.log(`INFO: ${message}`),
  error: (message, error) => console.error(`ERROR: ${message}`, error),
  warning: (message, error) => console.warn(`WARNING: ${message}`, error)
};

// =============== Supabase 配置 ===============
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// =============== 全局变量 ===============
const DELETE_FILES = true;
const STORAGE_CHANNEL_ID = parseInt(process.env.TELEGRAM_CHAT_ID);
const RETRY_CACHE = {};
const MUSIC_CACHE = {};
const API_BASE_URL = process.env.API_BASE_URL || "http://localhost:8080";

// 添加检查用户是否已订阅某作者的函数
async function isUserSubscribedToAuthor(chatId, uid) {
  try {
    if (!chatId || !uid) {
      return false;
    }
    
    const { count, error } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("user_id", chatId)
      .eq("uid", uid)
      .limit(1);
    
    if (error) {
      logger.error(`检查用户 ${chatId} 是否订阅作者 ${uid} 失败: ${error.message}`, error);
      return false;
    }
    
    return count > 0;
  } catch (error) {
    logger.error(`检查订阅状态异常: ${error.message}`, error);
    return false;
  }
}

// =============== 抖音解析器 ===============
class DouyinParser {
  static _sortBitrateItems(bitRateList) {
    // Sort by resolution, FPS, and bitrate
    return bitRateList.sort((a, b) => {
      const aPlayAddr = a.play_addr || {};
      const bPlayAddr = b.play_addr || {};
      const aResolution = (aPlayAddr.width || 0) * (aPlayAddr.height || 0);
      const bResolution = (bPlayAddr.width || 0) * (bPlayAddr.height || 0);
      
      if (aResolution !== bResolution) return bResolution - aResolution;
      
      const aFps = a.FPS || 0;
      const bFps = b.FPS || 0;
      if (aFps !== bFps) return bFps - aFps;
      
      const aBitrate = a.bit_rate || 0;
      const bBitrate = b.bit_rate || 0;
      return bBitrate - aBitrate;
    });
  }

  static _getFirstUrl(urlData) {
    const urlList = urlData?.url_list || [];
    if (Array.isArray(urlList) && urlList.length > 0) {
      return urlList[0];
    }
    return "";
  }

  static _getBestPlayUrl(videoInfo) {
    const bitRateList = videoInfo?.bit_rate || [];
    if (bitRateList.length > 0) {
      const sortedItems = DouyinParser._sortBitrateItems(bitRateList);
      for (const item of sortedItems) {
        const playAddr = item.play_addr || {};
        const bestUrl = DouyinParser._getFirstUrl(playAddr);
        if (bestUrl && !bestUrl.includes("watermark")) {
          return bestUrl;
        }
      }
    }

    // Fallback to default play_addr
    const fallbackUrl = DouyinParser._getFirstUrl(videoInfo?.play_addr || {});
    if (fallbackUrl && !fallbackUrl.includes("watermark")) {
      return fallbackUrl;
    }
    return fallbackUrl;
  }

  static _getBestImageUrl(imgData) {
    const urlList = imgData?.url_list || [];
    // Prefer URLs without "water" (watermark)
    const noWaterUrls = urlList.filter(u => !u.includes("water"));
    if (noWaterUrls.length > 0) {
      return noWaterUrls[0];
    }
    if (urlList.length > 0) {
      return urlList[0];
    }
    return "";
  }

  static parseAweme(respData) {
    try {
      const data = respData?.data || {};
      if (typeof data !== 'object') {
        return {};
      }

      const awemeId = data.aweme_id || "";
      const desc = data.desc || "";
      const createTimeTs = data.create_time || 0;
      
      let createTimeStr = "";
      try {
        createTimeStr = new Date(createTimeTs * 1000).toISOString()
          .replace('T', ' ').slice(0, 19);
      } catch (e) {}

      const author = data.author || {};
      const authorInfo = {
        nickname: author.nickname || "",
        uid: author.uid || "",
        sec_uid: author.sec_uid || "",
        unique_id: author.unique_id || "",
        follower_count: author.follower_count || 0,
        total_favorited: author.total_favorited || 0,
      };

      // Location
      let locationInfo = {};
      const anchorInfo = data.anchor_info || {};
      try {
        if (typeof anchorInfo.extra === 'string') {
          const extraData = JSON.parse(anchorInfo.extra);
          if (typeof extraData === 'object') {
            const addressInfo = extraData.address_info || {};
            locationInfo = {
              province: addressInfo.province || "",
              city: addressInfo.city || ""
            };
          }
        }
      } catch (e) {}

      // Statistics
      const statistics = data.statistics || {};
      const stats = {
        comment_count: statistics.comment_count || 0,
        digg_count: statistics.digg_count || 0,
        collect_count: statistics.collect_count || 0,
        share_count: statistics.share_count || 0
      };

      // Music
      const music = data.music || {};
      const musicInfo = {
        title: music.title || "",
        author: music.author || "",
        play_url: DouyinParser._getFirstUrl(music.play_url || {})
      };

      // Media
      const images = data.images || [];
      const videoInfo = data.video || {};
      const durationMs = data.duration || 0;
      const durationS = durationMs / 1000.0;

      let mediaInfo;
      if (images.length > 0) {
        // Image collection
        mediaInfo = {
          cover_url: "",
          play_url: "",
          width: 0,
          height: 0,
          duration: durationS,
          images: []
        };
        
        for (const img of images) {
          const imgInfo = {
            url: DouyinParser._getBestImageUrl(img),
            width: img.width || 0,
            height: img.height || 0,
            video: null
          };
          
          const vidData = img.video;
          if (vidData && typeof vidData === 'object') {
            const bestPlayUrl = DouyinParser._getBestPlayUrl(vidData);
            imgInfo.video = {
              url: bestPlayUrl,
              width: vidData.width || 0,
              height: vidData.height || 0
            };
          }
          
          mediaInfo.images.push(imgInfo);
        }
      } else {
        // Regular video
        mediaInfo = {
          cover_url: DouyinParser._getFirstUrl(videoInfo.cover || {}),
          play_url: DouyinParser._getBestPlayUrl(videoInfo),
          width: videoInfo.width || 0,
          height: videoInfo.height || 0,
          duration: durationS,
          images: []
        };
      }

      return {
        base_info: {
          aweme_id: awemeId,
          desc: desc,
          create_time: createTimeStr
        },
        author_info: authorInfo,
        location_info: locationInfo,
        statistics: stats,
        music_info: musicInfo,
        media_info: mediaInfo
      };
    } catch (error) {
      logger.error("parse_aweme error:", error);
      return {};
    }
  }

  static parseUser(respData) {
    try {
      const data = respData?.data || {};
      if (typeof data !== 'object') {
        return null;
      }
      
      const author = data.author || {};
      if (!author || !author.uid || !author.sec_uid) {
        return null;
      }
      
      const userData = {
        uid: author.uid,
        sec_uid: author.sec_uid,
        short_id: author.short_id || null,
        unique_id: author.unique_id || null,
        
        nickname: author.nickname || null,
        signature: author.signature || null,
        user_age: author.user_age || null,
        avatar_thumb_uri: author.avatar_thumb?.uri || null,
        avatar_thumb_url: author.avatar_thumb?.url_list?.[0] || null,
        create_time: author.create_time 
          ? new Date(author.create_time * 1000).toISOString() 
          : null,
        
        follower_count: author.follower_count || 0,
        following_count: author.following_count || 0,
        total_favorited: author.total_favorited || 0,
        favoriting_count: author.favoriting_count || 0,
        
        status: author.status || 1,
        verification_type: author.verification_type || null,
        user_canceled: author.user_canceled || false,
        mate_add_permission: author.mate_add_permission || null,
        
        custom_verify: author.custom_verify || null,
        enterprise_verify_reason: author.enterprise_verify_reason || null,
        
        prevent_download: author.prevent_download || false,
        contacts_status: author.contacts_status || null,
        
        cover_url: author.cover_url?.[0]?.url_list?.[0] || null,
        
        last_fetched_at: new Date().toISOString()
      };
      
      return userData;
    } catch (error) {
      logger.error("parse_user error:", error);
      return null;
    }
  }
}

// =============== 解析和下载函数 ===============
function parseDouyinWork(respData) {
  return DouyinParser.parseAweme(respData);
}

function parseDouyinUser(respData) {
  return DouyinParser.parseUser(respData);
}

// 在API调用函数中添加重试机制的通用函数
async function retryApiCallWithBackoff(apiCall, maxRetries = 3, initialBackoffMs = 1000) {
  let retries = 0;
  let backoffTime = initialBackoffMs;
  
  while (retries < maxRetries) {
    try {
      return await apiCall();
    } catch (error) {
      const is429 = error.status === 429 
                    || (error.message && error.message.includes('429')) 
                    || (error.message && error.message.toLowerCase().includes('too many requests'));
      
      if (is429 && retries < maxRetries - 1) {
        retries++;
        logger.warning(`API限流(429)，第${retries}次重试，等待${backoffTime/1000}秒...`);
        await new Promise(resolve => setTimeout(resolve, backoffTime));
        backoffTime *= 2;
      } else {
        throw error;
      }
    }
  }
}

// =============== 数据库保存函数 ===============

// 新版本的 saveDouyinToDatabase
async function saveDouyinToDatabase(parsedData) {
  try {
    const baseInfo = parsedData?.base_info || {};
    const authorInfo = parsedData?.author_info || {};
    const locationInfo = parsedData?.location_info || {};
    const musicInfo = parsedData?.music_info || {};
    const mediaInfo = parsedData?.media_info || {};
    
    const data = {
      aweme_id: baseInfo.aweme_id || null,
      description: baseInfo.desc || null,
      create_time: baseInfo.create_time || null,
      nickname: authorInfo.nickname || null,
      uid: authorInfo.uid || null,
      sec_uid: authorInfo.sec_uid || null,
      unique_id: authorInfo.unique_id || null,
      follower_count: authorInfo.follower_count || 0,
      total_favorited: authorInfo.total_favorited || 0,
      province: locationInfo.province || null,
      city: locationInfo.city || null,
      music_title: musicInfo.title || null,
      music_author: musicInfo.author || null,
      music_play_url: musicInfo.play_url || null,
      cover_url: mediaInfo.cover_url || null,
      media_play_url: mediaInfo.play_url || null,
      duration: mediaInfo.duration || 0,
      file_id: null,
      large: null,
      bot_token: process.env.BUTHISBOT
    };
    
    if (!data.aweme_id) {
      logger.error("保存数据库失败: 缺少作品ID");
      return false;
    }
    
    const { error: insertError } = await supabase
      .from("douyin")
      .insert([data]);
    
    if (insertError) {
      // 如果已经存在，这里一般是主键冲突
      if (insertError.code === '23505') {
        logger.warning(`尝试插入已存在的作品 ${data.aweme_id}，跳过插入。`);
        return true;
      } else {
        logger.error(`保存作品 ${data.aweme_id} 到数据库失败: ${insertError.message}`, insertError);
        return false;
      }
    }
    
    logger.info(`成功保存作品 ${data.aweme_id} 到数据库`);
    return true;
  } catch (error) {
    logger.error(`保存数据库异常: ${error.message}`, error);
    return false;
  }
}

// 单纯保存用户数据
async function saveDouyinUserToDatabase(userData) {
  try {
    if (!userData || !userData.uid || !userData.sec_uid) {
      logger.error("保存用户数据失败: 缺少必要字段uid或sec_uid");
      return false;
    }
    
    const { error } = await supabase
      .from("douyin_user")
      .upsert(userData);
    
    if (error) {
      logger.error(`保存用户数据到Supabase失败: ${error.message}`, error);
      return false;
    }
    
    logger.info(`成功保存用户 ${userData.uid} (${userData.nickname || "未知昵称"}) 到数据库`);
    return true;
  } catch (error) {
    logger.error(`保存用户数据库异常: ${error.message}`, error);
    return false;
  }
}

// 去掉了对 profileData 的处理，这个函数现在仅存为合并基础数据示例
async function saveEnhancedUserToDatabase(baseUserData) {
  try {
    if (!baseUserData || !baseUserData.uid || !baseUserData.sec_uid) {
      logger.error("保存增强用户数据失败: 缺少必要的基础字段uid或sec_uid");
      return false;
    }
    
    // 直接 upsert 基础数据
    const { error } = await supabase
      .from("douyin_user")
      .upsert(baseUserData);
    
    if (error) {
      logger.error(`保存增强用户数据到Supabase失败: ${error.message}`, error);
      return false;
    }
    
    logger.info(`成功保存/更新用户 ${baseUserData.uid} (${baseUserData.nickname || "未知昵称"}) 到数据库`);
    return true;
  } catch (error) {
    logger.error(`保存增强用户数据库异常: ${error.message}`, error);
    return false;
  }
}

// =============== URL解析函数 ===============
function extractDouyinVideoUrl(text) {
  const pattern = /(https?:\/\/v\.douyin\.com\/\S+|https?:\/\/www\.douyin\.com\/video\/\S+|https?:\/\/www\.douyin\.com\/note\/\S+|https?:\/\/www\.douyin\.com\/lvdetail\/\S+)/;
  const match = text.match(pattern);
  return match ? match[1].trim() : "";
}

function extractDouyinUserUrl(text) {
  const pattern = /(https:\/\/www\.douyin\.com\/user\/[^\s]+)/;
  const match = text.match(pattern);
  return match ? match[1].trim() : "";
}

function extractSecUserIdFromUserUrl(url) {
  const pattern = /https:\/\/www\.douyin\.com\/user\/([^/?]+)/;
  const match = url.match(pattern);
  return match ? match[1] : "";
}

// =============== 抖音API调用函数（剩余可用的） ===============
async function fetchUserPostVideosApi(secUserId, maxCursor = 0, count = 40) {
  return retryApiCallWithBackoff(async () => {
    try {
      const baseUrl = `${API_BASE_URL}/api/douyin/web/fetch_user_post_videos`;
      const params = new URLSearchParams({
        sec_user_id: secUserId,
        max_cursor: maxCursor,
        count: count
      });
      
      const response = await fetch(`${baseUrl}?${params}`, { timeout: 30000 });
      
      if (response.status === 429) {
        throw { status: 429, message: "API限流，请求过于频繁" };
      }
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      logger.error(`fetch_user_post_videos_api error: ${error.message}`, error);
      throw error;
    }
  }, 3, 2000);
}

async function fetchOneVideoApi(awemeId) {
  return retryApiCallWithBackoff(async () => {
    try {
      const baseUrl = `${API_BASE_URL}/api/hybrid/video_data`;
      const douyinUrl = `https://www.douyin.com/video/${awemeId}`;
      const params = new URLSearchParams({
        url: douyinUrl,
        minimal: "false"
      });
      
      const response = await fetch(`${baseUrl}?${params}`, { timeout: 30000 });
      
      if (response.status === 429) {
        throw { status: 429, message: "API限流，请求过于频繁" };
      }
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const jsonData = await response.json();
      
      const isNotAvailable = (
        !jsonData 
        || !jsonData.data 
        || jsonData.status_code === 404
        || jsonData.status_code === 10000
        || (jsonData.data && jsonData.data.status_code === 404)
        || (jsonData.data && jsonData.data.error_code === 10000)
        || (jsonData.data && jsonData.data.aweme_id === undefined)
      );
      
      if (isNotAvailable) {
        logger.warning(`检测到不可用作品: ${awemeId}，将标记为not available`);
        const minimalData = {
          base_info: { aweme_id: awemeId, desc: "Not Available" },
          author_info: {},
          location_info: {},
          music_info: {},
          media_info: {}
        };
        await saveDouyinToDatabase(minimalData);
        
        return { 
          data: { aweme_id: awemeId },
          not_available: true,
          status_message: "作品不可用或已删除"
        };
      }
      
      return jsonData;
    } catch (error) {
      logger.error(`fetch_one_video_api error: ${error.message}`, error);
      throw error;
    }
  }, 3, 2000);
}

// =============== 文件下载和发送 ===============
async function downloadFile(url, maxRetries = 3, timeoutMs = 60000) {
  let currentRetry = 0;
  let backoffTime = 2000;

  while (currentRetry < maxRetries) {
    try {
      logger.info(`[downloadFile] GET ${url}, attempt ${currentRetry + 1}`);
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeoutMs);
      
      try {
        const response = await fetch(url, { signal: controller.signal, timeout: timeoutMs });
        clearTimeout(timeoutId);
        
        if (response.status === 429) {
          logger.warning(`[downloadFile] 遇到API限流(429)，等待${backoffTime/1000}秒后重试...`);
          await new Promise(resolve => setTimeout(resolve, backoffTime));
          backoffTime *= 2;
          currentRetry++;
          continue;
        }
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const buffer = await response.buffer();
        const contentType = response.headers.get("content-type") || "";
        
        return { 
          binary_data: buffer, 
          content_type: contentType, 
          size: buffer.length
        };
      } catch (fetchError) {
        clearTimeout(timeoutId);
        throw fetchError;
      }
    } catch (error) {
      const isTimeout = error.name === 'AbortError' 
                        || error.type === 'aborted' 
                        || (error.message && error.message.includes('timeout'));
      
      const is429 = error.status === 429 
                    || (error.message && error.message.includes('429')) 
                    || (error.message && error.message.toLowerCase().includes('too many requests'));
      
      const errorMsg = isTimeout 
        ? `下载超时 (${timeoutMs/1000}秒)` 
        : is429
        ? `API限流(429)` 
        : error.message;
      
      logger.warning(`[downloadFile] Error: ${errorMsg}`, error);
      
      if (currentRetry < maxRetries - 1) {
        const waitSec = is429 
          ? Math.pow(2, currentRetry + 2) 
          : Math.pow(2, currentRetry + 1);
        
        logger.info(`等待 ${waitSec} 秒后重试...`);
        await new Promise(resolve => setTimeout(resolve, waitSec * 1000));
        currentRetry++;
      } else if (currentRetry === maxRetries - 1) {
        logger.error(`[downloadFile] 达到最大重试次数 (${maxRetries})，下载失败: ${url}`);
        currentRetry++;
      }
    }
  }
  
  return null;
}

function buildCaptionForSingle(parsedData) {
  const base = parsedData?.base_info || {};
  const author = parsedData?.author_info || {};
  const stats = parsedData?.statistics || {};
  const music = parsedData?.music_info || {};
  const location = parsedData?.location_info || {};

  const lines = [];
  if (base.aweme_id) {
    lines.push(`作品ID: ${base.aweme_id}`);
  }
  if (base.desc) {
    lines.push(`描述: ${base.desc}`);
  }
  if (base.create_time) {
    lines.push(`发布时间: ${base.create_time}`);
  }
  if (author.nickname) {
    lines.push(`作者昵称: ${author.nickname}`);
  }
  if (author.unique_id) {
    lines.push(`抖音号: ${author.unique_id}`);
  }
  if (author.uid) {
    lines.push(`作者UID: ${author.uid}`);
  }

  const fc = author.follower_count;
  const tf = author.total_favorited;
  if (fc !== undefined && tf !== undefined) {
    lines.push(`粉丝数: ${fc} | 获赞: ${tf}`);
  }

  const province = location.province || "";
  const city = location.city || "";
  if (province || city) {
    lines.push(`地点: ${province} ${city}`.trim());
  }

  const digg = stats.digg_count;
  const cmt = stats.comment_count;
  const shr = stats.share_count;
  const col = stats.collect_count;
  const statsParts = [];
  
  if (digg !== undefined) {
    statsParts.push(`点赞: ${digg}`);
  }
  if (cmt !== undefined) {
    statsParts.push(`评论: ${cmt}`);
  }
  if (shr !== undefined) {
    statsParts.push(`分享: ${shr}`);
  }
  if (col !== undefined) {
    statsParts.push(`收藏: ${col}`);
  }
  
  if (statsParts.length > 0) {
    lines.push(statsParts.join(" | "));
  }

  if (music.title && music.author) {
    lines.push(`音乐: ${music.title} - ${music.author}`);
  }

  return lines.join("\n").trim();
}

function isImageFile(filename) {
  const ext = path.extname(filename.toLowerCase());
  return ['.jpg', '.jpeg', '.png', '.gif', '.webp'].includes(ext);
}

function isVideoFile(filename) {
  const ext = path.extname(filename.toLowerCase());
  return ['.mp4', '.mov', '.avi', '.mkv', '.webm'].includes(ext);
}

function isAudioFile(filename) {
  const ext = path.extname(filename.toLowerCase());
  return ['.mp3', '.wav', '.m4a', '.flac', '.aac'].includes(ext);
}

async function getVideoDimensions(videoPath) {
  try {
    const { stdout } = await execPromise(
      `ffprobe -v error -select_streams v:0 -show_entries stream=width,height -of json "${videoPath}"`
    );
    const data = JSON.parse(stdout);
    const stream = data.streams?.[0] || {};
    return [stream.width, stream.height];
  } catch (error) {
    logger.error(`Error getting video dimensions: ${error.message}`, error);
    return [null, null];
  }
}

async function getVideoFileSizeInMB(filePath) {
  try {
    const stats = await fs.stat(filePath);
    return stats.size / (1024 * 1024);
  } catch (error) {
    logger.error(`获取文件大小失败: ${error.message}`, error);
    return 0;
  }
}

async function createZipArchive(files, outputPath) {
  return new Promise((resolve, reject) => {
    const output = fsSync.createWriteStream(outputPath);
    const archive = archiver('zip', { zlib: { level: 9 } });
    
    output.on('close', () => resolve(outputPath));
    archive.on('error', err => reject(err));
    archive.pipe(output);
    
    for (const file of files) {
      archive.file(file, { name: path.basename(file) });
    }
    archive.finalize();
  });
}

function chunkList(array, size) {
  const chunks = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}

async function getOrCreateUser(chatId, username = "") {
  try {
    const { data, error } = await supabase
      .from("users2")
      .select("*")
      .eq("user_id", chatId)
      .limit(1);
    
    if (error) throw error;
    
    if (data && data.length > 0) {
      const existingUser = data[0];
      let updates = {};
      
      if (username && existingUser.username !== username) {
        updates.username = username;
      }
      
      if (existingUser.douyin === null || existingUser.douyin === undefined || existingUser.douyin === '') {
        updates.douyin = "tier0";
        if (existingUser.today === null || existingUser.today === undefined) {
          updates.today = 20;
        }
        if (existingUser.already === null || existingUser.already === undefined) {
          updates.already = 0;
        }
      }
      
      if (Object.keys(updates).length > 0) {
        await supabase
          .from("users2")
          .update(updates)
          .eq("user_id", chatId);
        
        const { data: updatedData, error: updatedError } = await supabase
          .from("users2")
          .select("*")
          .eq("user_id", chatId)
          .limit(1);
          
        if (updatedError) throw updatedError;
        return updatedData?.[0] || existingUser; 
      }
      
      return existingUser;
    } else {
      const insertData = {
        user_id: chatId,
        username: username || null,
        douyin: "tier0",
        today: 20,
        already: 0
      };
      
      const { data: insertResult, error: insertError } = await supabase
        .from("users2")
        .insert([insertData])
        .select();
      
      if (insertError) throw insertError;
      return insertResult?.[0] || insertData;
    }
  } catch (error) {
    logger.error(`数据库查询/插入用户失败: ${error.message}`, error);
    return {
      user_id: chatId,
      username: username || null,
      douyin: "tier0",
      today: 20,
      already: 0
    };
  }
}

async function updateUser(chatId, updates) {
  try {
    await supabase
      .from("users2")
      .update(updates)
      .eq("user_id", chatId);
  } catch (error) {
    logger.error(`更新用户 ${chatId} 信息失败: ${error.message}`, error);
  }
}

function isPremiumUser(userRecord) {
  return userRecord && userRecord.douyin === "tier1";
}

// 检查用户是否已订阅某作者
async function isUserSubscribedToAuthor(chatId, uid) {
  try {
    if (!chatId || !uid) {
      return false;
    }
    
    const { count, error } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("user_id", chatId)
      .eq("uid", uid)
      .limit(1);
    
    if (error) {
      logger.error(`检查用户 ${chatId} 是否订阅作者 ${uid} 失败: ${error.message}`, error);
      return false;
    }
    
    return count > 0;
  } catch (error) {
    logger.error(`检查订阅状态异常: ${error.message}`, error);
    return false;
  }
}

// 订阅数量检查
async function checkSubscriptionLimit(chatId, ctx) {
  try {
    const { count, error } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("user_id", chatId);
    
    if (error) {
      logger.error(`检查用户 ${chatId} 订阅数量失败: ${error.message}`, error);
      await ctx.telegram.sendMessage(chatId, "检查订阅数量失败，请稍后再试。");
      return false;
    }
    
    if (count >= 2) {
      const message = '您是普通用户，最多可以订阅2个作者。升级到Premium可获得更高配额，请联系 <a href="https://t.me/juaer">@juaer</a> 了解详情。';
      await ctx.telegram.sendMessage(chatId, message, { parse_mode: 'HTML' });
      return false;
    }
    
    return true;
  } catch (error) {
    logger.error(`检查订阅数量异常: ${error.message}`, error);
    await ctx.telegram.sendMessage(chatId, "检查订阅数量出错，请稍后再试。");
    return false;
  }
}

function detectFileType(fileId) {
  if (!fileId || typeof fileId !== 'string') return null;

  if (fileId.startsWith('AgACAg')) {
    return 'photo';
  } else if (fileId.startsWith('BAACAg')) {
    return 'video';
  } else if (fileId.startsWith('BQACAg') || fileId.startsWith('BQADAg') || fileId.startsWith('BQACAgU')) {
    return 'document';
  } else if (fileId.startsWith('CQADAg') || fileId.startsWith('CQACAgU')) {
    return 'audio';
  } else if (fileId.startsWith('AwADAg')) {
    return 'voice';
  } else if (fileId.startsWith('CAADAg')) {
    return 'sticker';
  } else if (fileId.startsWith('CgADAg')) {
    return 'animation';
  } else if (fileId.startsWith('DQADAg')) {
    return 'video_note';
  }
  return null;
}

async function sendSingleFile(ctx, filePath, caption, replyMarkup = null) {
  try {
    if (!fsSync.existsSync(filePath) || fsSync.statSync(filePath).size === 0) {
      logger.info(`文件无效或大小为0: ${filePath}`);
      return null;
    }

    if (isVideoFile(filePath)) {
      const fileSizeMB = await getVideoFileSizeInMB(filePath);
      if (fileSizeMB > 49) {
        logger.info(`文件 ${filePath} 大小超过49MB (${fileSizeMB.toFixed(2)}MB)，放弃处理`);
        return null;
      }
      logger.info(`为视频 ${filePath} 生成缩略图`);
      const thumbnailPath = await extractVideoThumbnail(filePath);
      
      const [width, height] = await getVideoDimensions(filePath);
      const options = { 
        caption: caption, 
        width: width,
        height: height,
        parse_mode: 'HTML'
      };
      
      if (thumbnailPath && fsSync.existsSync(thumbnailPath)) {
        options.thumb = { source: thumbnailPath };
      }
      
      const message = await ctx.telegram.sendVideo(
        STORAGE_CHANNEL_ID,
        { source: filePath }, 
        options
      );
      
      if (thumbnailPath && fsSync.existsSync(thumbnailPath)) {
        try {
          await fs.unlink(thumbnailPath);
        } catch (err) {
          logger.warning(`删除缩略图失败: ${err.message}`);
        }
      }
      
      return message?.video?.file_id || null;
    } else if (isImageFile(filePath)) {
      const options = { caption, parse_mode: 'HTML' };
      const message = await ctx.telegram.sendPhoto(
        STORAGE_CHANNEL_ID,
        { source: filePath }, 
        options
      );
      return message?.photo ? message.photo[message.photo.length - 1]?.file_id : null;
    } else if (isAudioFile(filePath)) {
      const options = { caption, parse_mode: 'HTML' };
      const message = await ctx.telegram.sendAudio(
        STORAGE_CHANNEL_ID,
        { source: filePath }, 
        options
      );
      return message?.audio?.file_id || null;
    } else {
      const options = { caption, parse_mode: 'HTML' };
      const message = await ctx.telegram.sendDocument(
        STORAGE_CHANNEL_ID,
        { source: filePath }, 
        options
      );
      return message?.document?.file_id || null;
    }
  } catch (error) {
    logger.error(`sendSingleFile error: ${error.message}`, error);
    if (error.message && (error.message.includes('413') || error.message.includes('too large'))) {
      try {
        const options = { caption: `${caption}\n[文件太大，作为文档发送]`, parse_mode: 'HTML' };
        const message = await ctx.telegram.sendDocument(
          STORAGE_CHANNEL_ID,
          { source: filePath }, 
          options
        );
        return message?.document?.file_id || null;
      } catch (docError) {
        logger.error(`尝试作为文档发送也失败: ${docError.message}`, docError);
      }
    }
    return null;
  }
}

async function sendMediaByFileIds(ctx, fileIds, caption, markup = null, awemeId = null) {
  if (!fileIds || fileIds.length === 0) {
    return;
  }
  
  const fileIdArray = Array.isArray(fileIds) ? fileIds : fileIds.split(';').filter(id => id);
  if (fileIdArray.length === 0) {
    return;
  }
  
  const chatId = ctx.chat.id;
  
  try {
    const isStorageChannel = (chatId === STORAGE_CHANNEL_ID);
    if (isStorageChannel) {
      for (const fileId of fileIdArray) {
        const fileType = detectFileType(fileId);
        if (fileType === 'photo') {
          await ctx.telegram.sendPhoto(chatId, fileId);
        } else if (fileType === 'video') {
          await ctx.telegram.sendVideo(chatId, fileId);
        } else if (fileType === 'document') {
          await ctx.telegram.sendDocument(chatId, fileId);
        } else if (fileType === 'audio') {
          await ctx.telegram.sendAudio(chatId, fileId);
        } else if (fileType === 'voice') {
          await ctx.telegram.sendVoice(chatId, fileId);
        } else if (fileType === 'sticker') {
          await ctx.telegram.sendSticker(chatId, fileId);
        } else if (fileType === 'animation') {
          await ctx.telegram.sendAnimation(chatId, fileId);
        } else if (fileType === 'video_note') {
          await ctx.telegram.sendVideoNote(chatId, fileId);
        } else {
          await ctx.telegram.sendDocument(chatId, fileId);
        }
      }
      return;
    }
    
    const photoIds = [];
    const videoIds = [];
    const zipIds = [];
    const audioIds = [];
    const otherIds = [];
    
    for (const fileId of fileIdArray) {
      const fileType = detectFileType(fileId);
      if (fileType === 'photo') {
        photoIds.push(fileId);
      } else if (fileType === 'video') {
        videoIds.push(fileId);
      } else if (fileType === 'document') {
        zipIds.push(fileId);
      } else if (fileType === 'audio') {
        audioIds.push(fileId);
      } else {
        otherIds.push(fileId);
      }
    }
    
    const mediaIds = [...photoIds, ...videoIds];
    let mediaSent = false;
    for (let i = 0; i < mediaIds.length; i += 10) {
      const chunk = mediaIds.slice(i, i + 10);
      
      if (chunk.length === 1) {
        const fileId = chunk[0];
        const fileType = detectFileType(fileId);
        if (fileType === 'photo') {
          await ctx.telegram.sendPhoto(chatId, fileId);
          mediaSent = true;
        } else if (fileType === 'video') {
          await ctx.telegram.sendVideo(chatId, fileId);
          mediaSent = true;
        }
      } else if (chunk.length > 1) {
        const mediaGroup = chunk.map(fileId => {
          const fileType = detectFileType(fileId);
          return {
            type: fileType === 'photo' ? 'photo' : 'video',
            media: fileId
          };
        });
        await ctx.telegram.sendMediaGroup(chatId, mediaGroup);
        mediaSent = true;
      }
    }
    
    if (caption || mediaSent) {
      if (caption) {
        await ctx.telegram.sendMessage(chatId, caption, {
          parse_mode: 'HTML',
          ...(markup ? markup : {})
        });
      } else if (markup) {
        await ctx.telegram.sendMessage(chatId, "_", {
          parse_mode: 'HTML',
          ...(markup ? markup : {}),
          disable_notification: true 
        });
      }
    }
    
  } catch (error) {
    const isWrongFileIdError = error.response 
      && error.response.error_code === 400 
      && error.response.description 
      && error.response.description.includes('wrong file identifier');
    
    if (isWrongFileIdError) {
      logger.error(`通过file_id发送媒体失败 (awemeId: ${awemeId}) - Wrong File Identifier: ${error.message}`);
      await markWorkAsFailed(awemeId, ctx.chat.id);
      return; 
    }
    
    logger.error(`通过file_id发送媒体失败 (awemeId: ${awemeId}): ${error.message}`, error);
    try {
      let sentAny = false;
      for (const fileId of fileIdArray) {
        const fileType = detectFileType(fileId);
        if (fileType === 'photo') {
          await ctx.telegram.sendPhoto(chatId, fileId);
          sentAny = true;
        } else if (fileType === 'video') {
          await ctx.telegram.sendVideo(chatId, fileId);
          sentAny = true;
        }
      }
      if (caption || sentAny) {
        if (caption) {
          await ctx.telegram.sendMessage(chatId, caption, {
            parse_mode: 'HTML',
            ...(markup ? markup : {})
          });
        } else if (markup) {
          await ctx.telegram.sendMessage(chatId, "_", {
            parse_mode: 'HTML',
            ...(markup ? markup : {})
          });
        }
      }
    } catch (fallbackError) {
      const isFallbackWrongFileIdError = fallbackError.response 
        && fallbackError.response.error_code === 400 
        && fallbackError.response.description 
        && fallbackError.response.description.includes('wrong file identifier');
      
      if (isFallbackWrongFileIdError) {
        logger.error(`单个发送也失败 (awemeId: ${awemeId}) - Wrong File Identifier: ${fallbackError.message}`);
        await markWorkAsFailed(awemeId, ctx.chat.id);
        return;
      }
      
      logger.error(`单个发送也失败 (awemeId: ${awemeId}): ${fallbackError.message}`, fallbackError);
      try {
        if (caption) {
          await ctx.telegram.sendMessage(chatId, caption, {
            parse_mode: 'HTML',
            ...(markup ? markup : {})
          });
        }
      } catch (finalError) {
        logger.error(`发送说明文本也失败: ${finalError.message}`, finalError);
      }
    }
  }
}

async function sendMediaFiles(
  ctx,
  mediaFiles,
  douyinUrl,
  captionText,
  secUid,
  musicLink = "",
  fromAuthorPosts = false,
  userSettings = null,
  awemeId = null
) {
  const chatId = ctx.chat?.id;
  if (mediaFiles.length > 20) {
    logger.info(`媒体文件数量 (${mediaFiles.length}) 超过 20，将标记为large`);
    if (awemeId) {
      try {
        await updateLargeField(ctx, awemeId, "媒体文件数量过多");
        return "";
      } catch (dbError) {
        logger.error(`更新large字段时出错 (媒体文件数量过多): ${dbError.message}`, dbError);
        return "";
      }
    }
    return "";
  }

  // 新增: 检查是否为长视频，从数据库中获取时长信息
  if (awemeId) {
    try {
      const { data: videoData, error: videoError } = await supabase
        .from("douyin")
        .select("duration")
        .eq("aweme_id", awemeId)
        .limit(1);
        
      if (!videoError && videoData && videoData.length > 0) {
        const rawDuration = videoData[0].duration || 0;
        // 数据库中可能已经是秒，也可能是毫秒，需要智能判断
        let durationSeconds = rawDuration;
        if (rawDuration > 1000 && rawDuration % 1 === 0) { // 如果是整数且大于1000，可能是毫秒
          durationSeconds = rawDuration / 1000;
        }
        
        if (durationSeconds > 600) {
          // 超过10分钟的视频直接标记为failed
          logger.info(`sendMediaFiles检测到超长视频: ${awemeId}, 时长: ${durationSeconds.toFixed(2)}秒 > 10分钟 (原始值: ${rawDuration})，将直接标记为failed`);
          
          const failedReason = "video_too_long:视频时长超过10分钟";
          const { error: failedUpdateError } = await supabase
            .from("douyin")
            .update({ failed: failedReason })
            .eq("aweme_id", awemeId);
          
          if (failedUpdateError) {
            logger.error(`更新超长视频 ${awemeId} 的failed字段失败: ${failedUpdateError.message}`, failedUpdateError);
          } else {
            logger.info(`已将超长视频 ${awemeId} 的failed字段更新为 ${failedReason}`);
          }
          
          if (ctx.telegram && chatId) {
            try {
              await ctx.telegram.sendMessage(chatId, "视频时长超过10分钟，文件过大无法处理。");
            } catch (msgError) {
              logger.error(`发送超长视频通知失败: ${msgError.message}`);
            }
          }
          return "";
        }
        else if (durationSeconds > 360) {
          logger.info(`sendMediaFiles检测到长视频: ${awemeId}, 时长: ${durationSeconds.toFixed(2)}秒 > 6分钟 (原始值: ${rawDuration})，将标记为large并跳过处理`);
          await updateLargeField(ctx, awemeId, "视频时长超过6分钟");
          if (ctx.telegram && chatId) {
            try {
              await ctx.telegram.sendMessage(chatId, "视频时长超过6分钟，后台处理中，请稍后再查看。");
            } catch (msgError) {
              logger.error(`发送长视频通知失败: ${msgError.message}`);
            }
          }
          return "";
        }
      }
    } catch (dbError) {
      logger.error(`获取视频时长信息失败: ${dbError.message}`, dbError);
    }
  }

  const normalSizeFiles = [];
  let hasLargeIndividualFile = false;
  for (const file of mediaFiles) {
    if (!fsSync.existsSync(file) || fsSync.statSync(file).size === 0) {
      continue;
    }
    const fileSizeMB = await getVideoFileSizeInMB(file);
    if (fileSizeMB > 49) {
      hasLargeIndividualFile = true;
      logger.info(`发现单个大文件: ${file}, 大小: ${fileSizeMB.toFixed(2)}MB, 将放弃处理`);
      break;
    }
    normalSizeFiles.push(file);
  }

  if (hasLargeIndividualFile && awemeId) {
    try {
      await updateLargeField(ctx, awemeId, "单个文件过大");
      return "";
    } catch (dbError) {
      logger.error(`更新large字段时出错 (单个文件过大): ${dbError.message}`, dbError);
      return "";
    }
  }
  
  if (normalSizeFiles.length === 0) {
    logger.warning(`作品 ${awemeId} 没有符合大小要求的文件可处理。`);
    return "";
  }

  let zipFileId = null;
  let zipPath = null;
  let hasLargeZipFile = false;
  try {
    const baseName = path.basename(normalSizeFiles[0]).split('_')[0] || "douyin";
    const outputDir = path.dirname(normalSizeFiles[0]);
    zipPath = path.join(outputDir, `${baseName}.zip`);
    await createZipArchive(normalSizeFiles, zipPath);
    
    if (fsSync.existsSync(zipPath) && fsSync.statSync(zipPath).size > 0) {
      const zipSizeMB = await getVideoFileSizeInMB(zipPath);
      if (zipSizeMB > 49) {
        hasLargeZipFile = true;
        logger.info(`ZIP文件大小(${zipSizeMB.toFixed(2)}MB)超过49MB，将放弃处理`);
      }
    } else {
      logger.warning(`创建的ZIP文件无效或大小为0: ${zipPath}`);
      zipPath = null; 
    }
  } catch (error) {
    logger.error(`创建ZIP失败: ${error.message}`, error);
    zipPath = null;
  }

  if (hasLargeZipFile && awemeId) {
    try {
      await updateLargeField(ctx, awemeId, "ZIP文件过大");
      if (DELETE_FILES && zipPath && fsSync.existsSync(zipPath)) {
        try { await fs.unlink(zipPath); } catch (e) { logger.warn(`清理过大ZIP文件失败: ${e.message}`); }
      }
      return "";
    } catch (dbError) {
      logger.error(`更新large字段时出错 (ZIP文件过大): ${dbError.message}`, dbError);
      return "";
    }
  }

  const allFileIds = [];
  let audioFileId = null;

  try {
    if (zipPath && fsSync.existsSync(zipPath)) {
      try {
        const message = await ctx.telegram.sendDocument(
          STORAGE_CHANNEL_ID,
          { source: zipPath },
          { caption: captionText, parse_mode: 'HTML' }
        );
        zipFileId = message?.document?.file_id;
        if (zipFileId) {
          allFileIds.push(zipFileId);
          logger.info(`成功上传ZIP文件并获取file_id: ${zipFileId}`);
        }
      } catch (zipUploadError) {
        logger.error(`上传ZIP文件失败: ${zipUploadError.message}`, zipUploadError);
      }
    }

    const audioFiles = normalSizeFiles.filter(isAudioFile);
    const nonAudioFiles = normalSizeFiles.filter(file => !isAudioFile(file));
    const singleProcessVideos = [];
    const mediaGroupFiles = [];

    for (const filePath of nonAudioFiles) {
      if (isVideoFile(filePath)) {
        const fileSizeMB = await getVideoFileSizeInMB(filePath);
        if (fileSizeMB >= 9 && fileSizeMB <= 49) {
          singleProcessVideos.push(filePath);
        } else {
          mediaGroupFiles.push(filePath);
        }
      } else {
        mediaGroupFiles.push(filePath);
      }
    }

    for (const videoPath of singleProcessVideos) {
      try {
        const fileId = await sendSingleFile(ctx, videoPath, "");
        if (fileId) {
          allFileIds.push(fileId);
        }
      } catch (singleVideoError) {
        logger.error(`单独处理视频 ${videoPath} 失败: ${singleVideoError.message}`, singleVideoError);
      }
    }

    if (mediaGroupFiles.length > 0) {
      for (const chunk of chunkList(mediaGroupFiles, 10)) {
        try {
          const mediaGroup = await Promise.all(chunk.map(async (filePath) => {
            if (isImageFile(filePath)) {
              return { type: 'photo', media: { source: filePath } };
            } else if (isVideoFile(filePath)) {
              const [width, height] = await getVideoDimensions(filePath);
              return { type: 'video', media: { source: filePath }, width: width, height: height };
            } else {
              return { type: 'document', media: { source: filePath } };
            }
          }));
          const mediaGroupResult = await ctx.telegram.sendMediaGroup(STORAGE_CHANNEL_ID, mediaGroup);
          if (Array.isArray(mediaGroupResult)) {
            for (const msg of mediaGroupResult) {
              let fileId = null;
              if (msg.photo?.length > 0) fileId = msg.photo[msg.photo.length - 1].file_id;
              else if (msg.video) fileId = msg.video.file_id;
              else if (msg.document) fileId = msg.document.file_id;
              if (fileId) allFileIds.push(fileId);
            }
          }
        } catch (error) {
          logger.error(`发送媒体组出错: ${error.message}`, error);
          for (const filePath of chunk) {
            try {
              const fileId = await sendSingleFile(ctx, filePath, "");
              if (fileId) allFileIds.push(fileId);
            } catch (singleSendError) {
              logger.error(`单个发送文件失败 ${filePath}: ${singleSendError.message}`, singleSendError);
            }
          }
        }
      }
    }

    for (const audioFile of audioFiles) {
      try {
        const audioFileCaption = audioFiles.length === 1 && normalSizeFiles.length === 1 
          ? captionText 
          : `音乐文件 - ${path.basename(audioFile)}`;
        const fileId = await sendSingleFile(ctx, audioFile, audioFileCaption);
        if (fileId) {
          allFileIds.push(fileId);
          audioFileId = fileId;
        }
      } catch (audioError) {
        logger.error(`发送音频文件失败 ${audioFile}: ${audioError.message}`, audioError);
      }
    }

    if (allFileIds.length > 0 && awemeId) {
      const uniqueFileIds = [...new Set(allFileIds)];
      if (uniqueFileIds.length > 0) {
        const fileIdString = uniqueFileIds.join(';');
        try {
          const { error } = await supabase
            .from("douyin")
            .update({ file_id: fileIdString, bot_token: process.env.BUTHISBOT })
            .eq("aweme_id", awemeId);
          if (error) {
            logger.error(`更新作品 ${awemeId} 的file_id失败: ${error.message}`, error);
          } else {
            logger.info(`成功更新作品 ${awemeId} 的file_id: ${fileIdString}`);
          }
        } catch (dbUpdateError) {
          logger.error(`更新作品 ${awemeId} 的file_id时发生异常: ${dbUpdateError.message}`, dbUpdateError);
        }
      } else {
        logger.warning(`作品 ${awemeId} 没有收集到任何有效的 file_id，数据库未更新。`);
      }
    } else if (awemeId) {
      logger.warning(`作品 ${awemeId} 没有收集到任何有效的 file_id，数据库未更新。`);
    }

    const chunkArray = (array, size) => {
      const result = [];
      for (let i = 0; i < array.length; i += size) {
        result.push(array.slice(i, i + size));
      }
      return result;
    };

    let markup;
    
    // 获取作者uid，用于检查订阅状态
    let uid = null;
    try {
      if (secUid) { // secUid comes from function argument
        const { data: userData, error } = await supabase
          .from("douyin_user")
          .select("uid") // Only select uid as that's what we need here
          .eq("sec_uid", secUid)
          .limit(1);
        
        if (error) {
          logger.error(`sendMediaFiles: 获取作者uid失败 (sec_uid: ${secUid}): ${error.message}`, error);
        } else if (userData && userData.length > 0 && userData[0].uid) {
          uid = userData[0].uid;
        } else {
          logger.warning(`sendMediaFiles: 未找到sec_uid ${secUid} 对应的uid`);
        }
      }
    } catch (error) {
      logger.error(`sendMediaFiles: 获取作者uid时发生异常: ${error.message}`, error);
    }
    
    // 检查用户是否已订阅作品或直播
    let isSubscribedWork = false;
    let isSubscribedLive = false;
    try {
      if (uid && chatId) {
        isSubscribedWork = await isUserSubscribedWork(chatId, uid);
        isSubscribedLive = await isUserSubscribedLive(chatId, uid);
      }
    } catch (error) {
      logger.error(`sendMediaFiles: 检查订阅状态出错: ${error.message}`, error);
    }
    
    const buttons = [];
    buttons.push(Markup.button.url("打开", douyinUrl));
    if (audioFileId && awemeId) buttons.push(Markup.button.callback("获取音乐", `music:${awemeId}`));
    if (zipFileId && awemeId) buttons.push(Markup.button.callback("获取文件", `zip:${awemeId}`));
    
    // 根据作品/直播订阅状态添加按钮
    if (uid) {
      if (isSubscribedWork) {
        buttons.push(Markup.button.callback("取消订阅作品", `unsub:${uid}`));
      } else {
        buttons.push(Markup.button.callback("订阅作品", `sub:${uid}`));
      }

      if (isSubscribedLive) {
        buttons.push(Markup.button.callback("取消订阅直播", `unsub_live:${uid}`));
      } else {
        buttons.push(Markup.button.callback("订阅直播", `sub_live:${uid}`));
      }
    } else if (secUid) {
      logger.warning(`sendMediaFiles: 作者UID未找到 (sec_uid: ${secUid})，无法生成订阅按钮。`);
    }
    
    markup = Markup.inlineKeyboard(chunkArray(buttons, 3));

    if (allFileIds.length > 0) {
      const finalFileIdsToSend = [...new Set(allFileIds)].filter(id => id);
      if (finalFileIdsToSend.length > 0) {
        await sendMediaByFileIds(ctx, finalFileIdsToSend, captionText, markup, awemeId);
        return finalFileIdsToSend.join(';');
      }
      return "";
    }

    logger.warning(`作品 ${awemeId} 处理完成，但没有收集到任何 file_id 可发送给用户 ${chatId}`);
    return "";
  } finally {
    if (DELETE_FILES && zipPath && fsSync.existsSync(zipPath)) {
      try { await fs.unlink(zipPath); } catch (e) { logger.warn(`清理ZIP文件失败: ${e.message}`); }
    }
  }
}

async function updateLargeField(ctx, awemeId, reason) {
  const chatId = ctx.chat?.id;
  if (!chatId) {
    logger.warning(`无法更新 large 字段，因为缺少 chatId (awemeId: ${awemeId})`);
    return;
  }
  
  try {
    const { data: currentData, error: queryError } = await supabase
      .from("douyin")
      .select("large")
      .eq("aweme_id", awemeId)
      .single();
    
    if (queryError && queryError.code !== 'PGRST116') {
      logger.error(`查询作品 ${awemeId} 的large字段失败: ${queryError.message}`, queryError);
    }
    
    let newLargeValue = null;
    const currentChatIdStr = String(chatId);
    const currentValue = currentData?.large;
    if (currentValue) {
      const chatIds = currentValue.split(';').filter(id => id);
      if (!chatIds.includes(currentChatIdStr)) {
        chatIds.push(currentChatIdStr);
        newLargeValue = chatIds.join(';');
      } else {
        newLargeValue = currentValue;
      }
    } else {
      newLargeValue = currentChatIdStr;
    }
    
    const { error: updateError } = await supabase
      .from("douyin")
      .update({ large: newLargeValue })
      .eq("aweme_id", awemeId);
      
    if (updateError) {
      logger.error(`更新作品 ${awemeId} 的large字段失败 (${reason}): ${updateError.message}`, updateError);
      throw updateError;
    } else {
      logger.info(`已将作品 ${awemeId} 的large字段更新为 ${newLargeValue} (原因: ${reason})`);
      if (ctx.reply) {
        try {
          await ctx.reply("文件过大，后台处理中，请稍后再查看。");
        } catch (replyError) {
          logger.error(`发送大文件通知失败: ${replyError.message}`, replyError);
        }
      }
    }
  } catch (error) {
    logger.error(`执行 updateLargeField 时发生未预料错误 (awemeId: ${awemeId}): ${error.message}`, error);
  }
}

// =============== 实时监听 ===============
async function initRealtimeListener(bot) {
  try {
    logger.info("正在初始化Realtime监听...");

    const channel = supabase
      .channel('douyin_all_updates')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'douyin',
        },
        async (payload) => {
          try {
            const oldData = payload.old;
            const newData = payload.new;
            const aweme_id = newData.aweme_id;

            if (!aweme_id) {
              logger.warning(`检测到更新事件，但缺少 aweme_id`, payload);
              return;
            }

            const oldFailed = oldData?.failed;
            const newFailed = newData?.failed;
            const fileIdChanged = newData?.file_id && newData.file_id !== oldData?.file_id;
            logger.info(`[Realtime Update] aweme_id: ${aweme_id}, old.failed: "${oldFailed}", new.failed: "${newFailed}", fileIdChanged: ${fileIdChanged}`);

            const pendingMatch = oldFailed?.match(/^file id pending:(\d+)$/);
            if (pendingMatch && newFailed === "file id done") {
              const targetChatId = parseInt(pendingMatch[1]);
              if (!targetChatId) {
                logger.error(`[Realtime Resend Error] 无法从 oldFailed ("${oldFailed}") 中解析有效的 chatId for ${aweme_id}`);
                return;
              }
              logger.info(`[Realtime Condition Met] failed: pending:${targetChatId} -> done for ${aweme_id}. Triggering resend to ${targetChatId}.`);
              
              const { data: workFullData, error: workError } = await supabase
                .from("douyin")
                .select("*")
                .eq("aweme_id", aweme_id)
                .limit(1);

              if (workError || !workFullData || workFullData.length === 0) {
                logger.error(`重新发送：获取作品 ${aweme_id} 的完整信息失败: ${workError?.message || "无数据"}`, workError);
                return;
              }
              
              const work = workFullData[0];
              const file_id = work.file_id;
              if (!file_id) {
                logger.warning(`重新发送：作品 ${aweme_id} 的 file_id 为空，无法重新发送`);
                return;
              }
              
              const caption = buildCaptionForSingle({
                base_info: { aweme_id: work.aweme_id, desc: work.description, create_time: work.create_time },
                author_info: { nickname: work.nickname, uid: work.uid, sec_uid: work.sec_uid, unique_id: work.unique_id, follower_count: work.follower_count, total_favorited: work.total_favorited },
                location_info: { province: work.province, city: work.city },
                music_info: { title: work.music_title, author: work.music_author, play_url: work.music_play_url }
              });
              const fileIdsToSend = file_id.split(';').filter(id => id);
              let zipId = null;
              let audioFileId = null;
              for (const id of fileIdsToSend) {
                if (detectFileType(id) === 'document') zipId = id;
                if (detectFileType(id) === 'audio') audioFileId = id;
              }
              const chunkArray = (array, size) => {
                const result = [];
                for (let i = 0; i < array.length; i += size) {
                  result.push(array.slice(i, i + size));
                }
                return result;
              };

              let resendSuccess = true;
              const targetChatIds = [targetChatId];
              
              for (const chatId of targetChatIds) {
                if (chatId === STORAGE_CHANNEL_ID) continue;
                try {
                  const buttons = [];
                  buttons.push(Markup.button.url("打开", `https://www.douyin.com/video/${work.aweme_id}`));
                  if (audioFileId) buttons.push(Markup.button.callback("获取音乐", `music:${work.aweme_id}`));
                  if (zipId) buttons.push(Markup.button.callback("获取文件", `zip:${work.aweme_id}`));
                  
                  // 检查用户是否已订阅该作者
                  let isSubscribed = false;
                  try {
                    if (work.uid && chatId) {
                      const { count, error } = await supabase
                        .from("telegram_douyin_map")
                        .select("*", { count: 'exact', head: true })
                        .eq("user_id", chatId)
                        .eq("uid", work.uid)
                        .limit(1);
                      
                      if (!error) {
                        isSubscribed = count > 0;
                      }
                    }
                  } catch (error) {
                    logger.error(`检查订阅状态出错: ${error.message}`, error);
                  }
                  
                  // 根据订阅状态添加按钮
                  if (work.uid) {
                    if (isSubscribed) {
                      // 已订阅，显示取消订阅按钮
                      buttons.push(Markup.button.callback("取消订阅", `unsub:${work.uid}`));
                    } else if (work.sec_uid) {
                      // 未订阅，显示订阅按钮
                      const shortId = uuidv4().substring(0, 8);
                      buttons.push(Markup.button.callback("订阅", `sub:${shortId}`));
                    }
                  }
                  
                  const markup = Markup.inlineKeyboard(chunkArray(buttons, 4));
                  
                  await sendMediaByFileIds(
                    { 
                      telegram: bot.telegram, 
                      chat: { id: chatId }, 
                      reply: (text, extra) => bot.telegram.sendMessage(chatId, text, extra) 
                    },
                    fileIdsToSend.filter(id => id !== zipId),
                    caption,
                    markup,
                    aweme_id
                  );
                  logger.info(`已向用户 ${chatId} 重新发送作品 ${aweme_id} 的更新`);
                } catch (userError) {
                  logger.error(`向用户 ${chatId} 重新发送作品 ${aweme_id} 失败: ${userError.message}`, userError);
                  const isWrongFileIdError = userError.response
                    && userError.response.error_code === 400
                    && userError.response.description
                    && userError.response.description.includes('wrong file identifier');
                  if (isWrongFileIdError) {
                    logger.warning(`重新发送作品 ${aweme_id} 给用户 ${chatId} 再次遇到 400 错误，将保持 failed 状态`);
                    resendSuccess = false;
                  } else {
                    resendSuccess = false;
                  }
                }
              }
              
              if (resendSuccess) {
                logger.info(`尝试清理作品 ${aweme_id} 的 failed 字段`);
                try {
                  const { error: updateError } = await supabase
                    .from("douyin")
                    .update({ failed: null })
                    .eq("aweme_id", aweme_id);
                  if (updateError) {
                    logger.error(`清理作品 ${aweme_id} 的failed字段失败: ${updateError.message}`, updateError);
                  } else {
                    logger.info(`成功清理作品 ${aweme_id} 的failed字段`);
                  }
                } catch (clearError) {
                  logger.error(`清理作品 ${aweme_id} 的failed字段时发生异常: ${clearError.message}`, clearError);
                }
              } else {
                logger.info(`重新发送作品 ${aweme_id} 未完全成功或再次遇到错误，保持 failed 状态为 'file id done'`);
              }
            }
            else if (fileIdChanged 
                  && !(newFailed?.startsWith("file id pending:")) 
                  && newFailed !== "file id done") {
              const file_id = newData.file_id;
              logger.info(`[Realtime Condition Met] file_id changed for ${aweme_id} (failed is '${newFailed}'). Triggering standard update.`);

              const { data: workFullData, error: workError } = await supabase
                .from("douyin")
                .select("*")
                .eq("aweme_id", aweme_id)
                .limit(1);

              if (workError || !workFullData || workFullData.length === 0) {
                logger.error(`获取作品 ${aweme_id} 的完整信息失败: ${workError?.message || "无数据"}`, workError);
                return;
              }

              const work = workFullData[0];
              const uid = work.uid;
              const actualLargeString = work.large; 
              
              let targetChatIds = [];
              let sendToAllSubscribers = true;
              let clearLargeField = false;
  
              if (actualLargeString) {
                logger.info(`作品 ${aweme_id} large字段有内容: "${actualLargeString}", 将仅发送给这些用户。`);
                targetChatIds = actualLargeString.split(';')
                  .map(id => id.trim()).filter(id => id)
                  .map(id => parseInt(id)).filter(id => !isNaN(id));
  
                if (targetChatIds.length > 0) {
                  sendToAllSubscribers = false;
                  clearLargeField = true;
                  logger.info(`作品 ${aweme_id} 将发送给 ${targetChatIds.length} 个指定用户: ${targetChatIds.join(', ')}`);
                } else {
                  logger.warning(`作品 ${aweme_id} large字段 "${actualLargeString}" 解析后为空或无效, 将按正常订阅逻辑发送。`);
                  sendToAllSubscribers = true;
                }
              } else {
                logger.info(`作品 ${aweme_id} large字段为空，将按正常订阅逻辑发送。`);
              }
              
              if (sendToAllSubscribers) {
                if (!uid) {
                  logger.warning(`作品 ${aweme_id} large字段为空，但缺少uid，无法查找订阅者`);
                  return;
                }
                
                const { data: userData, error: userError } = await supabase
                  .from("douyin_user")
                  .select("once, nickname")
                  .eq("uid", uid)
                  .limit(1);
                
                if (userError) {
                  logger.error(`查询作者 ${uid} 的once状态失败: ${userError.message}`, userError);
                  return;
                }
                if (!userData || userData.length === 0 || !userData[0].once) {
                  const nickname = userData?.[0]?.nickname || "未知用户";
                  logger.info(`作者 ${nickname} (${uid}) 的once字段不为true，不发送通知`);
                  return;
                }
                
                const nickname = userData[0].nickname || "未知用户";
                logger.info(`作者 ${nickname} (${uid}) 的once字段为true，开始查找订阅用户`);
                const { data: subscribers, error: subsError } = await supabase
                  .from("telegram_douyin_map")
                  .select("user_id")
                  .eq("uid", uid);
                
                if (subsError) {
                  logger.error(`获取作者 ${uid} 的订阅用户失败: ${subsError.message}`, subsError);
                  return;
                }
                if (!subscribers || subscribers.length === 0) {
                  logger.info(`作者 ${nickname} (${uid}) 没有订阅用户，不处理`);
                  return;
                }
                targetChatIds = subscribers.map(s => s.user_id);
                logger.info(`作者 ${nickname} (${uid}) 有 ${targetChatIds.length} 个订阅用户，开始发送更新...`);
              }
              
              const caption = buildCaptionForSingle({
                base_info: { aweme_id: work.aweme_id, desc: work.description, create_time: work.create_time },
                author_info: { nickname: work.nickname, uid: work.uid, sec_uid: work.sec_uid, unique_id: work.unique_id, follower_count: work.follower_count, total_favorited: work.total_favorited },
                location_info: { province: work.province, city: work.city },
                music_info: { title: work.music_title, author: work.music_author, play_url: work.music_play_url }
              });
              const fileIdsToSend = work.file_id ? work.file_id.split(';').filter(id => id) : [];
              if (fileIdsToSend.length === 0) {
                logger.warning(`作品 ${aweme_id} 的file_id为空或无效，不发送`);
                return;
              }
              
              let zipId = null;
              let audioFileId = null;
              for (const id of fileIdsToSend) {
                if (detectFileType(id) === 'document') zipId = id;
                if (detectFileType(id) === 'audio') audioFileId = id;
              }
              
              const chunkArray = (array, size) => {
                const result = [];
                for (let i = 0; i < array.length; i += size) {
                  result.push(array.slice(i, i + size));
                }
                return result;
              };
              
              for (const chatId of targetChatIds) {
                if (chatId === STORAGE_CHANNEL_ID) {
                  logger.info(`跳过向 STORAGE_CHANNEL_ID (${STORAGE_CHANNEL_ID}) 发送作品 ${aweme_id}`);
                  continue;
                }
                try {
                  const buttons = [];
                  buttons.push(Markup.button.url("打开", `https://www.douyin.com/video/${work.aweme_id}`));
                  if (audioFileId) buttons.push(Markup.button.callback("获取音乐", `music:${work.aweme_id}`));
                  if (zipId) buttons.push(Markup.button.callback("获取文件", `zip:${work.aweme_id}`));
                  
                  // 检查用户是否已订阅该作者
                  let isSubscribed = false;
                  try {
                    if (work.uid && chatId) {
                      isSubscribed = await isUserSubscribedToAuthor(chatId, work.uid);
                    }
                  } catch (error) {
                    logger.error(`Realtime resend: 检查订阅状态出错 (uid: ${work.uid}): ${error.message}`, error);
                  }
                  
                  // 根据订阅状态添加按钮
                  if (work.uid) { // If author UID is available
                    if (isSubscribed) {
                      // 已订阅，显示取消订阅按钮
                      buttons.push(Markup.button.callback("取消订阅", `unsub:${work.uid}`));
                    } else {
                      // 未订阅，显示订阅按钮, using actual UID
                      buttons.push(Markup.button.callback("订阅", `sub:${work.uid}`));
                    }
                  }
                  
                  const markup = Markup.inlineKeyboard(chunkArray(buttons, 4));
                  
                  await sendMediaByFileIds(
                    { 
                      telegram: bot.telegram, 
                      chat: { id: chatId }, 
                      reply: (text, extra) => bot.telegram.sendMessage(chatId, text, extra) 
                    },
                    fileIdsToSend.filter(id => id !== zipId),
                    caption,
                    markup,
                    aweme_id
                  );
                  logger.info(`已向用户 ${chatId} 发送作品 ${aweme_id} 的更新`);
                } catch (userError) {
                  logger.error(`向用户 ${chatId} 发送作品 ${aweme_id} 失败: ${userError.message}`, userError);
                  const isWrongFileIdError = userError.response
                    && userError.response.error_code === 400
                    && userError.response.description
                    && userError.response.description.includes('wrong file identifier');
                  if (isWrongFileIdError) {
                      await markWorkAsFailed(aweme_id, chatId);
                  }
                }
              }
              
              if (clearLargeField) {
                logger.info(`尝试清理作品 ${aweme_id} 的 large 字段`);
                try {
                  const { error: updateError } = await supabase
                    .from("douyin")
                    .update({ large: null })
                    .eq("aweme_id", aweme_id);
                  if (updateError) {
                    logger.error(`清理作品 ${aweme_id} 的large字段失败: ${updateError.message}`, updateError);
                  } else {
                    logger.info(`成功清理作品 ${aweme_id} 的large字段`);
                  }
                } catch (clearError) {
                  logger.error(`清理作品 ${aweme_id} 的large字段时发生异常: ${clearError.message}`, clearError);
                }
              }
            } else {
              logger.info(`[Realtime Update Ignored] aweme_id: ${aweme_id}. No relevant change detected.`);
            }
          } catch (error) {
            logger.error(`处理file_id变化事件出错: ${error.message}`, error);
          }
        }
      )
      .subscribe();

    logger.info("Realtime监听初始化成功");
    return channel;
  } catch (error) {
    logger.error(`初始化Realtime监听失败: ${error.message}`, error);
    return null;
  }
}

// =============== 创建 Bot 实例 ===============
const bot = new Telegraf(process.env.BUTHISBOT);

// =============== 命令处理 ===============
bot.command('start', async (ctx) => {
  if (ctx.chat?.id === STORAGE_CHANNEL_ID) { return; }
  const chatId = ctx.chat.id;
  const username = ctx.from.username || "";
  const userRecord = await getOrCreateUser(chatId, username);

  const welcomeText = 
    `👋 你好！这是抖音下载Bot。\n` +
    `你的 Chat ID: <code>${chatId}</code>\n` +
    `当前数据库记录的用户名: @${userRecord.username || ''}`;
    
  await ctx.reply(welcomeText, { parse_mode: 'HTML' });
});

bot.command('help', async (ctx) => {
  if (ctx.chat?.id === STORAGE_CHANNEL_ID) { return; }
  const chatId = ctx.chat.id;
  const userRecord = await getOrCreateUser(chatId, ctx.from.username || "");
  
  const sendAny = userRecord?.send_any ?? true;
  if (!sendAny) {
    return;
  }

  const helpText = 
    "【功能说明】\n" +
    "- 获取音乐：并非所有作品都提供音乐获取。\n" +
    "- 获取文件：将该作品所有媒体文件打包，避免telegram二次压缩图片文件。\n" +
    "- 订阅功能：抖音作者删除作品是很常见的事，并且随着时间画质会逐渐变差。点击订阅后，程序将在后台值守被订阅作者公开的新作品，我们的数据库将尽最大可能及时保留下作品内容。";

  await ctx.reply(helpText);
});

// 查看当前订阅
bot.command('subscriptions', async (ctx) => {
  if (ctx.chat?.id === STORAGE_CHANNEL_ID) { return; }
  const chatId = ctx.chat.id;
  const username = ctx.from.username || null;
  
  try {
    const userRecord = await getOrCreateUser(chatId, username);
    const { count, error: countError } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("user_id", chatId);
    
    if (countError) {
      logger.error(`获取订阅数量失败: ${countError.message}`, countError);
      await ctx.reply("获取订阅列表时发生错误，请稍后再试。");
      return;
    }
    
    if (count === 0) {
      await ctx.reply("你当前没有订阅任何抖音创作者。\n\n可以通过点击视频下方的「订阅」按钮来订阅创作者。");
      return;
    }
    
    const limit = 10;
    const page = 1;
    
    const { data: subscriptions, error } = await supabase
      .from("telegram_douyin_map")
      .select(`
        uid,
        douyin_user!inner(
          nickname,
          unique_id,
          avatar_thumb_url,
          follower_count,
          total_favorited
        )
      `)
      .eq("user_id", chatId)
      .range((page - 1) * limit, page * limit - 1);
    
    if (error) {
      logger.error(`获取订阅列表失败: ${error.message}`, error);
      await ctx.reply("获取订阅列表时发生错误，请稍后再试。");
      return;
    }
    
    if (!subscriptions || subscriptions.length === 0) {
      await ctx.reply("你当前没有订阅任何抖音创作者。\n\n可以通过点击视频下方的「订阅」按钮来订阅创作者。");
      return;
    }
    
    const buttons = [];
    let message = `📋 <b>你的订阅列表 (${count} 个订阅)</b>`;
    if (count > limit) {
      message += ` - 显示 ${Math.min(limit, subscriptions.length)}/${count}\n`;
    }
    message += "\n\n";
    
    for (let i = 0; i < subscriptions.length; i++) {
      const sub = subscriptions[i];
      const creator = sub.douyin_user;
      const uid = sub.uid;
      
      message += `<b>${(page - 1) * limit + i + 1}. ${creator.nickname || "未知用户"}</b>\n`;
      if (creator.unique_id) {
        message += `抖音号: ${creator.unique_id}\n`;
      }
      if (creator.follower_count) {
        message += `粉丝数: ${numberWithCommas(creator.follower_count)}\n`;
      }
      if (creator.total_favorited) {
        message += `获赞数: ${numberWithCommas(creator.total_favorited)}\n`;
      }
      message += "\n";
      
      buttons.push([
        Markup.button.callback(`🗑️ 取消订阅: ${creator.nickname || uid}`, `unsub:${uid}`)
      ]);
    }
    buttons.push([
      Markup.button.callback("🗑️ 取消所有订阅", "unsub_all")
    ]);
    
    if (count > limit) {
      const pageButtons = [];
      if (page > 1) {
        pageButtons.push(Markup.button.callback("◀️ 上一页", `subs_page:${page - 1}`));
      }
      if (page * limit < count) {
        pageButtons.push(Markup.button.callback("▶️ 下一页", `subs_page:${page + 1}`));
      }
      if (pageButtons.length > 0) {
        buttons.push(pageButtons);
      }
    }
    
    const markup = Markup.inlineKeyboard(buttons);
    await ctx.reply(message, {
      parse_mode: 'HTML',
      ...markup
    });
  } catch (error) {
    logger.error(`处理订阅列表命令出错: ${error.message}`, error);
    await ctx.reply("获取订阅列表时发生错误，请稍后再试。");
  }
});

bot.action(/^subs_page:(\d+)$/, async (ctx) => {
  const chatId = ctx.chat.id;
  const page = parseInt(ctx.match[1]);
  
  if (isNaN(page) || page < 1) {
    await ctx.answerCbQuery("无效的页码");
    return;
  }
  
  try {
    const { count, error: countError } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("user_id", chatId);
    
    if (countError) {
      await ctx.answerCbQuery("获取订阅数量失败");
      return;
    }
    
    const limit = 10;
    if ((page - 1) * limit >= count) {
      await ctx.answerCbQuery("页码超出范围");
      return;
    }
    
    const { data: subscriptions, error } = await supabase
      .from("telegram_douyin_map")
      .select(`
        uid,
        douyin_user!inner(
          nickname,
          unique_id,
          avatar_thumb_url,
          follower_count,
          total_favorited
        )
      `)
      .eq("user_id", chatId)
      .range((page - 1) * limit, page * limit - 1);
    
    if (error) {
      await ctx.answerCbQuery("获取订阅列表失败");
      return;
    }
    
    const buttons = [];
    let message = `📋 <b>你的订阅列表 (${count} 个订阅)</b>`;
    if (count > limit) {
      message += ` - 显示 ${(page - 1) * limit + 1}-${Math.min(page * limit, count)}/${count}\n`;
    }
    message += "\n\n";
    
    for (let i = 0; i < subscriptions.length; i++) {
      const sub = subscriptions[i];
      const creator = sub.douyin_user;
      const uid = sub.uid;
      
      message += `<b>${(page - 1) * limit + i + 1}. ${creator.nickname || "未知用户"}</b>\n`;
      if (creator.unique_id) {
        message += `抖音号: ${creator.unique_id}\n`;
      }
      if (creator.follower_count) {
        message += `粉丝数: ${numberWithCommas(creator.follower_count)}\n`;
      }
      if (creator.total_favorited) {
        message += `获赞数: ${numberWithCommas(creator.total_favorited)}\n`;
      }
      message += "\n";

      buttons.push([
        Markup.button.callback(`🗑️ 取消订阅: ${creator.nickname || uid}`, `unsub:${uid}`)
      ]);
    }
    buttons.push([
      Markup.button.callback("🗑️ 取消所有订阅", "unsub_all")
    ]);
    
    if (count > limit) {
      const pageButtons = [];
      if (page > 1) {
        pageButtons.push(Markup.button.callback("◀️ 上一页", `subs_page:${page - 1}`));
      }
      if (page * limit < count) {
        pageButtons.push(Markup.button.callback("▶️ 下一页", `subs_page:${page + 1}`));
      }
      if (pageButtons.length > 0) {
        buttons.push(pageButtons);
      }
    }
    
    const markup = Markup.inlineKeyboard(buttons);
    await ctx.editMessageText(message, {
      parse_mode: 'HTML',
      ...markup
    });
    
    await ctx.answerCbQuery();
  } catch (error) {
    logger.error(`处理翻页请求出错: ${error.message}`, error);
    await ctx.answerCbQuery("处理请求失败，请稍后重试");
  }
});

function numberWithCommas(x) {
  return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// 添加检查用户是否已订阅某作者的函数
async function isUserSubscribedToAuthor(chatId, uid) {
  try {
    if (!chatId || !uid) {
      return false;
    }
    
    const { count, error } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("user_id", chatId)
      .eq("uid", uid)
      .limit(1);
    
    if (error) {
      logger.error(`检查用户 ${chatId} 是否订阅作者 ${uid} 失败: ${error.message}`, error);
      return false;
    }
    
    return count > 0;
  } catch (error) {
    logger.error(`检查订阅状态异常: ${error.message}`, error);
    return false;
  }
}

bot.action(/^unsub:(.+)$/, async (ctx) => {
  const uid = ctx.match[1];
  const chatId = ctx.chat.id;
  
  try {
    const { data, error } = await supabase
      .from("douyin_user")
      .select("nickname, unique_id")
      .eq("uid", uid)
      .limit(1);
    
    if (error) {
      await ctx.answerCbQuery("获取创作者信息失败");
      return;
    }
    
    const creator = data && data.length > 0 ? data[0] : { nickname: "未知用户", unique_id: "" };
    const message = `确定要取消订阅 <b>${creator.nickname || uid}</b> 吗？`;
    const buttons = [
      [
        Markup.button.callback("✅ 确定取消", `confirm_unsub:${uid}`),
        Markup.button.callback("❌ 取消操作", "cancel_unsub")
      ]
    ];
    
    const markup = Markup.inlineKeyboard(buttons);
    
    await ctx.editMessageText(message, {
      parse_mode: 'HTML',
      ...markup
    });
    
    await ctx.answerCbQuery();
  } catch (error) {
    logger.error(`处理取消订阅请求出错: ${error.message}`, error);
    await ctx.answerCbQuery("处理请求失败，请稍后重试");
  }
});

bot.action(/^confirm_unsub:(.+)$/, async (ctx) => {
  const uid = ctx.match[1];
  const chatId = ctx.chat.id;
  
  try {
    const { data: userData, error: userError } = await supabase
      .from("douyin_user")
      .select("nickname, unique_id")
      .eq("uid", uid)
      .limit(1);
    
    const creator = userData && userData.length > 0 ? userData[0] : { nickname: "未知用户", unique_id: "" };
    
    const { error } = await supabase
      .from("telegram_douyin_map")
      .delete()
      .eq("user_id", chatId)
      .eq("uid", uid);
    
    if (error) {
      await ctx.answerCbQuery("取消订阅失败，请稍后重试", { show_alert: true });
      return;
    }
    
    const { count, error: countError } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("uid", uid);
    
    if (!countError && count === 0) {
      await supabase
        .from("douyin_user")
        .update({ surveillance: false })
        .eq("uid", uid);
    }
    
    await ctx.editMessageText(
      `✅ 已成功取消订阅 <b>${creator.nickname || uid}</b>！\n\n使用 /subscriptions 查看你的订阅列表。`,
      { parse_mode: 'HTML' }
    );
    await ctx.answerCbQuery("已成功取消订阅", { show_alert: true });
  } catch (error) {
    logger.error(`确认取消订阅出错: ${error.message}`, error);
    await ctx.answerCbQuery("处理请求失败，请稍后重试", { show_alert: true });
  }
});

bot.action('cancel_unsub', async (ctx) => {
  await ctx.answerCbQuery("已取消操作", { show_alert: true });
  await ctx.editMessageText("操作已取消，订阅保持不变。请使用 /subscriptions 查看订阅列表。");
});

bot.action('unsub_all', async (ctx) => {
  const chatId = ctx.chat.id;
  
  try {
    const { count, error: countError } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("user_id", chatId);
    
    if (countError) {
      await ctx.answerCbQuery("获取订阅数量失败");
      return;
    }
    
    if (count === 0) {
      await ctx.answerCbQuery("你当前没有订阅任何创作者", { show_alert: true });
      return;
    }
    
    const message = `⚠️ <b>确定要取消所有 ${count} 个订阅吗？</b>\n\n此操作无法撤销，你将不再收到这些创作者的作品更新。`;
    const buttons = [
      [
        Markup.button.callback("✅ 确定取消所有", "confirm_unsub_all"),
        Markup.button.callback("❌ 取消操作", "cancel_unsub")
      ]
    ];
    
    const markup = Markup.inlineKeyboard(buttons);
    
    await ctx.editMessageText(message, {
      parse_mode: 'HTML',
      ...markup
    });
    
    await ctx.answerCbQuery();
  } catch (error) {
    logger.error(`处理取消所有订阅请求出错: ${error.message}`, error);
    await ctx.answerCbQuery("处理请求失败，请稍后重试");
  }
});

bot.action('confirm_unsub_all', async (ctx) => {
  const chatId = ctx.chat.id;
  
  try {
    const { data: subscriptions, error: getError } = await supabase
      .from("telegram_douyin_map")
      .select("uid")
      .eq("user_id", chatId);
    
    if (getError) {
      await ctx.answerCbQuery("获取订阅信息失败，请稍后重试", { show_alert: true });
      return;
    }
    
    const { error } = await supabase
      .from("telegram_douyin_map")
      .delete()
      .eq("user_id", chatId);
    
    if (error) {
      await ctx.answerCbQuery("取消订阅失败，请稍后重试", { show_alert: true });
      return;
    }
    
    if (subscriptions && subscriptions.length > 0) {
      for (const sub of subscriptions) {
        const { count, error: countError } = await supabase
          .from("telegram_douyin_map")
          .select("*", { count: 'exact', head: true })
          .eq("uid", sub.uid);
        
        if (!countError && count === 0) {
          await supabase
            .from("douyin_user")
            .update({ surveillance: false })
            .eq("uid", sub.uid);
        }
      }
    }
    
    await ctx.editMessageText(
      `✅ 已成功取消所有订阅！\n\n你不会再收到这些创作者的作品更新。`,
      { parse_mode: 'HTML' }
    );
    
    await ctx.answerCbQuery("已成功取消所有订阅", { show_alert: true });
  } catch (error) {
    logger.error(`确认取消所有订阅出错: ${error.message}`, error);
    await ctx.answerCbQuery("处理请求失败，请稍后重试", { show_alert: true });
  }
});

// =============== 回调按钮处理 ===============
bot.on('callback_query', async (ctx) => {
  if (ctx.chat?.id === STORAGE_CHANNEL_ID) {
    logger.info(`忽略来自 STORAGE_CHANNEL_ID (${STORAGE_CHANNEL_ID}) 的回调: ${ctx.callbackQuery.data}`);
    await ctx.answerCbQuery();
    return;
  }

  const chatId = ctx.chat.id;
  const fromUsername = ctx.from.username || "";
  const userRecord = await getOrCreateUser(chatId, fromUsername);

  const sendAny = userRecord.send_any !== undefined ? userRecord.send_any : true;
  const todayLimit = userRecord.today || 10;
  const alreadyUsed = userRecord.already || 0;

  if (!sendAny || alreadyUsed >= todayLimit) {
    await ctx.answerCbQuery();
    const replyMsg = await ctx.reply("你已达到每日配额上限，使用量将在下一个23:00清空。");
    setTimeout(async () => {
      try { await ctx.telegram.deleteMessage(ctx.chat.id, replyMsg.message_id); } catch (e) {}
    }, 10000);
    return;
  }

  const data = ctx.callbackQuery.data;

  // ------ 获取更多作者作品 (原 "author:") 省略。若需要可再加 ------

  // ------ 下载音乐 ------
  if (data.startsWith("music:")) {
    const awemeId = data.substring(6);
    if (!awemeId) {
      await ctx.answerCbQuery("无效的作品ID", { show_alert: true });
      return;
    }

    if (!sendAny || alreadyUsed >= todayLimit) {
      await ctx.answerCbQuery();
      const replyMsg = await ctx.reply("你已达到每日配额上限，使用量将在下一个23:00清空。");
      setTimeout(async () => {
        try { await ctx.telegram.deleteMessage(ctx.chat.id, replyMsg.message_id); } catch (e) {}
      }, 10000);
      return;
    }

    await ctx.answerCbQuery("正在查找音乐...");

    try {
      const { data: workData, error: dbError } = await supabase
        .from("douyin")
        .select("file_id")
        .eq("aweme_id", awemeId)
        .limit(1);

      if (dbError) {
        logger.error(`回调查询作品 ${awemeId} 的 file_id 失败: ${dbError.message}`, dbError);
        await ctx.answerCbQuery("查找音乐信息失败", { show_alert: true });
        return;
      }
      if (!workData || workData.length === 0 || !workData[0].file_id) {
        await ctx.answerCbQuery("未找到该作品的音乐文件信息", { show_alert: true });
        return;
      }

      const fileIds = workData[0].file_id.split(';');
      let audioFileId = null;
      for (const fileId of fileIds) {
        if (detectFileType(fileId) === 'audio') {
          audioFileId = fileId;
          break;
        }
      }

      if (!audioFileId) {
        await ctx.answerCbQuery("无法从记录中找到音乐文件", { show_alert: true });
        logger.warning(`用户 ${chatId} 请求作品 ${awemeId} 的音乐，但在数据库记录中未找到 audio file_id: ${workData[0].file_id}`);
        return;
      }

      await ctx.replyWithAudio(audioFileId);
      await ctx.answerCbQuery("音乐已发送");
      await updateUser(chatId, { already: alreadyUsed + 1 });
      logger.info(`用户 ${chatId} 点击按钮获取了作品 ${awemeId} 的音乐 (file_id: ${audioFileId})`);
    } catch (error) {
      logger.error(`发送音乐 (awemeId: ${awemeId}, 从DB获取) 失败: ${error.message}`, error);
      try {
        await ctx.reply("抱歉，发送音乐时遇到问题，请稍后再试。");
      } catch (replyError) {
        logger.error(`无法向用户 ${chatId} 发送音乐错误通知: ${replyError.message}`);
      }
    }
  }
  // ------ 订阅作者 ------
  else if (data.startsWith("sub:")) {
    const uidToSubscribe = data.substring(4); // Directly get UID

    if (!uidToSubscribe) {
      await ctx.answerCbQuery("无效的作者UID", { show_alert: true });
      return;
    }
    
    // User quota and send_any check (remains the same)
    if (!sendAny || alreadyUsed >= todayLimit) {
      await updateUser(chatId, { send_any: false }); // Ensure send_any is updated if limit is hit
      await ctx.answerCbQuery("你已达到每日配额上限。", { show_alert: true });
      const replyMsg = await ctx.reply("你已达到每日配额上限，使用量将在下一个23:00清空。");
      setTimeout(async () => {
        try { await ctx.telegram.deleteMessage(ctx.chat.id, replyMsg.message_id); } catch (e) {}
      }, 10000);
      return;
    }
    
    // Subscription limit check for non-premium users (remains the same)
    const isPremium = isPremiumUser(userRecord);
    if (!isPremium) {
      const isAllowedToSubscribe = await checkSubscriptionLimit(chatId, ctx);
      if (!isAllowedToSubscribe) {
        await ctx.answerCbQuery(); // answerCbQuery was missing if checkSubscriptionLimit sent a message and returned false
        return;
      }
    }
    
    try {
      // Fetch author details from douyin_user using UID
      const { data: authorDataArray, error: authorQueryError } = await supabase
        .from("douyin_user")
        .select("*") // Select all needed fields, esp. nickname, unique_id, sec_uid
        .eq("uid", uidToSubscribe)
        .limit(1);
      
      if (authorQueryError) {
        logger.error(`订阅回调：查询作者信息失败 (uid: ${uidToSubscribe}): ${authorQueryError.message}`, authorQueryError);
        await ctx.answerCbQuery("查询作者信息时出错，请稍后再试。", { show_alert: true });
        return;
      }
      
      if (!authorDataArray || authorDataArray.length === 0) {
        logger.warning(`订阅回调：数据库中未找到作者信息 (uid: ${uidToSubscribe})。可能需要用户通过作品链接重新触发作者信息保存。`);
        await ctx.answerCbQuery("订阅失败：作者信息不完整或未在库中找到。请尝试通过该作者的某一个作品链接再次操作，或稍后再试。", { show_alert: true });
        return;
      }
      
      const author = authorDataArray[0];
      // Ensure sec_uid is present if any subsequent logic strictly needs it, though primary operations now use UID.
      if (!author.sec_uid) {
          logger.warning(`订阅回调：作者信息 (uid: ${uidToSubscribe}) 缺少 sec_uid。某些操作可能受限。`);
          // Depending on strictness, might alert user or proceed if sec_uid is not critical for remaining steps.
      }
      
      // Check if already subscribed (uses uidToSubscribe)
      const { data: existingMap, error: checkError } = await supabase
        .from("telegram_douyin_map")
        .select("*")
        .eq("user_id", chatId)
        .eq("uid", uidToSubscribe)
        .limit(1);
        
      if (checkError) {
        logger.error(`订阅回调：检查订阅映射失败 (uid: ${uidToSubscribe}): ${checkError.message}`, checkError);
        await ctx.answerCbQuery("检查订阅状态时出错。", { show_alert: true });
        return;
      }
      
      if (existingMap && existingMap.length > 0) {
        // 如果已有记录但 douyin 字段未开启，则更新
        const row = existingMap[0];
        if (!row.douyin) {
          await supabase.from("telegram_douyin_map")
            .update({ douyin: true })
            .eq("user_id", chatId)
            .eq("uid", uidToSubscribe);
        } else {
          await ctx.answerCbQuery(`你已经订阅了该作者: ${author.nickname || author.unique_id || uidToSubscribe}`, { show_alert: true });
          return;
        }
      }

      // Mark surveillance in douyin_user table (using UID)
      const { error: updateError } = await supabase
        .from("douyin_user")
        .update({ surveillance: true })
        .eq("uid", uidToSubscribe); // Changed from sec_uid

      if (updateError) {
        logger.error(`订阅回调：更新作者surveillance字段失败 (uid: ${uidToSubscribe}): ${updateError.message}`, updateError);
      }
      
      if (!(existingMap && existingMap.length > 0)) {
        // 新增行
        const { error: mapError } = await supabase
          .from("telegram_douyin_map")
          .insert([{ user_id: chatId, uid: uidToSubscribe, douyin: true }]);

        if (mapError) {
          logger.error(`订阅回调：创建用户订阅映射失败 (uid: ${uidToSubscribe}): ${mapError.message}`, mapError);
          await ctx.answerCbQuery(`订阅关系建立失败: ${author.nickname || author.unique_id || uidToSubscribe}`, { show_alert: true });
          return;
        }
      }
      
      logger.info(`已创建用户 ${chatId} 订阅作者 ${uidToSubscribe} (${author.nickname || ""}) 的映射`);
      await ctx.answerCbQuery(`成功订阅作者: ${author.nickname || author.unique_id || uidToSubscribe}`, { show_alert: true });
      await updateUser(chatId, { already: alreadyUsed + 1 }); // Increment usage only on successful subscription completion
    } catch (error) {
      logger.error(`订阅作者 (uid: ${uidToSubscribe}) 出错: ${error.message}`, error);
      await ctx.answerCbQuery("订阅时发生未知错误。", { show_alert: true });
    }
  }
  // ------ 获取ZIP文件 ------
  else if (data.startsWith("zip:")) {
    if (!sendAny || alreadyUsed >= todayLimit) {
      await ctx.answerCbQuery();
      const replyMsg = await ctx.reply("你已达到每日配额上限，使用量将在下一个23:00清空。");
      setTimeout(async () => {
        try { await ctx.telegram.deleteMessage(ctx.chat.id, replyMsg.message_id); } catch (e) {}
      }, 10000);
      return;
    }
    
    const awemeId = data.substring(4);
    if (!awemeId) {
      await ctx.answerCbQuery("无效的作品ID", { show_alert: true });
      return;
    }

    try {
      const { data: workData, error: dbError } = await supabase
        .from("douyin")
        .select("file_id")
        .eq("aweme_id", awemeId)
        .limit(1);
      
      if (dbError) {
        logger.error(`回调查询作品 ${awemeId} 的 file_id 失败: ${dbError.message}`, dbError);
        await ctx.answerCbQuery("查找ZIP文件信息失败", { show_alert: true });
        return;
      }
      if (!workData || workData.length === 0 || !workData[0].file_id) {
        await ctx.answerCbQuery("未找到该作品的ZIP文件信息", { show_alert: true });
        return;
      }
      
      const fileIds = workData[0].file_id.split(';');
      let zipFileId = null;
      for (const fileId of fileIds) {
        if (fileId && detectFileType(fileId) === 'document') {
          zipFileId = fileId;
          break;
        }
      }
      
      if (!zipFileId) {
        await ctx.answerCbQuery("无法从记录中找到ZIP文件", { show_alert: true });
        logger.warning(`用户 ${chatId} 请求作品 ${awemeId} 的ZIP，但未找到 document file_id: ${workData[0].file_id}`);
        return;
      }
      
      await ctx.answerCbQuery("正在发送ZIP文件...");
      await ctx.replyWithDocument(zipFileId);
      logger.info(`用户 ${chatId} 点击按钮获取了作品 ${awemeId} 的ZIP (file_id: ${zipFileId})`);
      
      await updateUser(chatId, { already: alreadyUsed + 1 });
    } catch (error) {
      logger.error(`发送ZIP文件 (awemeId: ${awemeId}, 从DB获取) 失败: ${error.message}`, error);
      try {
        await ctx.reply("抱歉，发送ZIP文件时遇到问题，请稍后再试。");
      } catch (replyError) {
        logger.error(`无法向用户 ${chatId} 发送ZIP错误通知: ${replyError.message}`);
      }
    }
  }
  else if (data.startsWith("sub_live:")) {
    const uidLive = data.substring(9);

    if (!uidLive) {
      await ctx.answerCbQuery("无效的作者UID", { show_alert: true });
      return;
    }

    try {
      // 更新 douyin_user.record = true
      await supabase.from("douyin_user").update({ record: true }).eq("uid", uidLive);

      // 检查是否已存在映射
      const { data: existMap, error: existErr } = await supabase
        .from("telegram_douyin_map")
        .select("*")
        .eq("user_id", chatId)
        .eq("uid", uidLive)
        .limit(1);

      if (existErr) {
        logger.error(`检查订阅直播映射失败: ${existErr.message}`, existErr);
        await ctx.answerCbQuery("处理失败，请稍后再试", { show_alert: true });
        return;
      }

      if (existMap && existMap.length > 0) {
        // 更新 live 字段
        await supabase.from("telegram_douyin_map")
          .update({ live: true })
          .eq("user_id", chatId)
          .eq("uid", uidLive);
      } else {
        // 插入新行
        await supabase.from("telegram_douyin_map")
          .insert([{ user_id: chatId, uid: uidLive, live: true }]);
      }

      await ctx.answerCbQuery("已订阅直播", { show_alert: true });
    } catch (err) {
      logger.error(`订阅直播出错: ${err.message}`, err);
      await ctx.answerCbQuery("订阅直播时出错", { show_alert: true });
    }
  }
  else if (data.startsWith("unsub_live:")) {
    const uidLive = data.substring(11);
    if (!uidLive) {
      await ctx.answerCbQuery("无效的作者UID", { show_alert: true });
      return;
    }

    try {
      const { data: mapRows, error: mapErr } = await supabase
        .from("telegram_douyin_map")
        .select("douyin, live")
        .eq("user_id", chatId)
        .eq("uid", uidLive)
        .limit(1);

      if (mapErr) {
        logger.error(`查询映射失败: ${mapErr.message}`, mapErr);
        await ctx.answerCbQuery("处理失败，请稍后再试", { show_alert: true });
        return;
      }

      if (!mapRows || mapRows.length === 0) {
        await ctx.answerCbQuery("尚未订阅直播", { show_alert: true });
        return;
      }

      const row = mapRows[0];
      if (row.douyin) {
        // 仅取消 live
        await supabase.from("telegram_douyin_map")
          .update({ live: false })
          .eq("user_id", chatId)
          .eq("uid", uidLive);
      } else {
        // 如果没有作品订阅，直接删除整行
        await supabase.from("telegram_douyin_map")
          .delete()
          .eq("user_id", chatId)
          .eq("uid", uidLive);
      }

      // 如果该作者没有其他订阅者的 live=true，则把 douyin_user.record 置为 false (可选)
      try {
        const { count } = await supabase
          .from("telegram_douyin_map")
          .select("*", { count: 'exact', head: true })
          .eq("uid", uidLive)
          .eq("live", true);
        if (count === 0) {
          await supabase.from("douyin_user").update({ record: false }).eq("uid", uidLive);
        }
      } catch (innerErr) {}

      await ctx.answerCbQuery("已取消直播订阅", { show_alert: true });
    } catch (err) {
      logger.error(`取消直播订阅出错: ${err.message}`, err);
      await ctx.answerCbQuery("取消订阅时出错", { show_alert: true });
    }
  }
  else {
    await ctx.answerCbQuery("未知操作", { show_alert: true });
  }
});

// =============== 处理文本消息：抖音链接 ===============
bot.on('text', async (ctx) => {
  if (ctx.chat?.id === STORAGE_CHANNEL_ID) {
    logger.info(`忽略来自 STORAGE_CHANNEL_ID (${STORAGE_CHANNEL_ID}) 的文本消息: ${ctx.message.text}`);
    return;
  }

  try {
    if (ctx.message.text.startsWith('/')) {
      return;
    }

    const chatId = ctx.chat.id;
    const fromUsername = ctx.from.username || null;
    const userRecord = await getOrCreateUser(chatId, fromUsername);

    const sendAny = userRecord.send_any !== undefined ? userRecord.send_any : true;
    const todayLimit = userRecord.today || 10;
    const alreadyUsed = userRecord.already || 0;

    if (!sendAny || alreadyUsed >= todayLimit) {
      await updateUser(chatId, { send_any: false });
      const replyMsg = await ctx.reply("你已达到每日配额上限，使用量将在下一个23:00清空。");
      setTimeout(async () => {
        try { await ctx.telegram.deleteMessage(ctx.chat.id, replyMsg.message_id); } catch (e) {}
      }, 10000);
      return;
    }

    const text = ctx.message.text.trim();
    if (!text) {
      return;
    }

    const userUrl = extractDouyinUserUrl(text);
    if (userUrl) {
      // 如果识别到用户主页链接，先检查用户等级
      const isPremium = isPremiumUser(userRecord);
      if (!isPremium) {
        const premiumMsg = '您好，获取全部作品功能已下线，获取premium请联系<a href="https://t.me/juaer">@juaer</a>，premium用户可获得更高配额。';
        await ctx.reply(premiumMsg, { parse_mode: 'HTML' });
        return;
      }
      
      const processingMsg = await ctx.reply("正在处理您的请求，请稍候...");
      const secUserId = extractSecUserIdFromUserUrl(userUrl);
      if (!secUserId) {
        await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "无法从链接中提取sec_user_id");
        return;
      }

      try {
        // 基于作者主页链接的批量抓取逻辑（仅限 premium）
        // 从数据库先查一次
        const { data: userData, error: userError } = await supabase
          .from("douyin_user")
          .select("*")
          .eq("sec_uid", secUserId)
          .limit(1);

        if (userError) {
          logger.error(`查询作者信息失败: ${userError.message}`, userError);
          await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "获取作者信息失败，请稍后再试");
          return;
        }

        let author;
        if (!userData || userData.length === 0) {
          // 如果没有找到作者信息，则尝试获取一个作品
          try {
            const respJson = await fetchUserPostVideosApi(secUserId, 0, 1);
            const data = respJson?.data || {};
            const awemeList = data.aweme_list || [];
            
            if (awemeList.length > 0) {
              const firstVideo = await fetchOneVideoApi(awemeList[0].aweme_id);
              const authorData = parseDouyinUser(firstVideo);
              if (authorData) {
                await saveDouyinUserToDatabase(authorData);
                author = authorData;
              } else {
                await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "无法获取作者信息，请稍后再试。");
                return;
              }
            } else {
              await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "未找到该作者的任何作品。");
              return;
            }
          } catch (error) {
            logger.error(`获取作者信息失败: ${error.message}`, error);
            await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "处理请求时出错，请稍后再试。");
            return;
          }
        } else {
          author = userData[0];
        }

        const uid = author?.uid;
        const nickname = author?.nickname || "未知用户";
        
        if (!uid) {
          await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "作者信息不完整，无法处理请求。");
          return;
        }
        
        const { data: existingMap, error: checkError } = await supabase
          .from("telegram_douyin_map")
          .select("*")
          .eq("user_id", chatId)
          .eq("uid", uid)
          .limit(1);
        
        if (checkError) {
          logger.error(`检查映射关系失败: ${checkError.message}`, checkError);
          await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "检查请求状态失败，请稍后再试");
          return;
        }

        const { error: updateError } = await supabase
          .from("douyin_user")
          .update({ surveillance: true })
          .eq("sec_uid", secUserId);
        
        if (updateError) {
          logger.error(`更新作者surveillance字段失败: ${updateError.message}`, updateError);
          await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "处理请求时出错，请稍后再试");
          return;
        }

        if (!existingMap || existingMap.length === 0) {
          const { error: mapError } = await supabase
            .from("telegram_douyin_map")
            .insert([{ user_id: chatId, uid: uid }]);
              
          if (mapError) {
            logger.error(`创建用户映射关系失败: ${mapError.message}`, mapError);
            await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "处理请求时出错，请稍后再试");
            return;
          }
        }

        await ctx.telegram.editMessageText(
          chatId, 
          processingMsg.message_id, 
          null,
          `已记录您获取 <b>${nickname}</b> 全部作品的请求。\n\n` +
          `系统将在后台抓取该作者的所有作品，完成后会自动发送给您。\n\n` +
          `请耐心等待，无需重复操作。`,
          { parse_mode: 'HTML' }
        );

        await updateUser(chatId, { already: alreadyUsed + 1 });
        logger.info(`用户 ${chatId} 请求获取作者 ${uid} (${nickname}) 的全部作品，已设置监听`);
      } catch (error) {
        logger.error(`处理获取全部作品请求出错: ${error.message}`, error);
        await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "处理请求时发生错误，请稍后再试");
      }
      return;
    }

    // 单作品链接
    const douyinUrl = extractDouyinVideoUrl(text);
    if (douyinUrl) {
      const processingMsg = await ctx.reply("正在处理...");
      let awemeId = null; // 将 awemeId 的声明提到 try 外部

      try {
        // 先尝试从URL直接解析 awemeId
        let isStandardVideoUrl = false;
        
        const videoIdMatch = douyinUrl.match(/\/video\/(\d+)/);
        const noteIdMatch = douyinUrl.match(/\/note\/(\d+)/);
        const lvDetailMatch = douyinUrl.match(/\/lvdetail\/(\d+)/);
        
        if (videoIdMatch && videoIdMatch[1]) {
          awemeId = videoIdMatch[1];
          isStandardVideoUrl = true;
          logger.info(`从标准视频链接直接提取 aweme_id: ${awemeId}`);
        } else if (noteIdMatch && noteIdMatch[1]) {
          awemeId = noteIdMatch[1];
          isStandardVideoUrl = true;
          logger.info(`从标准笔记链接直接提取 aweme_id: ${awemeId}`);
        } else if (lvDetailMatch && lvDetailMatch[1]) {
          awemeId = lvDetailMatch[1];
          isStandardVideoUrl = true;
          logger.info(`从长视频链接直接提取 ID: ${awemeId}`);
        }

        let existingWorkData = null;
        let isWorkExistInDB = false;
        
        // 如果能提取到awemeId，先查数据库
        if (awemeId) {
          const { data: existingData, error: checkError } = await supabase
            .from("douyin")
            .select("*, douyin_user(*)")
            .eq("aweme_id", awemeId)
            .limit(1);
          
          if (!checkError && existingData && existingData.length > 0) {
            existingWorkData = existingData[0];
            isWorkExistInDB = true;
            logger.info(`作品 ${awemeId} 已存在于数据库，将直接发送`);
          }
        }

        if (existingWorkData) {
          if (!existingWorkData.is_available === false) {
            await ctx.telegram.editMessageText(
              chatId, 
              processingMsg.message_id, 
              null, 
              `抱歉，该作品 (ID: ${awemeId}) 已被标记为不可用或已被删除。`
            );
            await updateUser(chatId, { already: alreadyUsed + 1 });
            await cleanupMessages(ctx, processingMsg);
            return;
          }
          
          let fileIdsToUseForSend = [];
          let sourceOfFileIdsInfo = ""; // For logging

          if (existingWorkData.high_quality && existingWorkData.high_quality.trim() !== "") {
            logger.info(`作品 ${awemeId} 使用 high_quality 字段: ${existingWorkData.high_quality}`);
            fileIdsToUseForSend = existingWorkData.high_quality.split(';').filter(id => id);
            sourceOfFileIdsInfo = "high_quality";
          } else if (existingWorkData.file_id && existingWorkData.file_id.trim() !== "") {
            logger.info(`作品 ${awemeId} 使用 file_id 字段: ${existingWorkData.file_id}`);
            fileIdsToUseForSend = existingWorkData.file_id.split(';').filter(id => id);
            sourceOfFileIdsInfo = "file_id";
          }

          if (fileIdsToUseForSend.length > 0) {
            logger.info(`作品 ${awemeId} 将使用来自 ${sourceOfFileIdsInfo} 字段的 file_id 列表发送。`);
            const authorData = existingWorkData.douyin_user;
            if (authorData && authorData.sec_uid) {
              // 不再调用任何 fetchUserProfileApi 之类的
            }
            
            const dbParsedData = {
              base_info: {
                aweme_id: existingWorkData.aweme_id,
                desc: existingWorkData.description,
                create_time: existingWorkData.create_time
              },
              author_info: {
                nickname: existingWorkData.nickname,
                uid: existingWorkData.uid,
                sec_uid: existingWorkData.sec_uid,
                unique_id: existingWorkData.unique_id,
                follower_count: existingWorkData.follower_count,
                total_favorited: existingWorkData.total_favorited
              },
              location_info: {
                province: existingWorkData.province,
                city: existingWorkData.city
              },
              music_info: {
                title: existingWorkData.music_title,
                author: existingWorkData.music_author,
                play_url: existingWorkData.music_play_url
              }
            };
            
            // 检查视频时长
            const rawDuration = existingWorkData.duration || 0;
            // 数据库中可能已经是秒，也可能是毫秒，需要智能判断
            let durationSeconds = rawDuration;
            if (rawDuration > 1000 && rawDuration % 1 === 0) { // 如果是整数且大于1000，可能是毫秒
              durationSeconds = rawDuration / 1000;
            }
            
            if (durationSeconds > 600) {
              // 超过10分钟的视频直接标记为failed
              logger.info(`数据库记录检测到超长视频: ${awemeId}, 时长: ${durationSeconds.toFixed(2)}秒 > 10分钟 (原始值: ${rawDuration})，将直接标记为failed`);
              
              const failedReason = "video_too_long:视频时长超过10分钟";
              const { error: failedUpdateError } = await supabase
                .from("douyin")
                .update({ failed: failedReason })
                .eq("aweme_id", awemeId);
              
              if (failedUpdateError) {
                logger.error(`更新超长视频 ${awemeId} 的failed字段失败: ${failedUpdateError.message}`, failedUpdateError);
              } else {
                logger.info(`已将超长视频 ${awemeId} 的failed字段更新为 ${failedReason}`);
              }
              
              await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "视频时长超过10分钟，文件过大无法处理。");
              await updateUser(chatId, { already: alreadyUsed + 1 });
              await cleanupMessages(ctx, processingMsg);
              return;
            }
            else if (durationSeconds > 360) {
              logger.info(`数据库记录检测到长视频: ${awemeId}, 时长: ${durationSeconds.toFixed(2)}秒 > 6分钟 (原始值: ${rawDuration})，将标记为large并跳过下载`);
              await updateLargeField(ctx, awemeId, "视频时长超过6分钟");
              await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "视频时长超过6分钟，后台处理中，请稍后再查看。");
              await updateUser(chatId, { already: alreadyUsed + 1 });
              await cleanupMessages(ctx, processingMsg);
              return;
            }
            
            const captionText = buildCaptionForSingle(dbParsedData);
            const secUid = existingWorkData.sec_uid || "";
            
            const fileIds = fileIdsToUseForSend; // Use the resolved file IDs
            let zipFileId = null;
            let audioFileId = null;
            for (const fileId of fileIds) { // Iterate over resolved file IDs
              if (detectFileType(fileId) === 'document') zipFileId = fileId;
              if (detectFileType(fileId) === 'audio') audioFileId = fileId;
            }

            const buttons = [];
            buttons.push(Markup.button.url("打开", douyinUrl));
            if (audioFileId) buttons.push(Markup.button.callback("获取音乐", `music:${awemeId}`));
            if (zipFileId) buttons.push(Markup.button.callback("获取文件", `zip:${awemeId}`));
            
            // Use UID for subscribe button if available
            if (existingWorkData.uid) {
                // isUserSubscribedToAuthor should be called here if we want to show unsub button
                // For simplicity in this edit, we assume if UID exists, we offer 'sub:UID'
                // A more complete logic would check subscription status first.
                // Let's add the check for consistency.
                let isSubscribed = false;
                try {
                    isSubscribed = await isUserSubscribedToAuthor(chatId, existingWorkData.uid);
                } catch (e) { logger.error("Error checking subscription status in text handler (existing work)", e);}

                if (isSubscribed) {
                    buttons.push(Markup.button.callback("取消订阅", `unsub:${existingWorkData.uid}`));
                } else {
                    buttons.push(Markup.button.callback("订阅", `sub:${existingWorkData.uid}`));
                }
            } else if (secUid) {
                // Fallback or warning if UID is somehow missing but secUid is present.
                // This case should be rare if data is saved correctly.
                logger.warning(`bot.on(text): existingWorkData for ${awemeId} has secUid but no uid. Cannot create sub button.`);
            }
            const markup = Markup.inlineKeyboard(chunkList(buttons, 3));

            await sendMediaByFileIds(ctx, fileIds, captionText, markup, awemeId); // Use resolved fileIds
            await updateUser(chatId, { already: alreadyUsed + 1 });
            await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "处理完成。");
            await cleanupMessages(ctx, processingMsg);
            return; // Important: return after sending
          } else {
            logger.warning(`作品 ${awemeId} 存在于数据库，但 high_quality 和 file_id 均为空或无效。将进行完整API处理。`);
            // No 'return' here, so it falls through to subsequent API call logic.
          }
        }

        // 如果数据库里没有，或者文件ID为空，需要调用接口解析
        let respData;
        if (isStandardVideoUrl && awemeId) {
          // 修改此处的逻辑，不再创建最小化响应，而是强制调用API获取详细信息
          logger.info(`从标准链接提取了 aweme_id: ${awemeId}，调用API获取完整信息`);
          respData = await callHybridVideoData(douyinUrl);
        } else {
          respData = await callHybridVideoData(douyinUrl);
          
          // 检查是否需要静默处理
          if (respData.silent_process) {
            logger.info(`检测到需要静默处理的链接: ${douyinUrl}`);
            
            // 静默删除用户消息，不给任何提示
            try {
              await ctx.telegram.deleteMessage(ctx.chat.id, ctx.message.message_id);
              
              // 如果有处理中的消息，也静默删除
              if (processingMsg && processingMsg.message_id) {
                await ctx.telegram.deleteMessage(ctx.chat.id, processingMsg.message_id);
              }
              
              logger.info(`已静默删除用户 ${chatId} 发送的不支持链接: ${douyinUrl}`);
            } catch (deleteError) {
              logger.error(`静默删除消息失败: ${deleteError.message}`, deleteError);
            }
            
            return;
          }
          
          // 原有的not_available处理逻辑
          if (respData.not_available) {
            const respAwemeId = respData.data?.aweme_id || awemeId;
            let notAvailableMessage = `抱歉，该作品${respAwemeId ? ` (ID: ${respAwemeId})` : ""}不可用或已被删除。\n\n已在系统中标记该作品状态。`;
            
            // 检查是否为长视频
            if (respData.is_long_video) {
              notAvailableMessage = `抱歉，检测到这是一个长视频内容${respAwemeId ? ` (ID: ${respAwemeId})` : ""}，暂不支持下载。\n\n已在系统中记录此作品信息。`;
            }
            
            await ctx.telegram.editMessageText(
              chatId, 
              processingMsg.message_id, 
              null, 
              notAvailableMessage
            );
            await updateUser(chatId, { already: alreadyUsed + 1 });
            await cleanupMessages(ctx, processingMsg);
            return;
          }
        }

        if (respData?.data?.aweme_id) {
          const apiAwemeId = respData.data.aweme_id;
          if (apiAwemeId !== awemeId) {
            const { data: existingApiData, error: apiCheckError } = await supabase
              .from("douyin")
              .select("*")
              .eq("aweme_id", apiAwemeId)
              .limit(1);
            
            if (!apiCheckError && existingApiData && existingApiData.length > 0) {
              isWorkExistInDB = true;
              const existingRecord = existingApiData[0];

              let fileIdsToUseFromApiRecord = [];
              let sourceInfo = "";
              if (existingRecord.high_quality && existingRecord.high_quality.trim() !== "") {
                logger.info(`API解析的作品 ${apiAwemeId} (DB record) 使用 high_quality: ${existingRecord.high_quality}`);
                fileIdsToUseFromApiRecord = existingRecord.high_quality.split(';').filter(id => id);
                sourceInfo = "high_quality";
              } else if (existingRecord.file_id && existingRecord.file_id.trim() !== "") {
                logger.info(`API解析的作品 ${apiAwemeId} (DB record) 使用 file_id: ${existingRecord.file_id}`);
                fileIdsToUseFromApiRecord = existingRecord.file_id.split(';').filter(id => id);
                sourceInfo = "file_id";
              }

              if (fileIdsToUseFromApiRecord.length > 0) {
                logger.info(`API解析的作品 ${apiAwemeId} (DB record) 将使用来自 ${sourceInfo} 字段的 file_id 发送。`);
                const dbParsedData = {
                  base_info: {
                    aweme_id: existingRecord.aweme_id,
                    desc: existingRecord.description,
                    create_time: existingRecord.create_time
                  },
                  author_info: {
                    nickname: existingRecord.nickname,
                    uid: existingRecord.uid,
                    sec_uid: existingRecord.sec_uid,
                    unique_id: existingRecord.unique_id,
                    follower_count: existingRecord.follower_count,
                    total_favorited: existingRecord.total_favorited
                  },
                  location_info: {
                    province: existingRecord.province,
                    city: existingRecord.city
                  },
                  music_info: {
                    title: existingRecord.music_title,
                    author: existingRecord.music_author,
                    play_url: existingRecord.music_play_url
                  }
                };
                const captionText = buildCaptionForSingle(dbParsedData);
                const secUid = existingRecord.sec_uid || "";
                
                const fileIds = fileIdsToUseFromApiRecord; // Use these
                let zipFileId = null;
                let audioFileId = null;
                for (const fileId of fileIds) {
                  if (detectFileType(fileId) === 'document') zipFileId = fileId;
                  if (detectFileType(fileId) === 'audio') audioFileId = fileId;
                }
                
                const buttons = [];
                buttons.push(Markup.button.url("打开", douyinUrl));
                if (audioFileId) buttons.push(Markup.button.callback("获取音乐", `music:${apiAwemeId}`));
                if (zipFileId) buttons.push(Markup.button.callback("获取文件", `zip:${apiAwemeId}`));
                
                // Use UID for subscribe button
                if (existingRecord.uid) {
                    let isSubscribed = false;
                    try {
                        isSubscribed = await isUserSubscribedToAuthor(chatId, existingRecord.uid);
                    } catch (e) { logger.error("Error checking subscription status in text handler (API existing work)", e);}
                    
                    if (isSubscribed) {
                        buttons.push(Markup.button.callback("取消订阅", `unsub:${existingRecord.uid}`));
                    } else {
                        buttons.push(Markup.button.callback("订阅", `sub:${existingRecord.uid}`));
                    }
                } else if (secUid) { // secUid here is existingRecord.sec_uid
                     logger.warning(`bot.on(text): existingRecord for ${apiAwemeId} (from API) has secUid but no uid. Cannot create sub button.`);
                }
                const markup = Markup.inlineKeyboard(chunkList(buttons, 3));

                await sendMediaByFileIds(ctx, fileIds, captionText, markup, apiAwemeId); // Use these fileIds
                await updateUser(chatId, { already: alreadyUsed + 1 });
                await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "处理完成。");
                await cleanupMessages(ctx, processingMsg);
                return;
              }
              // If no usable file_ids, fall through
            }
          }
        }

        let parsedData = parseDouyinWork(respData);
        if (!parsedData || !parsedData.base_info?.aweme_id) {
          if (isStandardVideoUrl && awemeId) {
            logger.info(`从最小化响应解析失败，尝试直接调用API获取作品 ${awemeId} 详情`);
            respData = await callHybridVideoData(douyinUrl);
            if (respData.not_available) {
              await ctx.telegram.editMessageText(
                chatId, 
                processingMsg.message_id, 
                null, 
                `抱歉，该作品 (ID: ${awemeId}) 不可用或已被删除。\n\n已在系统中标记该作品状态。`
              );
              await updateUser(chatId, { already: alreadyUsed + 1 });
              await cleanupMessages(ctx, processingMsg);
              return;
            }
            const apiAwemeId = respData?.data?.aweme_id;
            if (apiAwemeId) {
              const { data: existingApiData, error: apiCheckError } = await supabase
                .from("douyin")
                .select("*")
                .eq("aweme_id", apiAwemeId)
                .limit(1);
              if (!apiCheckError && existingApiData && existingApiData.length > 0) {
                isWorkExistInDB = true;
                const existingRecord = existingApiData[0];
                if (existingRecord.file_id) {
                  const dbParsedData = {
                    base_info: {
                      aweme_id: existingRecord.aweme_id,
                      desc: existingRecord.description,
                      create_time: existingRecord.create_time
                    },
                    author_info: {
                      nickname: existingRecord.nickname,
                      uid: existingRecord.uid,
                      sec_uid: existingRecord.sec_uid,
                      unique_id: existingRecord.unique_id,
                      follower_count: existingRecord.follower_count,
                      total_favorited: existingRecord.total_favorited
                    },
                    location_info: {
                      province: existingRecord.province,
                      city: existingRecord.city
                    },
                    music_info: {
                      title: existingRecord.music_title,
                      author: existingRecord.music_author,
                      play_url: existingRecord.music_play_url
                    }
                  };
                  const captionText = buildCaptionForSingle(dbParsedData);
                  const secUid = existingRecord.sec_uid || "";
                  
                  const fileIds = existingRecord.file_id.split(';').filter(id => id);
                  let zipFileId = null;
                  let audioFileId = null;
                  for (const fileId of fileIds) {
                    if (detectFileType(fileId) === 'document') zipFileId = fileId;
                    if (detectFileType(fileId) === 'audio') audioFileId = fileId;
                  }
                  
                  const buttons = [];
                  buttons.push(Markup.button.url("打开", douyinUrl));
                  if (audioFileId) buttons.push(Markup.button.callback("获取音乐", `music:${apiAwemeId}`));
                  if (zipFileId) buttons.push(Markup.button.callback("获取文件", `zip:${apiAwemeId}`));
                  
                  // Use UID for subscribe button
                  if (existingRecord.uid) {
                        let isSubscribed = false;
                        try {
                            isSubscribed = await isUserSubscribedToAuthor(chatId, existingRecord.uid);
                        } catch (e) { logger.error("Error checking subscription status in text handler (API fallback existing work)", e);}
                        
                        if (isSubscribed) {
                            buttons.push(Markup.button.callback("取消订阅", `unsub:${existingRecord.uid}`));
                        } else {
                            buttons.push(Markup.button.callback("订阅", `sub:${existingRecord.uid}`));
                        }
                  } else if (secUid) { // secUid here is existingRecord.sec_uid
                        logger.warning(`bot.on(text): existingRecord for ${apiAwemeId} (API fallback) has secUid but no uid. Cannot create sub button.`);
                  }
                  const markup = Markup.inlineKeyboard(chunkList(buttons, 3));

                  await sendMediaByFileIds(ctx, fileIds, captionText, markup, apiAwemeId);
                  await updateUser(chatId, { already: alreadyUsed + 1 });
                  await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "处理完成。");
                  await cleanupMessages(ctx, processingMsg);
                  return;
                }
              }
            }
            parsedData = parseDouyinWork(respData);
            if (!parsedData || !parsedData.base_info?.aweme_id) {
              await ctx.telegram.editMessageText(
                chatId, 
                processingMsg.message_id, 
                null, 
                "解析失败，链接无效或视频无法获取。"
              );
              if (awemeId) {
                try {
                  const minimalData = {
                    base_info: { aweme_id: awemeId, desc: "Not Available" },
                    author_info: {}, 
                    location_info: {}, 
                    music_info: {}, 
                    media_info: {}
                  };
                  await saveDouyinToDatabase(minimalData);
                  logger.info(`因API解析失败，已将作品 ${awemeId} 标记为不可用`);
                } catch (error) {
                  logger.error(`尝试标记不可用作品 ${awemeId} 时出错: ${error.message}`, error);
                }
              }
              await cleanupMessages(ctx, processingMsg);
              return;
            }
          } else {
            await ctx.telegram.editMessageText(
              chatId, 
              processingMsg.message_id, 
              null, 
              "解析失败，链接无效或视频无法获取。"
            );
            if (awemeId) {
              try {
                const minimalData = {
                  base_info: { aweme_id: awemeId, desc: "Not Available" },
                  author_info: {}, 
                  location_info: {}, 
                  music_info: {}, 
                  media_info: {}
                };
                await saveDouyinToDatabase(minimalData);
                logger.info(`因解析失败，已将作品 ${awemeId} 标记为不可用`);
              } catch (error) {
                logger.error(`尝试标记不可用作品 ${awemeId} 时出错: ${error.message}`, error);
              }
            }
            await cleanupMessages(ctx, processingMsg);
            return;
          }
        }

        const finalAwemeId = parsedData.base_info.aweme_id;
        logger.info(`成功解析作品信息，aweme_id: ${finalAwemeId}`);

        if (!isWorkExistInDB && finalAwemeId) {
          const { data: finalCheck, error: finalCheckError } = await supabase
            .from("douyin")
            .select("*")
            .eq("aweme_id", finalAwemeId)
            .limit(1);
          if (!finalCheckError && finalCheck && finalCheck.length > 0) {
            isWorkExistInDB = true;
            const existingRecord = finalCheck[0];
            logger.info(`最终解析的作品 ${finalAwemeId} 已存在于数据库`);

            let fileIdsToUseFromFinalCheck = [];
            let sourceInfo = "";
            if (existingRecord.high_quality && existingRecord.high_quality.trim() !== "") {
              logger.info(`作品 ${finalAwemeId} (DB final check) 使用 high_quality: ${existingRecord.high_quality}`);
              fileIdsToUseFromFinalCheck = existingRecord.high_quality.split(';').filter(id => id);
              sourceInfo = "high_quality";
            } else if (existingRecord.file_id && existingRecord.file_id.trim() !== "") {
              logger.info(`作品 ${finalAwemeId} (DB final check) 使用 file_id: ${existingRecord.file_id}`);
              fileIdsToUseFromFinalCheck = existingRecord.file_id.split(';').filter(id => id);
              sourceInfo = "file_id";
            }
            
            if (fileIdsToUseFromFinalCheck.length > 0) {
              logger.info(`作品 ${finalAwemeId} 已有可用的 ${sourceInfo}，将直接使用现有记录发送`);
              const dbParsedData = {
                base_info: {
                  aweme_id: existingRecord.aweme_id,
                  desc: existingRecord.description,
                  create_time: existingRecord.create_time
                },
                author_info: {
                  nickname: existingRecord.nickname,
                  uid: existingRecord.uid,
                  sec_uid: existingRecord.sec_uid,
                  unique_id: existingRecord.unique_id,
                  follower_count: existingRecord.follower_count,
                  total_favorited: existingRecord.total_favorited
                },
                location_info: {
                  province: existingRecord.province,
                  city: existingRecord.city
                },
                music_info: {
                  title: existingRecord.music_title,
                  author: existingRecord.music_author,
                  play_url: existingRecord.music_play_url
                }
              };
              const captionText = buildCaptionForSingle(dbParsedData);
              const secUid = existingRecord.sec_uid || "";
              
              const fileIds = fileIdsToUseFromFinalCheck; // Use these
              let zipFileId = null;
              let audioFileId = null;
              for (const fileId of fileIds) {
                if (detectFileType(fileId) === 'document') zipFileId = fileId;
                if (detectFileType(fileId) === 'audio') audioFileId = fileId;
              }
              
              const buttons = [];
              buttons.push(Markup.button.url("打开", douyinUrl));
              if (audioFileId) buttons.push(Markup.button.callback("获取音乐", `music:${finalAwemeId}`));
              if (zipFileId) buttons.push(Markup.button.callback("获取文件", `zip:${finalAwemeId}`));
              
              // Use UID for subscribe button
              if (existingRecord.uid) {
                    let isSubscribed = false;
                    try {
                        isSubscribed = await isUserSubscribedToAuthor(chatId, existingRecord.uid);
                    } catch (e) { logger.error("Error checking subscription status in text handler (API existing work)", e);}
                    
                    if (isSubscribed) {
                        buttons.push(Markup.button.callback("取消订阅", `unsub:${existingRecord.uid}`));
                    } else {
                        buttons.push(Markup.button.callback("订阅", `sub:${existingRecord.uid}`));
                    }
              } else if (secUid) { // secUid here is existingRecord.sec_uid
                    logger.warning(`bot.on(text): existingRecord for ${finalAwemeId} (from API) has secUid but no uid. Cannot create sub button.`);
              }
              const markup = Markup.inlineKeyboard(chunkList(buttons, 3));

              await sendMediaByFileIds(ctx, fileIds, captionText, markup, finalAwemeId); // Use these fileIds
              await updateUser(chatId, { already: alreadyUsed + 1 });
              await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "处理完成。");
              await cleanupMessages(ctx, processingMsg);
              return;
            } else {
              logger.info(`作品 ${finalAwemeId} 存在于数据库 (final check) 但 high_quality 和 file_id 均为空或无效，将进行完整API后续处理。`);
            }
          }
        }

        if (!isWorkExistInDB) {
          logger.info(`将作品 ${finalAwemeId} 保存到数据库`);
          await saveDouyinToDatabase(parsedData);
          logger.info(`成功保存作品 ${finalAwemeId} 到数据库`);
        } else {
          logger.info(`作品 ${finalAwemeId} 已存在于数据库，跳过保存`);
        }

        // 新增: 在早期阶段检查视频时长
        const durationSeconds = parsedData.media_info?.duration ? (parsedData.media_info.duration / 1000) : 0;
        if (durationSeconds > 600) {
          // 超过10分钟的视频直接标记为failed
          logger.info(`检测到超长视频: ${finalAwemeId}, 时长: ${durationSeconds.toFixed(2)}秒 > 10分钟，将直接标记为failed`);
          
          const failedReason = "video_too_long:视频时长超过10分钟";
          const { error: failedUpdateError } = await supabase
            .from("douyin")
            .update({ failed: failedReason })
            .eq("aweme_id", finalAwemeId);
          
          if (failedUpdateError) {
            logger.error(`更新超长视频 ${finalAwemeId} 的failed字段失败: ${failedUpdateError.message}`, failedUpdateError);
          } else {
            logger.info(`已将超长视频 ${finalAwemeId} 的failed字段更新为 ${failedReason}`);
          }
          
          await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "视频时长超过10分钟，文件过大无法处理。");
          await updateUser(chatId, { already: alreadyUsed + 1 });
          await cleanupMessages(ctx, processingMsg);
          return;
        }
        else if (durationSeconds > 360) {
          logger.info(`检测到长视频: ${finalAwemeId}, 时长: ${durationSeconds.toFixed(2)}秒 > 6分钟，将标记为large并跳过下载`);
          await updateLargeField(ctx, finalAwemeId, "视频时长超过6分钟");
          await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "视频时长超过6分钟，后台处理中，请稍后再查看。");
          await updateUser(chatId, { already: alreadyUsed + 1 });
          await cleanupMessages(ctx, processingMsg);
          return;
        }

        let userData = null;
        if (respData.data) {
          userData = parseDouyinUser(respData);
          logger.info(userData ? `成功解析作者信息: ${userData.nickname || userData.uid}` : "无法解析作者信息");
        } else if (parsedData.author_info) {
          const authorInfo = parsedData.author_info;
          userData = {
            uid: authorInfo.uid,
            sec_uid: authorInfo.sec_uid,
            nickname: authorInfo.nickname,
            unique_id: authorInfo.unique_id,
            follower_count: authorInfo.follower_count,
            total_favorited: authorInfo.total_favorited,
            last_fetched_at: new Date().toISOString()
          };
          logger.info(`从作品信息提取作者数据: ${userData.nickname || userData.uid}`);
        }
        
        if (userData) {
          logger.info(`保存作者信息到数据库`);
          await saveEnhancedUserToDatabase(userData);
        }

        logger.info(`开始构建标题文本和准备下载`);
        const captionText = buildCaptionForSingle(parsedData);
        const finalSecUid = parsedData.author_info?.sec_uid || "";

        const wdir = path.join("douyin", `single_${chatId}_${finalAwemeId}`);
        await fs.mkdir(wdir, { recursive: true });
        logger.info(`创建临时目录: ${wdir}`);
        
        const downloadedFiles = [];
        let hasLargeFile = false;

        try {
          logger.info(`开始处理媒体文件`);
          const mediaInfo = parsedData.media_info;
          const images = mediaInfo.images || [];
          const playUrl = mediaInfo.play_url || "";
          const musicUrl = parsedData.music_info?.play_url || "";
          const downloadPromises = [];
          const fileInfos = [];

          if (images.length > 0) {
            logger.info(`检测到图片集合，共 ${images.length} 张图片`);
            for (let idx = 0; idx < images.length; idx++) {
              const imgObj = images[idx];
              const imgUrl = imgObj.url;
              const vid = imgObj.video;
              if (imgUrl) {
                const fpath = path.join(wdir, `${finalAwemeId}_${idx}.jpg`);
                downloadPromises.push(downloadFile(imgUrl));
                fileInfos.push({ type: 'image', path: fpath, originalUrl: imgUrl, index: idx });
              }
              if (vid && vid.url) {
                const vpath = path.join(wdir, `${finalAwemeId}_${idx}.mp4`);
                downloadPromises.push(downloadFile(vid.url));
                fileInfos.push({ type: 'video', path: vpath, originalUrl: vid.url, index: idx });
              }
            }
          }
          if (playUrl && images.length === 0) {
            logger.info(`检测到视频URL: ${playUrl}`);
            const videoPath = path.join(wdir, `${finalAwemeId}.mp4`);
            downloadPromises.push(downloadFile(playUrl));
            fileInfos.push({ type: 'video', path: videoPath, originalUrl: playUrl });
          }
          if (musicUrl) {
            logger.info(`检测到音乐URL: ${musicUrl}`);
            let musicTitle = "";
            let musicAuthor = "";
            try {
              const musicInfo = parsedData.music_info || {};
              musicTitle = musicInfo.title || "";
              musicAuthor = musicInfo.author || "";
            } catch (e) {}
            const safeFilenameBase = musicTitle 
              ? `${musicTitle}-${musicAuthor}`.replace(/[\s-]+$/, '').replace(/[<>:"\/\\|?*]/g, '_') 
              : `music_${finalAwemeId}`;
            const musicPath = path.join(wdir, `${safeFilenameBase}.mp3`);
            downloadPromises.push(downloadFile(musicUrl));
            fileInfos.push({ type: 'music', path: musicPath, originalUrl: musicUrl });
          }

          if (downloadPromises.length > 0) {
            logger.info(`开始下载 ${downloadPromises.length} 个媒体文件`);
            const settledResults = await Promise.allSettled(downloadPromises);
            let hasDownloadFailures = false;
            
            logger.info(`下载完成，处理下载结果`);
            for (let i = 0; i < settledResults.length; i++) {
              const settled = settledResults[i];
              const info = fileInfos[i];
              if (settled.status === 'fulfilled' && settled.value) {
                const result = settled.value;
                if (result && result.binary_data) {
                  const fileSizeMB = (result.size / (1024 * 1024));
                  if (fileSizeMB > 49) {
                    hasLargeFile = true;
                    logger.info(`发现大文件: ${info.path}, 大小: ${fileSizeMB.toFixed(2)}MB, 将放弃处理并标记large`);
                    break;
                  }
                  try {
                    await fs.writeFile(info.path, result.binary_data);
                    downloadedFiles.push(info.path);
                    logger.info(`成功保存文件: ${info.path}, 大小: ${fileSizeMB.toFixed(2)}MB`);
                  } catch (writeError) {
                    logger.error(`写入文件失败 ${info.path}: ${writeError.message}`);
                  }
                } else {
                  logger.warning(`下载结果无效: ${info.type} ${info.originalUrl}`);
                }
              } else {
                hasDownloadFailures = true;
                const errorReason = settled.reason 
                  ? (settled.reason.message || String(settled.reason)) 
                  : '未知错误';
                logger.warning(`下载失败: ${info.type} ${info.originalUrl}, 原因: ${errorReason}`);
              }
            }
            
            // 新增：如果有下载失败，更新failed字段并通知用户
            if (hasDownloadFailures && finalAwemeId) {
              try {
                const failedReason = `download_failed:${chatId}`;
                const { error: failedUpdateError } = await supabase
                  .from("douyin")
                  .update({ failed: failedReason })
                  .eq("aweme_id", finalAwemeId);
                
                if (failedUpdateError) {
                  logger.error(`更新作品 ${finalAwemeId} 的failed字段失败 (下载失败): ${failedUpdateError.message}`, failedUpdateError);
                } else {
                  logger.info(`已将作品 ${finalAwemeId} 的failed字段更新为 ${failedReason} (下载失败)`);
                }
                
                // 如果没有下载成功任何文件，通知用户稍后再试
                if (downloadedFiles.length === 0) {
                  await ctx.telegram.editMessageText(
                    chatId, 
                    processingMsg.message_id, 
                    null, 
                    "下载遇到问题，请稍后再试。系统已记录此问题，将尝试后台修复。"
                  );
                  await updateUser(chatId, { already: alreadyUsed + 1 });
                  await cleanupMessages(ctx, processingMsg, 10);
                  // 提前返回，不继续处理
                  return;
                }
              } catch (failedMarkError) {
                logger.error(`标记下载失败状态时出错: ${failedMarkError.message}`, failedMarkError);
              }
            }
          }
          
          if (hasLargeFile) {
            await updateLargeField(ctx, finalAwemeId, "单个文件过大");
            await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "文件过大，后台处理中，请稍后再查看。");
            await updateUser(chatId, { already: alreadyUsed + 1 });
            await cleanupMessages(ctx, processingMsg);
            return;
          }

          if (downloadedFiles.length > 0) {
            await sendMediaFiles(
              ctx,
              downloadedFiles,
              douyinUrl,
              captionText,
              finalSecUid,
              musicUrl,
              false,
              null,
              finalAwemeId
            );
            
            await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "处理完成。");
            await updateUser(chatId, { already: alreadyUsed + 1 });
          } else {
            await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "处理失败或没有可下载的媒体。");
          }
        } finally {
          if (fsSync.existsSync(wdir)) {
            try {
              await fs.rm(wdir, { recursive: true, force: true });
              logger.info(`成功清理临时目录: ${wdir}`);
            } catch (cleanupError) {
              logger.warning(`清理临时目录 ${wdir} 失败: ${cleanupError.message}，将在后台异步清理`);
              setTimeout(() => {
                try { fsSync.rmSync(wdir, { recursive: true, force: true }); } catch (e) {}
              }, 5000);
            }
          }
        }
      } catch (error) {
        logger.error(`处理单个作品链接 ${douyinUrl} 出错: ${error.message}`, error);
        
        // 无论什么错误，都尝试静默删除用户消息
        try {
          // 删除用户发送的原始消息
          await ctx.telegram.deleteMessage(ctx.chat.id, ctx.message.message_id);
          
          // 如果有处理中的消息，也删除它
          if (processingMsg && processingMsg.message_id) {
            await ctx.telegram.deleteMessage(ctx.chat.id, processingMsg.message_id);
          }
          
          logger.info(`已静默删除用户 ${ctx.chat.id} 发送的出错链接: ${douyinUrl}`);
        } catch (deleteError) {
          logger.error(`静默删除消息失败: ${deleteError.message}`, deleteError);
        }
        
        // 尝试将API错误信息作为aweme_id为空的记录保存
        if (douyinUrl) {
          try {
            // 尝试确保 awemeId 对 minimalData 可用
            // 首先使用从URL解析的awemeId，如果错误发生在解析实际内容之前，它可能是唯一可用的ID
            // 如果respData已定义且包含aweme_id (意味着API调用至少返回了结构)，则使用它
            // parsedData是在respData之后生成的，所以它的aweme_id是进一步确认的
            const idForDb = awemeId || respData?.data?.aweme_id || parsedData?.base_info?.aweme_id || "unknown_aweme_id_on_error";
            
            const minimalData = {
              base_info: { 
                aweme_id: idForDb, 
                desc: `API_ERROR: ${error.message.substring(0, 250)}` // 增加错误描述长度
              },
              author_info: {},
              location_info: {},
              music_info: {},
              media_info: {}
            };
            await saveDouyinToDatabase(minimalData);
            logger.info(`已将API错误信息作为description保存到数据库 (aweme_id: ${idForDb})`);

            // 新增：更新 failed 字段
            if (idForDb && idForDb !== "unknown_aweme_id_on_error") {
              const failureReason = `processing_error: ${error.message.substring(0, 250)}`; // 保持错误描述一致性
              try {
                const { error: updateFailedError } = await supabase
                  .from("douyin")
                  .update({ failed: failureReason })
                  .eq("aweme_id", idForDb);
                if (updateFailedError) {
                  logger.error(`更新作品 ${idForDb} 的failed字段失败 (捕获的错误): ${updateFailedError.message}`, updateFailedError);
                } else {
                  logger.info(`已更新作品 ${idForDb} 的failed字段为: ${failureReason}`);
                }
              } catch (dbUpdateFailedError) {
                logger.error(`更新作品 ${idForDb} 的failed字段时发生异常 (捕获的错误): ${dbUpdateFailedError.message}`, dbUpdateFailedError);
              }
            } else {
              logger.warning(`无法更新failed字段，因为 aweme_id 在错误处理中不可用或为unknown_aweme_id_on_error.`);
            }

          } catch (dbError) {
            logger.error(`在主错误处理中保存错误信息到数据库失败: ${dbError.message}`, dbError);
          }
        }
        
        return; // 确保在处理完错误后退出，不再执行后续的 cleanupMessages
      }
      
      await cleanupMessages(ctx, processingMsg);
      return;
    }

    const replyMsg = await ctx.reply("未检测到有效的抖音链接。");
    setTimeout(async () => {
      try { await ctx.telegram.deleteMessage(ctx.chat.id, replyMsg.message_id); } catch (e) {}
      try { await ctx.telegram.deleteMessage(ctx.chat.id, ctx.message.message_id); } catch (e) {}
    }, 10000);
  } catch (error) { // 这是 bot.on('text', ...) 的最外层 catch
    logger.error(`处理文本消息出错: ${error.message}`, error);
    if (ctx && ctx.reply) {
      // 为了避免双重错误消息(如果内部catch已处理并静默删除)，此处不再主动回复
      // await ctx.reply(`❌ 出错: ${error.message}`);
    }
  }
});

async function cleanupMessages(ctx, processMsg, delay = 5.0) {
  await new Promise(resolve => setTimeout(resolve, delay * 1000));
  try {
    await ctx.telegram.deleteMessage(ctx.chat.id, processMsg.message_id);
  } catch (error) {}
  try {
    await ctx.telegram.deleteMessage(ctx.chat.id, ctx.message.message_id);
  } catch (error) {}
}

// =============== 标记作品为失败状态 ===============
async function markWorkAsFailed(awemeId, chatId) {
  if (!awemeId) {
    logger.error(`无法标记失败状态：缺少 awemeId`);
    return;
  }
  if (!chatId) {
    logger.error(`无法标记失败状态 (awemeId: ${awemeId})：缺少 chatId`);
    return;
  }
  try {
    const failedStatus = `file id pending:${chatId}`;
    const { error } = await supabase
      .from("douyin")
      .update({ failed: failedStatus })
      .eq("aweme_id", awemeId);

    if (error) {
      logger.error(`标记作品 ${awemeId} 为失败状态 (${failedStatus}) 失败: ${error.message}`, error);
    } else {
      logger.info(`已将作品 ${awemeId} 标记为失败状态 (${failedStatus})`);
    }
  } catch (dbError) {
    logger.error(`标记作品 ${awemeId} 为失败状态时发生异常: ${dbError.message}`, dbError);
  }
}

// 提取视频缩略图
async function extractVideoThumbnail(videoPath) {
  try {
    const thumbnailPath = `${videoPath}_thumb.jpg`;
    const cmd = `ffmpeg -i "${videoPath}" -ss 00:00:01 -vframes 1 -vf "scale=320:-1" -q:v 2 "${thumbnailPath}" -y`;
    await execPromise(cmd);
    
    if (fsSync.existsSync(thumbnailPath) && fsSync.statSync(thumbnailPath).size > 0) {
      logger.info(`成功从视频第1秒提取缩略图: ${thumbnailPath}`);
      return thumbnailPath;
    } else {
      const cmd2 = `ffmpeg -i "${videoPath}" -ss 00:00:00.1 -vframes 1 -vf "scale=320:-1" -q:v 2 "${thumbnailPath}" -y`;
      await execPromise(cmd2);
      if (fsSync.existsSync(thumbnailPath) && fsSync.statSync(thumbnailPath).size > 0) {
        logger.info(`成功从视频开始处提取缩略图: ${thumbnailPath}`);
        return thumbnailPath;
      }
    }
    logger.error(`缩略图提取失败或文件为空: ${thumbnailPath}`);
    return null;
  } catch (error) {
    logger.error(`提取视频缩略图时出错: ${error.message}`, error);
    return null;
  }
}

// 混合数据接口 - 保留，主要用来获取作品信息
async function callHybridVideoData(douyinUrl) {
  return retryApiCallWithBackoff(async () => {
    try {
      const encodedUrl = encodeURIComponent(douyinUrl);
      const apiUrl = `${API_BASE_URL}/api/hybrid/video_data?url=${encodedUrl}&minimal=false`;
      
      logger.info(`[callHybridVideoData] 原始URL: ${douyinUrl}`);
      logger.info(`[callHybridVideoData] 完整API URL: ${apiUrl}`);
      
      const response = await fetch(apiUrl, { timeout: 30000 });
      logger.info(`[callHybridVideoData] 状态码: ${response.status}`);
      
      if (response.status === 429) {
        throw { status: 429, message: "API限流，请求过于频繁" };
      }
      
      const text = await response.text();
      logger.info(`[callHybridVideoData] 响应内容(前200字): ${text.substring(0, 200)}...`);
      
      if (!response.ok) {
        // 主API请求失败，尝试使用TikHub API作为备用方案
        logger.info(`[callHybridVideoData] 主API调用失败，尝试使用TikHub API作为备用方案`);
        try {
          // 检查是否有TikHub API密钥
          const tikhubApiKey = process.env.TIKHUB_API_KEY;
          if (!tikhubApiKey) {
            logger.warning(`[callHybridVideoData] 未配置TIKHUB_API_KEY环境变量，无法使用备用API`);
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          
          const tikhubUrl = `https://api.tikhub.io/api/v1/douyin/app/v3/fetch_one_video_by_share_url?share_url=${encodedUrl}`;
          
          logger.info(`[callHybridVideoData] 尝试TikHub API: ${tikhubUrl}`);
          
          const tikhubResponse = await fetch(tikhubUrl, {
            method: 'GET',
            headers: {
              'accept': 'application/json',
              'Authorization': `Bearer ${tikhubApiKey}`
            },
            timeout: 30000
          });
          
          if (!tikhubResponse.ok) {
            logger.error(`[callHybridVideoData] TikHub API请求失败: ${tikhubResponse.status}`);
            throw new Error(`TikHub API HTTP error! status: ${tikhubResponse.status}`);
          }
          
          const tikhubData = await tikhubResponse.json();
          logger.info(`[callHybridVideoData] TikHub API响应成功`);
          
          // 检查是否包含aweme_id
          let awemeId = null;
          if (tikhubData.data && tikhubData.data.filter_list && tikhubData.data.filter_list.length > 0) {
            awemeId = tikhubData.data.filter_list[0].aweme_id;
            logger.info(`[callHybridVideoData] 从TikHub API获取到aweme_id: ${awemeId}`);
          } else if (tikhubData.data && tikhubData.data.aweme_details && tikhubData.data.aweme_details.length > 0) {
            awemeId = tikhubData.data.aweme_details[0].aweme_id;
            logger.info(`[callHybridVideoData] 从TikHub API的aweme_details获取到aweme_id: ${awemeId}`);
          }
          
          if (awemeId) {
            // 标记为不可用，但记录aweme_id
            logger.info(`[callHybridVideoData] 长视频检测到aweme_id: ${awemeId}，将静默处理并标记为not available`);
            
            // 调用本地API获取filter_reason
            try {
              const localApiUrl = `${API_BASE_URL}/api/douyin/web/fetch_one_video?aweme_id=${awemeId}`;
              logger.info(`[callHybridVideoData] 调用本地API获取filter_reason: ${localApiUrl}`);
              
              const localApiResponse = await fetch(localApiUrl, { timeout: 30000 });
              if (localApiResponse.ok) {
                const localApiData = await localApiResponse.json();
                logger.info(`[callHybridVideoData] 本地API响应成功`);
                
                let filterReason = "unknown";
                if (localApiData.data && 
                    localApiData.data.filter_detail && 
                    localApiData.data.filter_detail.filter_reason) {
                  filterReason = localApiData.data.filter_detail.filter_reason;
                  logger.info(`[callHybridVideoData] 获取到filter_reason: ${filterReason}`);
                }
                
                // 构建最小化数据对象，包含filter_reason
                const minimalData = {
                  base_info: { 
                    aweme_id: awemeId, 
                    desc: filterReason  // 将filter_reason作为描述
                  },
                  author_info: {},
                  location_info: {},
                  music_info: {},
                  media_info: {}
                };
                
                // 将数据保存到数据库
                await saveDouyinToDatabase(minimalData);
              } else {
                logger.warning(`[callHybridVideoData] 本地API请求失败: ${localApiResponse.status}`);
                // 如果本地API调用失败，仍然保存基本信息
                const minimalData = {
                  base_info: { 
                    aweme_id: awemeId, 
                    desc: "Not Available (Long Video)"
                  },
                  author_info: {},
                  location_info: {},
                  music_info: {},
                  media_info: {}
                };
                await saveDouyinToDatabase(minimalData);
              }
            } catch (localApiError) {
              logger.error(`[callHybridVideoData] 本地API调用错误: ${localApiError.message}`, localApiError);
              // 出错时仍然保存基本信息
              const minimalData = {
                base_info: { 
                  aweme_id: awemeId, 
                  desc: "Not Available (API Error)"
                },
                author_info: {},
                location_info: {},
                music_info: {},
                media_info: {}
              };
              await saveDouyinToDatabase(minimalData);
            }
            
            // 返回标准格式的响应，包含aweme_id和not_available标记
            return { 
              data: { aweme_id: awemeId },
              not_available: true,
              status_message: "长视频内容不支持下载",
              is_long_video: true,
              silent_process: true  // 添加静默处理标记
            };
          } else {
            logger.warning(`[callHybridVideoData] TikHub API未返回有效的aweme_id`);
          }
        } catch (backupError) {
          logger.error(`[callHybridVideoData] 备用API调用失败: ${backupError.message}`, backupError);
        }
        
        // 如果备用方案也失败，则继续原有的逻辑
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const jsonData = JSON.parse(text);
      const isNotAvailable = (
        !jsonData 
        || !jsonData.data 
        || jsonData.status_code === 404
        || jsonData.status_code === 10000
        || (jsonData.data && jsonData.data.status_code === 404)
        || (jsonData.data && jsonData.data.error_code === 10000)
        || (jsonData.data && jsonData.data.aweme_id === undefined)
        || (text.includes("Not Found") && text.includes("error"))
        || text.includes("作品不存在")
        || text.includes("视频不见了")
      );
      
      if (isNotAvailable) {
        let awemeId = null;
        const videoIdMatch = douyinUrl.match(/\/video\/(\d+)/);
        const noteIdMatch = douyinUrl.match(/\/note\/(\d+)/);
        const lvDetailMatch = douyinUrl.match(/\/lvdetail\/(\d+)/);
        
        if (videoIdMatch && videoIdMatch[1]) {
          awemeId = videoIdMatch[1];
        } else if (noteIdMatch && noteIdMatch[1]) {
          awemeId = noteIdMatch[1];
        } else if (lvDetailMatch && lvDetailMatch[1]) {
          // 处理长视频链接格式
          awemeId = lvDetailMatch[1];
          logger.info(`[callHybridVideoData] 检测到长视频格式，ID: ${awemeId}`);
        }
        
        if (awemeId) {
          logger.warning(`检测到不可用作品: ${awemeId}，将标记为not available`);
          const minimalData = {
            base_info: { aweme_id: awemeId, desc: "Not Available" },
            author_info: {},
            location_info: {},
            music_info: {},
            media_info: {}
          };
          await saveDouyinToDatabase(minimalData);
          return { 
            data: { aweme_id: awemeId },
            not_available: true,
            status_message: "作品不可用或已删除"
          };
        }
      }
      
      // 新增: 检查视频时长
      let videoDuration = 0;
      try {
        // 尝试从jsonData中提取视频时长信息
        if (jsonData && jsonData.data && jsonData.data.duration) {
          // 抖音API总是返回毫秒，将其转换为秒
          videoDuration = parseInt(jsonData.data.duration) || 0;
          videoDuration = videoDuration / 1000; // 始终转换为秒
          
          logger.info(`[callHybridVideoData] 从API响应中检测到视频时长: ${videoDuration.toFixed(2)}秒 (原始值: ${jsonData.data.duration}毫秒)`);
          
          const awemeId = jsonData.data.aweme_id;
          if (!awemeId) {
            logger.warning(`[callHybridVideoData] 无法从API响应中提取aweme_id`);
          } else {
            // 超长视频(>10分钟/600秒)直接标记为failed
            if (videoDuration > 600) {
              logger.info(`[callHybridVideoData] 检测到超长视频 (${videoDuration.toFixed(2)}秒 > 10分钟): ${awemeId}，直接标记为failed`);
              
              // 保存作品信息到数据库
              const parsedData = parseDouyinWork(jsonData);
              if (parsedData && parsedData.base_info) {
                await saveDouyinToDatabase(parsedData);
                
                // 标记为failed
                const failedReason = "video_too_long:视频时长超过10分钟";
                const { error: failedUpdateError } = await supabase
                  .from("douyin")
                  .update({ failed: failedReason })
                  .eq("aweme_id", awemeId);
                
                if (failedUpdateError) {
                  logger.error(`[callHybridVideoData] 更新超长视频 ${awemeId} 的failed字段失败: ${failedUpdateError.message}`, failedUpdateError);
                } else {
                  logger.info(`[callHybridVideoData] 已将超长视频 ${awemeId} 的failed字段更新为 ${failedReason}`);
                }
                
                // 返回特殊标记
                jsonData.video_too_long = true;
                jsonData.video_duration = videoDuration;
                return jsonData;
              }
            }
            // 长视频(6-10分钟)仍然标记为large
            else if (videoDuration > 360) {
              logger.info(`[callHybridVideoData] 检测到长视频 (${videoDuration.toFixed(2)}秒 > 6分钟): ${awemeId}`);
              
              // 保存作品信息到数据库，设置时长
              const parsedData = parseDouyinWork(jsonData);
              if (parsedData && parsedData.base_info) {
                await saveDouyinToDatabase(parsedData);
                
                // 不再返回 not_available，而是返回正常响应，让后续处理逻辑检测时长并标记为large
                jsonData.detected_long_video = true;
                jsonData.video_duration = videoDuration;
                return jsonData;
              }
            }
            else {
              // 普通视频，仅记录时长并返回
              logger.info(`[callHybridVideoData] 检测到普通视频 (${videoDuration.toFixed(2)}秒 < 6分钟): ${awemeId}`);
              jsonData.video_duration = videoDuration;
            }
          }
        }
      } catch (durationError) {
        logger.error(`[callHybridVideoData] 检查视频时长时出错: ${durationError.message}`, durationError);
      }
      
      logger.info(`[callHybridVideoData] 完成视频检测，返回响应数据`);
      return jsonData;
    } catch (error) {
      logger.error(`获取视频信息失败: ${error.message}`, error);
      throw error;
    }
  }, 3, 2000);
}

// =============== 主入口 ===============
function main() {
  logger.info("Starting douyin_bot...");
  
  if (!fsSync.existsSync('douyin')) {
    fsSync.mkdirSync('douyin');
  }
  
  bot.launch().then(async () => {
    logger.info("Bot is running!");
    try {
      const me = await bot.telegram.getMe();
      logger.info(`Logged in as: ${me.username} (ID: ${me.id})`);
    } catch (err) {
      logger.error(`Failed to get bot info: ${err.message}`, err);
    }
    
    const channel = await initRealtimeListener(bot);
    if (channel) {
      logger.info("Realtime监听已激活");
    } else {
      logger.warning("Realtime监听初始化失败");
    }
    
  }).catch(err => {
    logger.error(`Bot 启动失败: ${err.message}`, err);
  });
  
  process.once('SIGINT', () => bot.stop('SIGINT'));
  process.once('SIGTERM', () => bot.stop('SIGTERM'));
}

main();

// 全局异常捕获
process.on('uncaughtException', (error) => {
  try {
    logger.error(`全局未捕获异常: ${error.message}`, error);
    logger.error(`堆栈: ${error.stack}`);
  } catch (logError) {
    console.error('记录错误日志时出错:', logError);
    console.error('原始错误:', error);
  }
});

process.on('unhandledRejection', (reason, promise) => {
  try {
    logger.error(`未处理的Promise拒绝: ${reason instanceof Error ? reason.message : String(reason)}`);
    if (reason instanceof Error) {
      logger.error(`堆栈: ${reason.stack}`);
    }
  } catch (logError) {
    console.error('记录错误日志时出错:', logError);
    console.error('原始拒绝原因:', reason);
  }
});

// 在 numberWithCommas 函数后新增两个辅助函数，用于检测作品和直播订阅状态
// ... existing code ...
async function isUserSubscribedWork(chatId, uid) {
  try {
    if (!chatId || !uid) return false;
    const { count, error } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("user_id", chatId)
      .eq("uid", uid)
      .eq("douyin", true)  // 仅检查作品订阅
      .limit(1);
    if (error) {
      logger.error(`检查用户 ${chatId} 是否订阅作品 ${uid} 失败: ${error.message}`, error);
      return false;
    }
    return count > 0;
  } catch (err) {
    logger.error(`isUserSubscribedWork 异常: ${err.message}`, err);
    return false;
  }
}

async function isUserSubscribedLive(chatId, uid) {
  try {
    if (!chatId || !uid) return false;
    const { count, error } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("user_id", chatId)
      .eq("uid", uid)
      .eq("live", true)  // 仅检查直播订阅
      .limit(1);
    if (error) {
      logger.error(`检查用户 ${chatId} 是否订阅直播 ${uid} 失败: ${error.message}`, error);
      return false;
    }
    return count > 0;
  } catch (err) {
    logger.error(`isUserSubscribedLive 异常: ${err.message}`, err);
    return false;
  }
}
// ... existing code ...
