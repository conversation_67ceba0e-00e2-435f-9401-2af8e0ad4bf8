import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import axios from 'axios';
import { Telegraf, Markup } from 'telegraf';
import { createClient } from '@supabase/supabase-js';
import { parse as parseDate } from 'date-fns'; // 或者使用 dayjs、luxon 等库
import { v4 as uuidv4 } from 'uuid'; // 用于生成随机文件名(若需要)
import AdmZip from 'adm-zip'; // 用于打包zip

// ES Modules 中需要这样获取 __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 初始化 dotenv
dotenv.config();

// 确保必要的环境变量存在
const requiredEnvVars = [
  'SUPABASE_URL',
  'SUPABASE_KEY',
  'BOT_TOKEN',
  'TWITTER_COOKIE_LIKES',
  'TWITTER_COOKIE_USER',
  'TWITTER_USERNAME',
  'FIXED_CHAT_ID'
];

for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
}

/*******************************************************
 * 全局配置与初始化
 *******************************************************/

// 从环境变量加载
const {
  // Supabase
  SUPABASE_URL,
  SUPABASE_KEY,
  DUCKSNIFF_FOLLOWINGS_API,
  DUCKSNIFF_FOLLOWINGS_AUTH,
  DUCKSNIFF_SCREEN_NAME,

  // Telegram Bot
  TELEGRAM_API_ID,     // Python中没直接用，但保留
  TELEGRAM_API_HASH,   // Python中没直接用，但保留
  BOT_TOKEN,

  // Twitter
  TWITTER_COOKIE_LIKES,
  TWITTER_COOKIE_USER,
  TWITTER_USERNAME,
  FIXED_CHAT_ID: TWITTER_FIXED_CHAT_ID
} = process.env;

// 打印关键配置（用于调试）
console.log('Supabase URL:', SUPABASE_URL);
console.log('Bot Token:', BOT_TOKEN ? '已设置' : '未设置');

// Supabase 客户端
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// Telegram Bot - 使用 Telegraf
const bot = new Telegraf(BOT_TOKEN);

// 获取并显示 Bot 和目标用户信息
async function displayBotInfo() {
  try {
    // 获取 bot 信息
    const botInfo = await bot.telegram.getMe();
    console.log('\n=== Bot 信息 ===');
    console.log(`Bot 名称: @${botInfo.username}`);
    console.log(`Bot 显示名称: ${botInfo.first_name}`);
    console.log(`Bot ID: ${botInfo.id}`);

    // 获取目标聊天信息
    try {
      const chatInfo = await bot.telegram.getChat(TWITTER_FIXED_CHAT_ID);
      console.log('\n=== 目标聊天信息 ===');
      console.log(`聊天 ID: ${chatInfo.id}`);
      console.log(`类型: ${chatInfo.type}`);
      if (chatInfo.type === 'private') {
        console.log(`用户名: @${chatInfo.username || '未设置'}`);
        console.log(`显示名称: ${chatInfo.first_name} ${chatInfo.last_name || ''}`);
      } else if (chatInfo.type === 'group' || chatInfo.type === 'supergroup') {
        console.log(`群组名称: ${chatInfo.title}`);
      }
    } catch (chatErr) {
      console.log('\n=== 目标聊天信息 ===');
      console.log(`聊天 ID: ${TWITTER_FIXED_CHAT_ID}`);
      console.log('无法获取详细信息，可能是 bot 还未与目标用户/群组建立对话');
    }
  } catch (err) {
    console.error('获取 Bot 信息失败:', err.message);
  }
}

// 全局标志
let monitoringStarted = false;

/**
 * 下载目录为程序所在目录的downloads文件夹
 */
const TEMP_DOWNLOAD_DIR = path.join(__dirname, 'downloads');
if (!fs.existsSync(TEMP_DOWNLOAD_DIR)) {
  fs.mkdirSync(TEMP_DOWNLOAD_DIR, { recursive: true });
}


// 记录日志或打印
function logInfo(msg) {
  console.log(`[INFO] ${msg}`);
}
function logWarn(msg) {
  console.warn(`[WARN] ${msg}`);
}
function logError(msg) {
  console.error(`[ERR] ${msg}`);
}



/*******************************************************
 *  辅助函数
 *******************************************************/

// URL 编码，只针对 {}
function quoteUrl(url) {
  return url.replace(/{/g, '%7B').replace(/}/g, '%7D');
}

// 从 cookie 中提取 csrf token (ct0) - (本示例暂不使用)
function getCsrfToken(cookie) {
  const match = /ct0=([^;]+);/i.exec(cookie);
  return match ? match[1] : '';
}

// 取出视频比特率最高的链接
function getHighestVideoUrl(variants) {
  if (!variants || !Array.isArray(variants)) return '';
  let bestUrl = '';
  let maxBitrate = 0;
  for (const variant of variants) {
    if (!variant) continue;
    if (variant.bitrate && variant.url && variant.bitrate > maxBitrate) {
      maxBitrate = variant.bitrate;
      bestUrl = variant.url;
    }
  }
  // 如果所有 variant 都没有 bitrate，就使用第一条带 url 的
  if (!bestUrl && variants.length > 0) {
    for (const variant of variants) {
      if (variant && variant.url) {
        bestUrl = variant.url;
        break;
      }
    }
  }
  return bestUrl;
}

/**
 * 获取视频元信息（宽、高、时长）。
 * 这里仅示例返回 (0,0,0)。
 * 可以使用 ffprobe 或其他方式集成。
 */
async function getVideoMetadata(videoPath) {
  return [0, 0, 0];
}

// 下载文件到本地
async function downloadFileToLocal(url, savePath) {
  if (!url) {
    logWarn(`[downloadFileToLocal] URL 为空，无法下载`);
    return false;
  }
  try {
    const response = await axios.get(url, {
      responseType: 'arraybuffer',
      maxRedirects: 5
    });
    fs.mkdirSync(path.dirname(savePath), { recursive: true });
    fs.writeFileSync(savePath, response.data);
    logInfo(`[✓] 下载完成: ${savePath}`);
    return true;
  } catch (error) {
    logError(`[✗] 下载失败: ${url} -> ${savePath}, 错误: ${error}`);
    return false;
  }
}

// 解析推文ID - 增强版
function getTweetIdFromResult(tweetResult) {
  if (!tweetResult) return 'unknown_id';
  
  // 直接从content结构中提取
  if (tweetResult.content?.itemContent?.tweet_results?.result) {
    const result = tweetResult.content.itemContent.tweet_results.result;
    
    // 检查各种可能的ID路径
    if (result.rest_id) return result.rest_id;
    if (result.legacy?.id_str) return result.legacy.id_str;
    
    // 处理quote和retweet情况
    if (result.quoted_status_result?.result?.rest_id) 
      return result.quoted_status_result.result.rest_id;
    if (result.quoted_status_result?.result?.legacy?.id_str) 
      return result.quoted_status_result.result.legacy.id_str;
  }
  
  // 检查tweet结构
  if (tweetResult.tweet) {
    if (tweetResult.tweet.rest_id) return tweetResult.tweet.rest_id;
    if (tweetResult.tweet.legacy?.id_str) return tweetResult.tweet.legacy.id_str;
  }
  
  // 直接从tweet_results中提取
  if (tweetResult.tweet_results?.result) {
    const result = tweetResult.tweet_results.result;
    if (result.rest_id) return result.rest_id;
    if (result.legacy?.id_str) return result.legacy.id_str;
  }
  
  // 检查传统路径 
  if (tweetResult.legacy?.id_str) return tweetResult.legacy.id_str;
  if (tweetResult.rest_id) return tweetResult.rest_id;
  
  // 最后尝试查找任何可能的id_str属性
  if (tweetResult.id_str) return tweetResult.id_str;
  
  // 记录失败的数据结构以便调试
  try {
    logError(`无法解析的推文结构: ${JSON.stringify(tweetResult).substring(0, 200)}...`);
  } catch (e) {
    logError(`无法解析的推文结构（无法序列化）`);
  }
  
  return 'unknown_id';
}

// 从已发送消息中提取 Telegram file_id
function extractTelegramFileId(ctxMessage) {
  if (!ctxMessage) return '';
  if (ctxMessage.photo && Array.isArray(ctxMessage.photo) && ctxMessage.photo.length > 0) {
    // 最后一张photo分辨率最高
    return ctxMessage.photo[ctxMessage.photo.length - 1].file_id;
  }
  if (ctxMessage.video) {
    return ctxMessage.video.file_id;
  }
  if (ctxMessage.document) {
    return ctxMessage.document.file_id;
  }
  return '';
}

/*******************************************************
 * 解析推文数据
 *******************************************************/

function parseTweet(tweetJson, code = 200, router = null, params = null, status = 'active') {
  try {
    // 首先检查是否是直接的推文结果对象
    let tweetResult = tweetJson;
    
    // 如果是嵌套在 content.itemContent 中的结构
    if (tweetJson?.content?.itemContent?.tweet_results?.result) {
      tweetResult = tweetJson.content.itemContent.tweet_results.result;
    }
    
    // 如果是嵌套在 tweet_results.result 中的结构
    if (tweetJson?.tweet_results?.result) {
      tweetResult = tweetJson.tweet_results.result;
    }
    
    if (!tweetResult) {
      logError('无效的推文数据结构');
      return null;
    }
    
    // 处理 TweetWithVisibilityResults 类型
    if (tweetResult.__typename === 'TweetWithVisibilityResults' && tweetResult.tweet) {
      logInfo('检测到 TweetWithVisibilityResults 类型，使用内部 tweet 对象');
      tweetResult = tweetResult.tweet;
    }

    // 获取基本字段 - 使用健壮的方式
    const tweet_id = getTweetIdFromResult(tweetResult);
    if (!tweet_id || tweet_id === 'unknown_id') {
      logError('无法获取推文ID');
      return null;
    }

    // 获取作者信息 - 增强健壮性
    let author_info = null;
    
    // 尝试各种可能的路径获取作者信息
    if (tweetResult.core?.user_results?.result) {
      author_info = tweetResult.core.user_results.result;
    } else if (tweetResult.user) {
      author_info = tweetResult.user;
    } else if (tweetResult.legacy?.user) {
      author_info = tweetResult.legacy.user;
    } else if (tweetResult.user_results?.result) {
      author_info = tweetResult.user_results.result;
    }
    
    let author_id = 'unknown_author';
    if (author_info) {
      author_id = author_info.rest_id || author_info.id_str || 'unknown_author';
    }

    // 获取 legacy 数据 - 更安全的方式
    const legacy = tweetResult.legacy || {};

    // 提取基本字段
    const created_at = legacy.created_at || '';
    const text = legacy.full_text || legacy.text || '';

    // 计数
    const likes = legacy.favorite_count || 0;
    const retweets = legacy.retweet_count || 0;
    const replies = legacy.reply_count || 0;
    const quotes = legacy.quote_count || 0;
    const bookmarks = legacy.bookmark_count || 0;

    // 媒体内容
    let media = [];

    // 1. 尝试从当前推文提取
    if (legacy.extended_entities?.media) {
      media = legacy.extended_entities.media;
    } else if (legacy.entities?.media) {
      media = legacy.entities.media;
    }

    // 2. 如果没有媒体，检查是否是转推
    if (media.length === 0 && legacy.retweeted_status_result?.result) {
      const retweetLegacy = legacy.retweeted_status_result.result.legacy || {};
      if (retweetLegacy.extended_entities?.media) {
        media = retweetLegacy.extended_entities.media;
      }
    }

    // 3. 检查引用推文
    if (media.length === 0 && tweetResult.quoted_status_result?.result) {
      const quotedLegacy = tweetResult.quoted_status_result.result.legacy || {};
      if (quotedLegacy.extended_entities?.media) {
        media = quotedLegacy.extended_entities.media;
      }
    }

    // 视图数据
    let views = null;
    if (tweetResult.views?.count !== undefined) {
      try {
        views = parseInt(tweetResult.views.count, 10);
      } catch {
        views = null;
      }
    }

    // 返回解析后的数据 - 只包含数据库中存在的字段
    return {
      tweet_id,
      author_id,
      code,
      router,
      created_at,
      status,
      text,
      likes,
      retweets,
      replies,
      quotes,
      bookmarks,
      views,
      sensitive: !!legacy.possibly_sensitive,
      media,
      collected_at: new Date().toISOString()
    };
  } catch (err) {
    logError(`解析推文时出错: ${err.message}`);
    return null;
  }
}

/*******************************************************
 * Supabase 相关函数
 *******************************************************/

// 将推文数据插入(或更新)到Supabase
async function upsertTweetToSupabase(tweetData) {
  const tweet_id = tweetData?.tweet_id;
  if (!tweet_id) {
    logWarn('[upsertTweetToSupabase] 缺少 tweet_id，跳过');
    return false;
  }

  // 复制一份
  const row = { ...tweetData };
  // 移除旧的分离字段映射，现在统一使用file_id字段

  // 已知字段
  const knownFields = new Set([
    'tweet_id', 'author_id', 'text', 'created_at', 'collected_at',
    'processed_at', 'status', 'media_urls', 'caption', 'file_id', 'no_media', 
    'router', 'liked_by_ducksniff', 'sensitive',
    'code', 'likes', 'retweets', 'replies', 'quotes', 'bookmarks', 'views', 'url',
    'media'
  ]);

  // 移除未知字段
  for (const key in row) {
    if (!knownFields.has(key)) {
      // 这里仅提示信息，或可直接删除
      logInfo(`移除未知字段: ${key}`);
      delete row[key];
    }
  }

  // author_id 必填
  if (!row.author_id) {
    row.author_id = 'unknown_author';
  }

  // 处理日期字段
  const dateFields = ['created_at', 'collected_at', 'processed_at'];
  dateFields.forEach((dateField) => {
    if (row[dateField] === null || row[dateField] === '') {
      row[dateField] = null;
      return;
    }
    if (!row[dateField]) return;
    try {
      let d = new Date(row[dateField]);
      if (isNaN(d.getTime())) {
        row[dateField] = null;
      } else {
        row[dateField] = d.toISOString();
      }
    } catch {
      row[dateField] = null;
    }
  });

  // 处理 JSON 字段
  const jsonFields = ['media'];
  jsonFields.forEach((jf) => {
    if (row[jf]) {
      try {
        if (typeof row[jf] === 'object') {
          row[jf] = JSON.stringify(row[jf]);
        } else {
          JSON.parse(row[jf]); // 验证
        }
      } catch {
        row[jf] = null;
      }
    }
  });

  // 如果状态是 processed 没有 processed_at 就补上
  if (row.status === 'processed' && !row.processed_at) {
    row.processed_at = new Date().toISOString();
  }

  // 数字字段
  ['likes', 'retweets', 'replies', 'quotes', 'bookmarks', 'views', 'code'].forEach((numField) => {
    if (row[numField] !== undefined && row[numField] !== null) {
      const val = parseInt(row[numField], 10);
      if (Number.isNaN(val)) {
        row[numField] = null;
      } else {
        row[numField] = val;
      }
    }
  });

  // 布尔字段
  ['sensitive', 'liked_by_ducksniff'].forEach((boolField) => {
    if (row[boolField] !== undefined && row[boolField] !== null) {
      row[boolField] = Boolean(row[boolField]);
    }
  });

  // 如果没有 collected_at 就补一个
  if (!row.collected_at) {
    row.collected_at = new Date().toISOString();
  }

  // Upsert
  try {
    const { data, error } = await supabase.from('tweet').upsert(row).select();
    if (error) {
      throw new Error(error.message);
    }
    logInfo(`[OK] 数据Upsert成功: tweet_id=${tweet_id}`);
    return true;
  } catch (err) {
    logError(`[ERR] Upsert失败 tweet_id=${tweet_id}, error=${err.message}`);
    return false;
  }
}

// 将用户数据插入(或更新)到 Supabase
async function upsertUserToSupabase(userData) {
  const user_id = userData?.user_id;
  if (!user_id) {
    logWarn('[upsertUserToSupabase] 缺少 user_id，跳过');
    return;
  }

  const row = {
    user_id,
    screen_name: userData.screen_name,
    description: userData.description,
    profile_image: userData.profile_image,
    statuses_count: userData.statuses_count || 0,
    followers_count: userData.followers_count || 0,
    friends_count: userData.friends_count || 0,
    media_count: userData.media_count || 0,
    name: userData.name,
    followed_by_ducksniff: true,
    surveillance: true  // 同时设置监控标记
  };

  try {
    const { data, error } = await supabase.from('twitter').upsert(row).select();
    if (error) {
      throw new Error(error.message);
    }
    logInfo(`[OK] Upsert成功 user_id=${user_id}`);
  } catch (err) {
    logError(`[ERR] Upsert失败 user_id=${user_id}, error=${err.message}`);
  }
}


/*******************************************************
 * 推文处理流程 (handleTweet)
 *******************************************************/

/**
 * 处理推文数据：
 *  - 解析推文ID
 *  - （删除了已处理跳过逻辑，始终处理）
 *  - 解析并下载媒体
 *  - 发送到Telegram
 *  - 更新数据库记录
 *  - 只删除ZIP包
 */
async function handleTweet(tweetResult, chatId, ctxBot) {
  // 准备输出消息数组
  let outputMessages = [];
  outputMessages.push(`开始处理推文`);

  // 获取推文ID
  const tweet_id = getTweetIdFromResult(tweetResult);
  if (!tweet_id || tweet_id === 'unknown_id') {
    logError('无法获取推文ID');
    return '无法获取推文ID';
  }

  outputMessages[0] = `开始处理推文 ${tweet_id}`;

  // 解析数据
  const parsed_tweet = parseTweet(tweetResult, 200, null, null, 'pending');
  if (!parsed_tweet) {
    logError(`推文 ${tweet_id} 解析失败`);
    return `推文 ${tweet_id} 解析失败`;
  }

  // 确保有作者ID
  const author_id = parsed_tweet.author_id;
  if (!author_id || author_id === 'unknown') {
    logError('未找到作者ID');
    return '未找到作者ID';
  }

  // 先获取作者信息，用于文件名和caption
  let author_name = 'Unknown';
  let screen_name = 'unknown';
  
  try {
    // 从数据库查询作者信息
    const { data: userData } = await supabase
      .from('twitter')
      .select('screen_name,name')
      .eq('user_id', author_id)
      .limit(1);
    
    if (userData && userData.length > 0) {
      const info = userData[0];
      screen_name = info.screen_name || 'unknown';
      author_name = info.name || 'Unknown';
    } else {
      // 从tweetResult中提取作者信息
      if (tweetResult.core?.user_results?.result) {
        const userData = tweetResult.core.user_results.result;
        const user_legacy = userData.legacy || {};
        screen_name = user_legacy.screen_name || 'unknown';
        author_name = user_legacy.name || 'Unknown';
        
        // 保存到数据库
        const userObj = {
          user_id: userData.rest_id,
          screen_name: screen_name,
          name: author_name
        };
        await upsertUserToSupabase(userObj);
      }
    }
  } catch (err) {
    logError(`获取作者信息失败: ${err.message}`);
  }

  // 在 tweetResult 中找 user info (已在上面处理)
  if (
    tweetResult.core &&
    tweetResult.core.user_results &&
    tweetResult.core.user_results.result &&
    !author_name
  ) {
    const userData = tweetResult.core.user_results.result;
    const user_legacy = userData.legacy || {};
    const userObj = {
      user_id: userData.rest_id,
      screen_name: user_legacy.screen_name,
      name: user_legacy.name
    };
    await upsertUserToSupabase(userObj);
  }

  // 提取媒体 (图片+视频)
  let media = parsed_tweet.media || [];
  let media_urls = [];
  for (const m of media) {
    if (!m) continue;
    // 如果是 photo
    if (m.type === 'photo') {
      let url = m.media_url_https;
      // 确保最高画质
      if (url) url += '?format=jpg&name=orig';
      media_urls.push(url);
    }
    // 视频 / GIF
    else if (m.video_info) {
      const best = getHighestVideoUrl(m.video_info.variants);
      if (best) media_urls.push(best);
    } else {
      // 其他类型
      if (m.media_url_https) {
        media_urls.push(m.media_url_https);
      }
    }
  }
  parsed_tweet.media_urls = media_urls.length > 0 ? media_urls.join(';') : null;

  // 初次 upsert (状态= pending)
  await upsertTweetToSupabase(parsed_tweet);

  // 清理文件名中的非法字符
  const safeAuthorId = author_id.replace(/[^a-zA-Z0-9]/g, '_');
  // 修改：保留中文和Unicode字符，只替换文件系统不允许的字符
  const safeAuthorName = author_name
    .replace(/[\\\/\:\*\?\"\<\>\|]/g, '_') // 只替换Windows/Unix文件系统不允许的字符
    .substring(0, 30); // 允许更长的名称长度

  // 所有媒体文件将直接保存在 TEMP_DOWNLOAD_DIR 中
  let allMediaFiles = [];
  // 检查是否是来自点赞的内容
  const isLikedContent = tweetResult.__ducksniff_likes__ === true;
  
  for (let i = 0; i < media_urls.length; i++) {
    const url = media_urls[i];
    if (!url || !url.trim()) continue;
    
    // 确定文件扩展名
    let ext = '.jpg';
    if (url.toLowerCase().includes('.mp4')) {
      ext = '.mp4';
    } else if (url.includes('.jpg') || url.includes('.png')) {
      ext = url.toLowerCase().includes('.png') ? '.png' : '.jpg';
    }

    // 构建文件名：tweet_id_author_id_name_timestamp(+liked).ext
    const timestamp = Date.now();
    // 如果是点赞内容，在文件名末尾添加"_liked"
    const likedSuffix = isLikedContent ? '_liked' : '';
    const fileName = `tweet_${tweet_id}_${safeAuthorId}_${safeAuthorName}_${timestamp}${likedSuffix}${ext}`;
    const savePath = path.join(TEMP_DOWNLOAD_DIR, fileName);
    
    
    const ok = await downloadFileToLocal(url, savePath);
    if (ok) {
      allMediaFiles.push(savePath);
      outputMessages.push(`下载成功: ${fileName}`);
    } else {
      outputMessages.push(`下载失败: ${fileName}`);
    }
  }

  // 构建 caption
  const text = parsed_tweet.text || '';
  const created_at = parsed_tweet.created_at || '';

  parsed_tweet.caption = (
    `Tweet ID: ${tweet_id}\n` +
    `Created At: ${created_at}\n` +
    `\n[User Info]\n  ID: ${author_id}\n  Name: ${author_name}\n  Screen Name: @${screen_name}\n` +
    `\n[Text]\n${text}`
  );

  // 更新 caption
  await upsertTweetToSupabase(parsed_tweet);

  let fileIds = {
    file_id: null,
    no_media: null
  };

  const tweetUrl = `https://twitter.com/${screen_name}/status/${tweet_id}`;

  // 发送到 Telegram
  let messageSent = false;
  const firstMedia = allMediaFiles[0] || null;

  async function sendPhotoOrVideo(filePath, isVideo, captionText) {
    if (isVideo) {
      const [w, h, dur] = await getVideoMetadata(filePath);
      return await bot.telegram.sendVideo(chatId, { source: filePath }, {
        caption: captionText,
        supports_streaming: true,
        reply_markup: Markup.inlineKeyboard([
          [Markup.button.url('View Tweet', tweetUrl)]
        ])
      });
    } else {
      return await bot.telegram.sendPhoto(chatId, { source: filePath }, {
        caption: captionText,
        reply_markup: Markup.inlineKeyboard([
          [Markup.button.url('View Tweet', tweetUrl)]
        ])
      });
    }
  }

  // 发送到 Telegram
  let collectedFileIds = [];
  
  if (allMediaFiles.length === 0) {
    // 纯文本推文
    try {
      await bot.telegram.sendMessage(chatId, parsed_tweet.caption, {
        reply_markup: Markup.inlineKeyboard([
          [Markup.button.url('View Tweet', tweetUrl)]
        ])
      });
      messageSent = true;
      outputMessages.push('纯文本消息已发送');
      
      // 标记为无媒体推文
      fileIds.no_media = true;
    } catch (err) {
      outputMessages.push(`发送纯文本失败: ${err.message}`);
    }
  } else {
    // 有媒体的推文：总是发送媒体 + ZIP包
    
    // 1. 发送单个媒体文件（如果只有一个）或所有媒体文件
    if (allMediaFiles.length === 1) {
      const ext = path.extname(firstMedia).toLowerCase();
      const isVideo = ext === '.mp4';
      try {
        const sentMsg = await sendPhotoOrVideo(firstMedia, isVideo, parsed_tweet.caption);
        const mediaFileId = extractTelegramFileId(sentMsg);
        if (mediaFileId) {
          collectedFileIds.push(mediaFileId);
          outputMessages.push(`单个媒体文件 (${ext}) 已发送`);
        }
      } catch (err) {
        outputMessages.push(`发送单个媒体文件失败: ${err.message}`);
      }
    } else {
      // 多个媒体文件，发送第一个作为预览
      const ext = path.extname(firstMedia).toLowerCase();
      const isVideo = ext === '.mp4';
      try {
        const sentMsg = await sendPhotoOrVideo(firstMedia, isVideo, `预览 (共${allMediaFiles.length}个媒体)\n\n${parsed_tweet.caption}`);
        const mediaFileId = extractTelegramFileId(sentMsg);
        if (mediaFileId) {
          collectedFileIds.push(mediaFileId);
          outputMessages.push(`媒体预览 (${ext}) 已发送`);
        }
      } catch (err) {
        outputMessages.push(`发送媒体预览失败: ${err.message}`);
      }
    }
    
    // 2. 总是创建并发送ZIP包
    const timestamp = Date.now();
    const likedSuffix = tweetResult.__ducksniff_likes__ === true ? '_liked' : '';
    const zipFileName = `tweet_${tweet_id}_${safeAuthorId}_${safeAuthorName}_${timestamp}${likedSuffix}.zip`;
    const zipPath = path.join(TEMP_DOWNLOAD_DIR, zipFileName);

    try {
      const zip = new AdmZip();
      for (const mf of allMediaFiles) {
        zip.addLocalFile(mf);
      }
      zip.writeZip(zipPath);

      const zipMsg = await bot.telegram.sendDocument(chatId, {
        source: zipPath
      }, {
        caption: `完整媒体包 (${allMediaFiles.length}个文件)\n\n${parsed_tweet.caption}`,
        reply_markup: Markup.inlineKeyboard([
          [Markup.button.url('View Tweet', tweetUrl)]
        ])
      });
      const zipFileId = extractTelegramFileId(zipMsg);
      if (zipFileId) {
        collectedFileIds.push(zipFileId);
        outputMessages.push(`ZIP包 (${allMediaFiles.length}个文件) 已发送`);
      }
      
      messageSent = true;
    } catch (err) {
      outputMessages.push(`发送ZIP包失败: ${err.message}`);
    } finally {
      // 删除ZIP文件
      try {
        if (fs.existsSync(zipPath)) {
          fs.unlinkSync(zipPath);
          logInfo(`ZIP文件已删除: ${zipPath}`);
        }
      } catch (err) {
        logError(`删除ZIP文件时出错: ${err.message}`);
      }
    }
    
    // 3. 保存所有file_id（用分号分隔）
    if (collectedFileIds.length > 0) {
      fileIds.file_id = collectedFileIds.join(';');
    }
  }

  // 更新数据库状态 (只有在 messageSent 为 true 时才更新)
  if (messageSent) {
    try {
      const updateFields = {
        status: 'processed',
        processed_at: new Date().toISOString()
      };
      for (const [k, v] of Object.entries(fileIds)) {
        if (v) {
          updateFields[k] = v;
        }
      }
      const { data, error } = await supabase
        .from('tweet')
        .update(updateFields)
        .eq('tweet_id', tweet_id)
        .select();
      if (error) throw new Error(error.message);
      outputMessages.push('数据库状态已更新为已处理');
    } catch (err) {
      outputMessages.push(`更新数据库状态失败: ${err.message}`);
    }
  }

  return outputMessages.join('\n');
}


/*******************************************************
 * DuckSniff 点赞相关（示例）
 *******************************************************/

/**
 * 获取所有的 DuckSniff 点赞推文
 * @returns {Promise<Array>} 返回点赞推文数组
 */
async function getAllDucksniffLikes(maxPages = 1) {
  const pageText = maxPages > 100 ? "全部页面" : (maxPages > 1 ? `前${maxPages}页` : "仅第一页");
  logInfo(`开始获取DuckSniff的点赞推文数据（${pageText}）...`);
  
  const cookie = TWITTER_COOKIE_LIKES;
  if (!cookie) {
    logError("TWITTER_COOKIE_LIKES 环境变量未设置");
    return [];
  }
  
  const csrf = getCsrfToken(cookie);
  if (!csrf) {
    logError("无法从cookie中提取CSRF token");
    return [];
  }
  
  const headers = {
    "authorization": "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
    "cookie": cookie,
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "x-csrf-token": csrf,
    "referer": `https://twitter.com/${TWITTER_USERNAME}`
  };
  
  logInfo(`使用用户名: ${TWITTER_USERNAME}`);
  logInfo(`CSRF Token: ${csrf.substring(0, 10)}...`);

  // 获取rest_id
  let rest_id = null;
  let allLikesData = [];

  try {
    // 1. 首先获取用户的 rest_id
    const userVars = {
      "screen_name": TWITTER_USERNAME,
      "withSafetyModeUserFields": false
    };
    const userFeatures = {
      "hidden_profile_likes_enabled": false,
      "hidden_profile_subscriptions_enabled": false,
      "responsive_web_graphql_exclude_directive_enabled": true,
      "verified_phone_label_enabled": false,
      "subscriptions_verification_info_verified_since_enabled": true,
      "highlights_tweets_tab_ui_enabled": true,
      "creator_subscriptions_tweet_preview_api_enabled": true,
      "responsive_web_graphql_skip_user_profile_image_extensions_enabled": false,
      "responsive_web_graphql_timeline_navigation_enabled": true
    };
    const userFieldToggles = {
      "withAuxiliaryUserLabels": false
    };

    const userUrl = "https://twitter.com/i/api/graphql/xc8f1g7BYqr6VTzTbvNlGw/UserByScreenName" +
      "?variables=" + quoteUrl(JSON.stringify(userVars)) +
      "&features=" + quoteUrl(JSON.stringify(userFeatures)) +
      "&fieldToggles=" + quoteUrl(JSON.stringify(userFieldToggles));

    const userResp = await axios.get(userUrl, { headers });
    logInfo(`获取用户信息API响应状态: ${userResp.status}`);
    
    const userData = userResp.data;
    if (userData.errors) {
      logError(`获取用户信息时发生错误: ${JSON.stringify(userData.errors)}`);
      throw new Error(`API错误: ${JSON.stringify(userData.errors)}`);
    }
    
    rest_id = userData.data?.user?.result?.rest_id;
    
    if (!rest_id) {
      logError(`无法获取用户rest_id，响应数据: ${JSON.stringify(userData).substring(0, 500)}...`);
      throw new Error("无法获取用户rest_id");
    }
    logInfo(`获取到DuckSniff的rest_id: ${rest_id}`);

    // 2. 获取点赞数据
    let currentCursor = null;
    let emptyPageCount = 0; // 连续空页计数器
    const MAX_EMPTY_PAGES = 3; // 连续3页没有数据就停止
    
    for (let page = 1; page <= maxPages; page++) {
      logInfo(`正在获取第${page}页点赞...`);

      const variables = {
        "userId": rest_id,
        "count": 200,
        "cursor": currentCursor || "",
        "includePromotedContent": false,
        "withClientEventToken": false,
        "withBirdwatchNotes": false,
        "withVoice": true,
        "withV2Timeline": true
      };

    const features = {
      "responsive_web_graphql_exclude_directive_enabled": true,
      "verified_phone_label_enabled": false,
      "creator_subscriptions_tweet_preview_api_enabled": true,
      "responsive_web_graphql_timeline_navigation_enabled": true,
      "responsive_web_graphql_skip_user_profile_image_extensions_enabled": false,
      "c9s_tweet_anatomy_moderator_badge_enabled": true,
      "tweetypie_unmention_optimization_enabled": true,
      "responsive_web_edit_tweet_api_enabled": true,
      "graphql_is_translatable_rweb_tweet_is_translatable_enabled": true,
      "view_counts_everywhere_api_enabled": true,
      "longform_notetweets_consumption_enabled": true,
      "responsive_web_twitter_article_tweet_consumption_enabled": false,
      "tweet_awards_web_tipping_enabled": false,
      "freedom_of_speech_not_reach_fetch_enabled": true,
      "standardized_nudges_misinfo": true,
      "tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled": true,
      "rweb_video_timestamps_enabled": true,
      "longform_notetweets_rich_text_read_enabled": true,
      "longform_notetweets_inline_media_enabled": true,
      "responsive_web_media_download_video_enabled": false,
      "responsive_web_enhance_cards_enabled": false
    };

    const likesUrl = "https://twitter.com/i/api/graphql/-fbTO1rKPa3nO6-XIRgEFQ/Likes" +
      "?variables=" + quoteUrl(JSON.stringify(variables)) +
      "&features=" + quoteUrl(JSON.stringify(features));

    try {
      logInfo(`开始请求点赞数据，URL长度: ${likesUrl.length}`);
      const resp = await axios.get(likesUrl, { headers });
      logInfo(`点赞API响应状态: ${resp.status}`);
      
      const result = resp.data;

      if (result.errors) {
        logError(`获取点赞时发生错误: ${JSON.stringify(result.errors)}`);
        return [];
      }

      // 检查响应结构
      if (!result.data) {
        logError(`点赞API响应缺少data字段: ${JSON.stringify(result).substring(0, 500)}...`);
        return [];
      }

      if (!result.data.user) {
        logError(`点赞API响应缺少user字段: ${JSON.stringify(result.data).substring(0, 500)}...`);
        return [];
      }

      if (!result.data.user.result) {
        logError(`点赞API响应缺少result字段: ${JSON.stringify(result.data.user).substring(0, 500)}...`);
        return [];
      }

      const timeline = result.data.user.result.timeline_v2?.timeline;
      if (!timeline) {
        logError(`点赞API响应缺少timeline字段: ${JSON.stringify(result.data.user.result).substring(0, 500)}...`);
        return [];
      }

      const instructions = timeline.instructions || [];
      logInfo(`找到 ${instructions.length} 个instructions`);
      
      let entries = [];
      for (const inst of instructions) {
        if (inst.entries) {
          entries = inst.entries;
          logInfo(`从instruction中找到 ${entries.length} 个entries`);
          break;
        }
      }

      if (entries.length === 0) {
        logError(`未找到任何entries，instructions: ${JSON.stringify(instructions).substring(0, 500)}...`);
        return [];
      }

      let pageLikeCount = 0;
      let skippedCount = 0;

      for (const entry of entries) {
        const eid = entry.entryId || '';
        logInfo(`处理entry: ${eid}`);
        
        if (eid.startsWith('tweet-')) {
          const itemContent = entry.content?.itemContent;
          if (!itemContent?.tweet_results?.result) {
            logWarn(`跳过无效tweet entry: ${eid} - 缺少tweet_results`);
            skippedCount++;
            continue;
          }
          
          const tweetData = itemContent.tweet_results.result;
          
          // 标记为DuckSniff点赞
          tweetData.__ducksniff_likes__ = true;
          allLikesData.push(tweetData);
          pageLikeCount++;
          logInfo(`成功处理tweet: ${eid}`);
        } else {
          logInfo(`跳过非tweet entry: ${eid}`);
        }
      }

      logInfo(`第${page}页获取到${pageLikeCount}条点赞，跳过${skippedCount}条无效条目，总计${allLikesData.length}条`);
      
      // 检查是否为空页
      if (pageLikeCount === 0) {
        emptyPageCount++;
        logWarn(`连续${emptyPageCount}页没有获取到数据`);
        
        if (emptyPageCount >= MAX_EMPTY_PAGES) {
          logInfo(`连续${MAX_EMPTY_PAGES}页没有数据，停止获取`);
          break;
        }
      } else {
        emptyPageCount = 0; // 重置计数器
      }
      
      // 获取下一页的cursor
      let nextCursor = null;
      for (const entry of entries) {
        if (entry.entryId?.startsWith('cursor-bottom-')) {
          nextCursor = entry.content?.value;
          break;
        }
      }
      
      // 如果没有下一页或已达到最大页数，停止
      if (!nextCursor || page >= maxPages) {
        break;
      }
      
      currentCursor = nextCursor;
      
      // 页面之间添加短暂延迟，避免请求过快
      if (page < maxPages) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      } catch (err) {
        logError(`请求点赞数据失败: ${err.message}`);
        if (err.response) {
          logError(`响应状态: ${err.response.status}`);
          logError(`响应数据: ${JSON.stringify(err.response.data).substring(0, 500)}...`);
        }
        return allLikesData; // 返回已获取的数据
      }
    }

  } catch (err) {
    logError(`获取点赞失败: ${err.message}`);
    return [];
  }

  logInfo(`完成获取DuckSniff的点赞，共${allLikesData.length}条`);
  return allLikesData;
}

/**
 * 从头开始处理每个点赞，并在最前进行。
 * 这里删除"已处理跳过"逻辑，即每条都会调用 handleTweet。
 */
async function fetchDucksniffLikesOptimized(chatId, maxPages = 1) {
  const pageText = maxPages > 100 ? "全部页面" : (maxPages > 1 ? `前${maxPages}页` : "仅第一页");
  logInfo(`[fetchDucksniffLikesOptimized] 开始获取DuckSniff点赞(${pageText}，批量处理)...`);

  // 获取点赞推文
  const allLikes = await getAllDucksniffLikes(maxPages); 
  if (!allLikes || allLikes.length === 0) {
    logInfo('[fetchDucksniffLikesOptimized] 暂无点赞数据');
    await bot.telegram.sendMessage(chatId, '⚠️ 未获取到点赞数据，可能是API限制或网络问题');
    return;
  }

  // 提取所有有效的推文ID
  const tweetIds = [];
  const tweetMap = {}; // 用于映射ID到tweet对象

  for (const like of allLikes) {
    const tweetId = getTweetIdFromResult(like);
    if (tweetId && tweetId !== 'unknown_id') {
      tweetIds.push(tweetId);
      tweetMap[tweetId] = like;
    } else {
      logError(`[fetchDucksniffLikesOptimized] 无法获取推文ID: ${JSON.stringify(like).substring(0, 100)}...`);
    }
  }

  if (tweetIds.length === 0) {
    logError('[fetchDucksniffLikesOptimized] 所有推文ID获取失败');
    await bot.telegram.sendMessage(chatId, '❌ 所有推文ID获取失败，请检查日志');
    return;
  }

  logInfo(`[fetchDucksniffLikesOptimized] 获取到 ${tweetIds.length} 条有效点赞推文ID`);

  // 分批查询数据库获取已存在的推文ID和media字段
  try {
    const BATCH_SIZE = 500; // 每批查询500个ID
    const existingTweets = [];
    
    // 分批查询
    for (let i = 0; i < tweetIds.length; i += BATCH_SIZE) {
      const batch = tweetIds.slice(i, i + BATCH_SIZE);
      const { data, error } = await supabase
        .from('tweet')
        .select('tweet_id, media')
        .in('tweet_id', batch);
      
      if (error) {
        logError(`[fetchDucksniffLikesOptimized] 批量查询数据库失败 (批次 ${Math.floor(i/BATCH_SIZE) + 1}): ${error.message}`);
        await bot.telegram.sendMessage(chatId, `❌ 数据库查询失败: ${error.message}`);
        return;
      }
      
      if (data) {
        existingTweets.push(...data);
      }
      
      // 显示进度
      if ((i + BATCH_SIZE) % 2000 === 0 || i + BATCH_SIZE >= tweetIds.length) {
        logInfo(`[fetchDucksniffLikesOptimized] 已查询 ${Math.min(i + BATCH_SIZE, tweetIds.length)}/${tweetIds.length} 条推文`);
      }
    }

    // 创建已存在且已完整处理的ID集合，用于快速查找
    const existingCompleteIdSet = new Set();
    const existingIncompleteIds = [];
    
    for (const tweet of existingTweets) {
      const tweetId = tweet.tweet_id;
      const fileId = tweet.file_id;
      const noMedia = tweet.no_media;
      
      let needsProcessing = false;
      
      // 需要处理的条件：no_media为false且file_id为空
      if (noMedia === false && (!fileId || fileId === '')) {
        needsProcessing = true;
        existingIncompleteIds.push(tweetId);
      } else {
        // 已完整处理的情况：
        // 1. no_media = true (纯文本推文)
        // 2. no_media = false 且 file_id 不为空 (有媒体推文已处理)
        // 3. 其他情况视为已处理
        existingCompleteIdSet.add(tweetId);
      }
    }
    
    logInfo(`[fetchDucksniffLikesOptimized] 数据库中已存在 ${existingTweets.length} 条推文`);
    logInfo(`[fetchDucksniffLikesOptimized] 其中 ${existingCompleteIdSet.size} 条已完整处理`);
    logInfo(`[fetchDucksniffLikesOptimized] 其中 ${existingIncompleteIds.length} 条符合处理条件（no_media=false且file_id为空）`);

    // 过滤出需要处理的推文：新推文(数据库中不存在) + 符合重新处理条件的已存在推文
    const newTweetIds = tweetIds.filter(id => !existingCompleteIdSet.has(id));
    const actualNewTweetIds = newTweetIds.filter(id => !existingIncompleteIds.includes(id));
    
    logInfo(`[fetchDucksniffLikesOptimized] 需要处理 ${newTweetIds.length} 条推文:`);
    logInfo(`[fetchDucksniffLikesOptimized] - 新推文(数据库中不存在): ${actualNewTweetIds.length} 条`);
    logInfo(`[fetchDucksniffLikesOptimized] - 重新处理(no_media=false且file_id为空): ${existingIncompleteIds.length} 条`);

    // 发送初始状态消息
    if (newTweetIds.length > 0) {
      const newCount = actualNewTweetIds.length;
      const reprocessCount = existingIncompleteIds.length;
      
      let message = `🔍 发现需要处理的推文：\n`;
      if (newCount > 0) {
        message += `- 新推文(数据库中不存在): ${newCount} 条\n`;
      }
      if (reprocessCount > 0) {
        message += `- 重新处理(no_media=false且file_id为空): ${reprocessCount} 条\n`;
      }
      message += `总计: ${newTweetIds.length} 条，开始处理...`;
      
      await bot.telegram.sendMessage(chatId, message);
    } else {
      await bot.telegram.sendMessage(chatId, `✅ 所有点赞推文都已完整处理，数据库包含 ${existingCompleteIdSet.size} 条完整记录`);
      return;
    }

    // 处理计数器
    let successCount = 0;
    let failCount = 0;

    // 处理新推文
    for (const tweetId of newTweetIds) {
      try {
        const tweet = tweetMap[tweetId];
        const resMsg = await handleTweet(tweet, chatId, bot);
        logInfo(`[likeTweet] ${tweetId}: ${resMsg}`);
        
        // 检查是否处理失败
        if (resMsg.includes('失败') || resMsg.includes('无法')) {
          failCount++;
        } else {
          successCount++;
        }
      } catch (err) {
        logError(`[likeTweet] 处理点赞推文 ${tweetId} 出错: ${err.message}`);
        // 保存处理失败的推文数据
        const tweet = tweetMap[tweetId];
        if (tweet) {
        }
        failCount++;
      }
    }

    // 通知完成
    const newCount = actualNewTweetIds.length;
    const reprocessCount = existingIncompleteIds.length;
    
    let completeMsg = `✅ 点赞处理完成\n` +
      `📊 统计:\n` +
      `- 总点赞数: ${tweetIds.length}\n` +
      `- 已完整处理: ${existingCompleteIdSet.size}\n` +
      `- 本次新处理: ${newCount}\n` +
      `- 本次重新处理: ${reprocessCount}\n` +
      `- 成功: ${successCount}\n` +
      `- 失败: ${failCount}`;
    
    
    await bot.telegram.sendMessage(chatId, completeMsg);
  } catch (err) {
    logError(`[fetchDucksniffLikesOptimized] 处理点赞数据时出错: ${err.message}`);
    await bot.telegram.sendMessage(chatId, `❌ 处理点赞数据时出错: ${err.message}`);
  }
}


// 临时的测试函数，用于验证数据结构
async function testParseSingleLike() {
  console.log('开始测试单个点赞推文解析...');
  
  // 获取一条点赞数据
  const likes = await getAllDucksniffLikes(1);
  if (likes.length === 0) {
    console.log('没有获取到点赞数据');
    return;
  }
  
  const firstLike = likes[0];
  console.log('\n第一条点赞数据:');
  console.log('类型:', typeof firstLike);
  console.log('顶层键:', Object.keys(firstLike));
  
  // 尝试使用parseTweet解析
  console.log('\n尝试解析:');
  const parsed = parseTweet(firstLike);
  
  if (parsed) {
    console.log('✓ 解析成功');
    console.log('- tweet_id:', parsed.tweet_id);
    console.log('- author_id:', parsed.author_id);
    console.log('- created_at:', parsed.created_at);
    console.log('- text:', parsed.text?.substring(0, 50) + '...');
    console.log('- media数量:', parsed.media?.length || 0);
    
    if (parsed.media && parsed.media.length > 0) {
      console.log('\n媒体详情:');
      parsed.media.forEach((m, i) => {
        console.log(`  媒体${i+1}:`, {
          type: m.type,
          url: m.media_url_https || m.url,
          hasVideoInfo: !!m.video_info
        });
      });
    }
  } else {
    console.log('❌ 解析失败');
  }
}

/*******************************************************
 * DuckSniff 关注列表获取（示例）
 *******************************************************/
async function fetchDucksniffFollowingsPage(cursor) {
  const params = { screen_name: DUCKSNIFF_SCREEN_NAME };
  if (cursor) {
    params.cursor = cursor;
  }

  const headers = {
    accept: 'application/json',
    Authorization: DUCKSNIFF_FOLLOWINGS_AUTH
  };

  try {
    const resp = await axios.get(DUCKSNIFF_FOLLOWINGS_API, { headers, params });
    const data = resp.data;
    if (!data.data) {
      logError('[fetchDucksniffFollowingsPage] 响应无 data');
      return null;
    }
    const followings = data.data.following || [];
    
    // 检查用户是否存在，不存在才插入
    for (const userInfo of followings) {
      if (!userInfo.user_id) continue;
      
      // 为每个用户同时设置followed_by_ducksniff和surveillance为true
      userInfo.followed_by_ducksniff = true;
      userInfo.surveillance = true;
      
      // 先检查用户是否存在
      const { data: existingUser, error: checkError } = await supabase
        .from('twitter')
        .select('user_id')
        .eq('user_id', userInfo.user_id)
        .limit(1);
        
      if (checkError) {
        logError(`检查用户存在性失败: ${checkError.message}`);
        continue;
      }
      
      // 用户不存在才插入，存在则更新
      if (!existingUser || existingUser.length === 0) {
        await upsertUserToSupabase(userInfo);
      } else {
        // 更新已存在用户的surveillance状态
        const { error: updateError } = await supabase
          .from('twitter')
          .update({ 
            followed_by_ducksniff: true,
            surveillance: true 
          })
          .eq('user_id', userInfo.user_id);
          
        if (updateError) {
          logError(`更新用户状态失败: ${updateError.message}`);
        } else {
          logInfo(`已更新用户 ${userInfo.screen_name || userInfo.user_id} 的状态`);
        }
      }
    }
    
    const more_users = data.data.more_users;
    if (more_users) {
      return data.data.next_cursor;
    }
    return null;
  } catch (err) {
    logError(`[fetchDucksniffFollowingsPage] 请求失败: ${err.message}`);
    return null;
  }
}

async function fetchDucksniffFollowings() {
  logInfo('[fetchDucksniffFollowings] 开始获取 DuckSniff 关注列表前3页...');
  let cursor = null;
  let pageCount = 0;
  const MAX_PAGES = 3;
  
  while (pageCount < MAX_PAGES) {
    pageCount++;
    logInfo(`[fetchDucksniffFollowings] 正在处理第 ${pageCount} 页...`);
    
    let newCursor = await fetchDucksniffFollowingsPage(cursor);
    if (!newCursor) break;
    
    cursor = newCursor;
    await sleep(1000);
  }
  
  logInfo(`[fetchDucksniffFollowings] 完成处理 ${pageCount} 页数据！`);
}


/*******************************************************
 * 监控用户相关
 *******************************************************/

async function getSurveillanceUsers() {
  try {
    const { data, error } = await supabase
      .from('twitter')
      .select('screen_name,user_id')
      .eq('surveillance', true);
    if (error) throw new Error(error.message);

    let users = {};
    data.forEach((item) => {
      if (item.screen_name) {
        users[item.screen_name] = {
          user_id: item.user_id
        };
      }
    });
    return users;
  } catch (err) {
    logError(`[getSurveillanceUsers] 失败: ${err.message}`);
    return {};
  }
}

async function updateUserSurveillance(screenName, value = true) {
  try {
    const { data: checkData, error: checkErr } = await supabase
      .from('twitter')
      .select('user_id')
      .eq('screen_name', screenName);
    if (checkErr) {
      throw new Error(checkErr.message);
    }
    if (!checkData || checkData.length === 0) {
      return [false, '用户不存在于数据库中'];
    }

    const { error: updErr } = await supabase
      .from('twitter')
      .update({ surveillance: value })
      .eq('screen_name', screenName);

    if (updErr) {
      throw new Error(updErr.message);
    }
    return [true, `已${value ? '开启' : '关闭'}对 @${screenName} 的监控`];
  } catch (err) {
    logError(`[updateUserSurveillance] 失败: ${err.message}`);
    return [false, `更新失败: ${err.message}`];
  }
}


/*******************************************************
 * 循环任务调度 (sequential_listener)
 *******************************************************/

async function sequentialListener() {
  logInfo('已启动点赞监听服务，将以1小时为周期检测新增点赞');
  
  // 发送启动通知
  try {
    await bot.telegram.sendMessage(
      TWITTER_FIXED_CHAT_ID, 
      '🔄 已启动点赞监听服务\n⏱️ 设置为1小时检测一次\n👍 仅处理新增点赞作品'
    );
  } catch (err) {
    logError(`[sequentialListener] 发送启动通知失败: ${err.message}`);
  }

  while (true) {
    try {
      // 记录开始时间
      const startTime = new Date();
      logInfo(`[sequentialListener] 开始新一轮点赞检测，时间: ${startTime.toISOString()}`);
      
      // 处理DuckSniff点赞（从头开始）- 定期检查只获取第一页
      await fetchDucksniffLikesOptimized(TWITTER_FIXED_CHAT_ID, 1);
      
      // 检测结束通知
      const endTime = new Date();
      const duration = Math.round((endTime - startTime) / 1000); // 秒数
      const message = `✅ 点赞检测完成\n⏱️ 耗时: ${duration}秒\n⏳ 将在1小时后再次检测`;
      
      try {
        await bot.telegram.sendMessage(TWITTER_FIXED_CHAT_ID, message);
      } catch (err) {
        logError(`[sequentialListener] 发送完成通知失败: ${err.message}`);
      }

      // 等待1小时
      logInfo(`[sequentialListener] 检测完成，将在1小时后再次运行`);
      await sleep(60 * 60 * 1000); // 1小时 = 60分钟 * 60秒 * 1000毫秒
    } catch (err) {
      logError(`[sequentialListener] 异常: ${err.message}`);
      // 发生错误也等待1小时再次尝试
      await sleep(60 * 60 * 1000);
    }
  }
}

async function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}


/*******************************************************
 * Telegram Bot 命令 & 事件绑定 (Telegraf)
 *******************************************************/

bot.on('message', async (ctx) => {
  try {
    if (String(ctx.chat.id) !== String(TWITTER_FIXED_CHAT_ID)) {
      return;
    }

    const text = ctx.message.text?.trim() || '';
    if (!text) return;

    // /check
    if (text === '/check') {
      await ctx.reply(
        '当前模式为: 仅监控点赞作品，每小时检测一次新增点赞\n\n' +
        '可用命令:\n' +
        '/likes - 立即获取第一页点赞\n' +
        '/all - 获取全部点赞页面\n' +
        '/test - 测试数据结构解析\n' +
        '/failed - 查看失败的推文记录\n' +
        '/status - 查看点赞统计状态\n' +
        '/download <tweet_id> - 下载指定推文的媒体\n\n' +
        '直接输入:\n' +
        '• 推文ID (如: 1234567890) - 查看推文信息\n' +
        '• 推文URL - 查看推文信息\n\n' +
        '启动参数:\n' +
        'node twitter.js - 默认启动\n' +
        'node twitter.js 3 - 启动时获取前3页\n' +
        'node twitter.js all - 启动时获取全部页面\n\n' +
        '⚠️ 注意: 连续3页没有数据时会自动停止获取'
      );
      return;
    }

    // /status => 示例
    if (text === '/status') {
      await ctx.reply('正在获取DuckSniff点赞统计状态...');
      // 省略 getLikesStatus() 实现
      const msg = '点赞状态信息...（此处省略）';
      await ctx.reply(msg);
      return;
    }

    // /likes
    if (text === '/likes') {
      await ctx.reply('开始立即获取 DuckSniff 的点赞列表（并从头处理）...');
      await fetchDucksniffLikesOptimized(TWITTER_FIXED_CHAT_ID, 1);
      return;
    }
    
    // /test - 测试单个点赞解析
    if (text === '/test') {
      await ctx.reply('运行测试：检查点赞推文数据结构...');
      await testParseSingleLike();
      await ctx.reply('测试完成，请查看控制台日志');
      return;
    }
    
    // /failed - 查看失败的推文列表
    if (text === '/failed') {
      try {
        const files = fs.readdirSync(FAILED_JSON_DIR);
        if (files.length === 0) {
          await ctx.reply('✅ 没有失败的推文记录');
          return;
        }
        
        let msg = `📋 失败的推文记录 (共${files.length}条):\n\n`;
        
        // 只显示最近10条
        const recentFiles = files.slice(-10);
        for (const file of recentFiles) {
          const filePath = path.join(FAILED_JSON_DIR, file);
          try {
            const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
            msg += `• ${data.tweet_id}\n`;
            msg += `  错误: ${data.error}\n`;
            msg += `  时间: ${data.timestamp}\n\n`;
          } catch (e) {
            // 忽略读取错误的文件
          }
        }
        
        if (files.length > 10) {
          msg += `\n... 还有 ${files.length - 10} 条记录\n`;
        }
        
        msg += `\n📁 完整记录保存在:\n${FAILED_JSON_DIR}`;
        
        await ctx.reply(msg);
      } catch (err) {
        await ctx.reply(`❌ 读取失败记录时出错: ${err.message}`);
      }
      return;
    }
    
    // /all - 获取全部点赞页面
    if (text === '/all') {
      await ctx.reply('开始获取 DuckSniff 的全部点赞页面（这可能需要较长时间）...');
      await fetchDucksniffLikesOptimized(TWITTER_FIXED_CHAT_ID, 999);
      return;
    }
    
    // /download <tweet_id> - 下载指定推文的媒体
    if (text.startsWith('/download ')) {
      const tweetId = text.replace('/download ', '').trim();
      if (!/^\d+$/.test(tweetId)) {
        await ctx.reply('❌ 请提供有效的推文ID');
        return;
      }
      
      await ctx.reply(`📥 开始下载推文 ${tweetId} 的媒体...`);
      
      // 获取推文数据
      const tweetData = await getTweetById(tweetId);
      if (!tweetData) {
        await ctx.reply('❌ 获取推文失败');
        return;
      }
      
      // 处理推文（下载媒体并发送）
      try {
        const result = await handleTweet(tweetData, ctx.chat.id, bot);
        await ctx.reply(`✅ 处理完成:\n${result}`);
      } catch (err) {
        await ctx.reply(`❌ 处理失败: ${err.message}`);
      }
      
      return;
    }

    // 如果是推文 URL 或 ID
    if (/^https?:\/\//i.test(text) || /^\d+$/.test(text)) {
      let tweetId = text;
      
      // 从URL中提取tweet ID
      if (text.includes('twitter.com') || text.includes('x.com')) {
        const match = text.match(/status\/(\d+)/);
        if (match) {
          tweetId = match[1];
        } else {
          await ctx.reply('❌ 无法从URL中提取推文ID');
          return;
        }
      }
      
      await ctx.reply(`🔍 正在获取推文 ${tweetId} 的信息...`);
      
      // 获取推文数据
      const tweetData = await getTweetById(tweetId);
      if (!tweetData) {
        await ctx.reply('❌ 获取推文失败，可能是ID无效或API限制');
        return;
      }
      
      // 解析并处理推文
      const parsed = parseTweet(tweetData);
      if (parsed) {
        await ctx.reply(
          `✅ 推文信息:\n\n` +
          `ID: ${parsed.tweet_id}\n` +
          `作者ID: ${parsed.author_id}\n` +
          `创建时间: ${parsed.created_at}\n` +
          `文本: ${parsed.text?.substring(0, 100)}${parsed.text?.length > 100 ? '...' : ''}\n` +
          `媒体数量: ${parsed.media?.length || 0}\n` +
          `点赞: ${parsed.likes} | 转推: ${parsed.retweets} | 回复: ${parsed.replies}\n\n` +
          `是否要下载并发送媒体文件？请回复 /download ${tweetId}`
        );
        
        // 如果有媒体，显示媒体详情
        if (parsed.media && parsed.media.length > 0) {
          let mediaInfo = '媒体详情:\n';
          parsed.media.forEach((m, i) => {
            mediaInfo += `${i+1}. ${m.type}`;
            if (m.type === 'video' && m.video_info?.variants) {
              const variants = m.video_info.variants.filter(v => v.bitrate);
              if (variants.length > 0) {
                const maxBitrate = Math.max(...variants.map(v => v.bitrate));
                mediaInfo += ` (最高比特率: ${maxBitrate})`;
              }
            }
            mediaInfo += '\n';
          });
          await ctx.reply(mediaInfo);
        }
      } else {
        await ctx.reply('❌ 无法解析推文数据');
      }
      
      return;
    }

    // 不再处理用户监控相关命令
    if (text.startsWith('@') || text.startsWith('/media') || text === '/get' || text === '/sync') {
      await ctx.reply('⚠️ 当前模式已设置为仅监控点赞作品，暂不接受用户监控和媒体获取命令。');
      return;
    }

  } catch (err) {
    logError(`[on_message] 处理消息出错: ${err.message}`);
  }
});

// 回调查询
bot.on('callback_query', async (ctx) => {
  try {
    const data = ctx.callbackQuery.data || '';
    
    // 我们不再支持删除监控用户的操作
    if (data.startsWith('delete_')) {
      await ctx.answerCbQuery('当前模式为仅监控点赞，不支持用户监控操作');
      await ctx.editMessageText('⚠️ 当前模式已设置为仅监控点赞作品，不支持用户监控操作');
      return;
    }
    
    // 未知操作
    await ctx.answerCbQuery('未知操作');
  } catch (err) {
    logError(`[on_callback_query] 处理回调失败: ${err.message}`);
  }
});


/*******************************************************
 * 示例： fetchNewTweetsIncremental (供监控用户使用)
 *******************************************************/
async function fetchNewTweetsIncremental(username, chatId) {
  // 检查用户是否存在及其once状态
  let userId = null;
  let isOnce = false;
  try {
    const { data, error } = await supabase
      .from('twitter')
      .select('user_id, once')
      .eq('screen_name', username)
      .limit(1);
    
    if (error) {
      logError(`获取用户ID失败: ${error.message}`);
      return;
    }
    
    if (!data || data.length === 0) {
      logError(`用户 @${username} 不存在于数据库中`);
      return;
    }
    
    userId = data[0].user_id;
    isOnce = data[0].once === true;
    
    if (isOnce) {
      logInfo(`[fetchNewTweetsIncremental] 检查用户 @${username} 的新推文 (增量处理，仅第一页)...`);
    } else {
      logInfo(`[fetchNewTweetsIncremental] 检查用户 @${username} 的新推文 (全量处理)...`);
    }
  } catch (err) {
    logError(`查询用户数据出错: ${err.message}`);
    return;
  }
  
  // 如果once为false，使用fetchUserMediaTweets获取全部媒体推文
  if (!isOnce) {
    logInfo(`[fetchNewTweetsIncremental] 用户 @${username} 的once为false, 使用fetchUserMediaTweets获取全部媒体推文`);
    try {
      await fetchUserMediaTweets(username, true, chatId);
      
      // 更新用户的once状态为true
      try {
        const { error: updateError } = await supabase
          .from('twitter')
          .update({ once: true })
          .eq('screen_name', username);
        
        if (updateError) {
          logError(`更新用户once字段失败: ${updateError.message}`);
        } else {
          logInfo(`[fetchNewTweetsIncremental] 已将用户 @${username} 的once字段设置为true`);
        }
        
        await bot.telegram.sendMessage(chatId, `用户 @${username} 全量媒体推文抓取完成，once标记已更新。`);
      } catch (err) {
        logError(`更新用户once字段时出错: ${err.message}`);
      }
      return;
    } catch (err) {
      logError(`获取全部媒体推文失败: ${err.message}`);
      // 失败后继续原有的增量处理逻辑
    }
  }
  
  // 实际获取用户推文的函数，在这里应该实现真正的API调用
  // 这个函数应该根据isOnce决定获取全部数据还是仅第一页
  async function getUserTweets(screenName, userId, onlyFirstPage = false) {
    // 实际实现应该调用Twitter API获取用户推文
    // 以下仅为示例，返回一条模拟数据
    return [{
      rest_id: '1234567890',
      content: {
        itemContent: {
          tweet_results: {
            result: {
              rest_id: '1234567890',
              core: {
                user_results: {
                  result: {
                    rest_id: userId || '111222333',
                    legacy: {
                      screen_name: screenName,
                      name: `MockNameFor_${screenName}`
                    }
                  }
                }
              },
              legacy: {
                id_str: '1234567890',
                created_at: new Date().toISOString(),
                full_text: '这是模拟的一条推文文本 for incremental fetch...',
                favorite_count: 0,
                retweet_count: 0,
                reply_count: 0,
                quote_count: 0,
                bookmark_count: 0,
                lang: 'zh',
                possibly_sensitive: false,
                extended_entities: {
                  media: []
                }
              }
            }
          }
        }
      }
    }];
  }
  
  // 以下是原有的增量处理逻辑，保持不变
  // 获取用户推文
  const tweets = await getUserTweets(username, userId, isOnce);
  if (!tweets || tweets.length === 0) {
    logInfo(`[fetchNewTweetsIncremental] 用户 @${username} 没有推文`);
    return;
  }
  
  // 提取所有有效的推文ID
  const tweetIds = [];
  const tweetMap = {}; // 用于映射ID到tweet对象
  
  for (const tweet of tweets) {
    const tweetId = getTweetIdFromResult(tweet);
    if (tweetId && tweetId !== 'unknown_id') {
      tweetIds.push(tweetId);
      tweetMap[tweetId] = tweet;
    } else {
      logError(`[fetchNewTweetsIncremental] 无法获取推文ID: ${JSON.stringify(tweet).substring(0, 100)}...`);
    }
  }
  
  if (tweetIds.length === 0) {
    logError(`[fetchNewTweetsIncremental] 所有推文ID获取失败，用户 @${username}`);
    return;
  }
  
  logInfo(`[fetchNewTweetsIncremental] 获取到 ${tweetIds.length} 条推文ID，用户 @${username}`);
  
  // 一次性查询数据库获取已存在的推文ID
  try {
    const { data: existingTweets, error } = await supabase
      .from('tweet')
      .select('tweet_id')
      .in('tweet_id', tweetIds);
    
    if (error) {
      logError(`[fetchNewTweetsIncremental] 批量查询数据库失败: ${error.message}`);
      return;
    }
    
    // 创建已存在ID的集合，用于快速查找
    const existingIdSet = new Set(existingTweets.map(t => t.tweet_id));
    logInfo(`[fetchNewTweetsIncremental] 数据库中已存在 ${existingIdSet.size} 条推文`);
    
    // 过滤出需要处理的新推文
    const newTweetIds = tweetIds.filter(id => !existingIdSet.has(id));
    logInfo(`[fetchNewTweetsIncremental] 需要处理 ${newTweetIds.length} 条新推文`);
    
    // 处理新推文
    for (const tweetId of newTweetIds) {
      try {
        const tweet = tweetMap[tweetId];
        const resMsg = await handleTweet(tweet, chatId, bot);
        logInfo(`[userTweet] ${tweetId}: ${resMsg}`);
      } catch (err) {
        logError(`[userTweet] 处理用户推文 ${tweetId} 出错: ${err.message}`);
      }
    }
    
    // 如果是全量处理(once为false)，处理完成后设置once标记为true
    if (!isOnce) {
      try {
        const { error: updateError } = await supabase
          .from('twitter')
          .update({ once: true })
          .eq('screen_name', username);
        
        if (updateError) {
          logError(`更新用户once字段失败: ${updateError.message}`);
        } else {
          logInfo(`[fetchNewTweetsIncremental] 已将用户 @${username} 的once字段设置为true`);
        }
        
        await bot.telegram.sendMessage(chatId, `[fetchNewTweetsIncremental] @${username} 全量抓取完成，once标记已更新。共处理 ${tweetIds.length} 条推文，其中 ${newTweetIds.length} 条新推文。`);
      } catch (err) {
        logError(`更新用户once字段时出错: ${err.message}`);
      }
    } else {
      // 增量处理完成的消息
      await bot.telegram.sendMessage(chatId, `[fetchNewTweetsIncremental] @${username} 增量抓取完成(仅第一页)。共处理 ${tweetIds.length} 条推文，其中 ${newTweetIds.length} 条新推文。`);
    }
  } catch (err) {
    logError(`[fetchNewTweetsIncremental] 处理用户推文时出错: ${err.message}`);
  }
}


/*******************************************************
 * 主函数入口 - 启动Bot
 *******************************************************/
async function verifyDatabaseTables() {
  logInfo('[verifyDatabaseTables] 验证数据库表结构...');
  try {
    // 验证 tweet 表
    const { data: tweetCheck, error: tweetError } = await supabase
      .from('tweet')
      .select('tweet_id', { count: 'exact', head: true });
    if (tweetError) throw new Error(`tweet表验证失败: ${tweetError.message}`);
    
    // 验证 twitter 表
    const { data: twitterCheck, error: twitterError } = await supabase
      .from('twitter')
      .select('user_id', { count: 'exact', head: true });
    if (twitterError) throw new Error(`twitter表验证失败: ${twitterError.message}`);
    
    // 验证 does 表
    const { data: doesCheck, error: doesError } = await supabase
      .from('does')
      .select('x_follow_refresh', { count: 'exact', head: true });
    if (doesError) throw new Error(`does表验证失败: ${doesError.message}`);
    
    logInfo('[verifyDatabaseTables] 数据库表验证成功');
    return true;
  } catch (err) {
    logError(`[verifyDatabaseTables] 失败: ${err.message}`);
    return false;
  }
}

// 添加 Supabase 实时订阅功能
async function setupDoesTableSubscription() {
  logInfo('[setupDoesTableSubscription] 开始监听 does 表的 x_follow_refresh 变化...');
  
  try {
    const subscription = supabase
      .channel('does_changes')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'does',
          filter: 'x_follow_refresh=true'
        },
        async (payload) => {
          logInfo('[does_subscription] 检测到 x_follow_refresh 变为 true，开始更新关注列表...');
          
          // 获取第一页关注列表并设置监控
          await fetchDucksniffFollowingsFirstPage();
          
          // 重置 x_follow_refresh 标志
          try {
            const { error } = await supabase
              .from('does')
              .update({ x_follow_refresh: false })
              .eq('id', payload.new.id);
              
            if (error) {
              throw new Error(error.message);
            }
            logInfo('[does_subscription] 已重置 x_follow_refresh 标志');
          } catch (err) {
            logError(`[does_subscription] 重置 x_follow_refresh 失败: ${err.message}`);
          }
        }
      )
      .subscribe();
      
    logInfo('[setupDoesTableSubscription] 订阅设置成功');
    return subscription;
  } catch (err) {
    logError(`[setupDoesTableSubscription] 设置订阅失败: ${err.message}`);
    return null;
  }
}

// 只获取第一页关注列表的函数
async function fetchDucksniffFollowingsFirstPage() {
  logInfo('[fetchDucksniffFollowingsFirstPage] 开始获取 DuckSniff 关注列表第一页...');
  try {
    await fetchDucksniffFollowingsPage(null); // 传入 null 表示获取第一页
    logInfo('[fetchDucksniffFollowingsFirstPage] 完成处理第一页数据');
  } catch (err) {
    logError(`[fetchDucksniffFollowingsFirstPage] 获取失败: ${err.message}`);
  }
}

async function startupCheck() {
  logInfo('[startupCheck] 程序启动，进行初始检查...');
  
  // 验证数据库表
  const dbOk = await verifyDatabaseTables();
  if (!dbOk) {
    logError('[startupCheck] 数据库表验证失败');
    process.exit(1);
  }

  logInfo('[startupCheck] 初始检查完成');
}

async function main() {
  // 获取命令行参数
  const args = process.argv.slice(2);
  let initialPages = 1;
  let getAllPages = false;
  
  // 处理参数
  if (args.length > 0) {
    if (args[0].toLowerCase() === 'all') {
      getAllPages = true;
      logInfo('启动参数: all - 将获取全部点赞页面');
    } else {
      const pageNum = parseInt(args[0]);
      if (!isNaN(pageNum) && pageNum > 0) {
        initialPages = pageNum;
        logInfo(`启动参数: 初始获取 ${initialPages} 页点赞数据`);
      }
    }
  }
  
  // 先做启动检查
  await startupCheck();

  // 显示 Bot 信息
  await displayBotInfo();

  // 设置 does 表的订阅
  const subscription = await setupDoesTableSubscription();
  if (!subscription) {
    logError('设置 does 表订阅失败，程序将退出');
    process.exit(1);
  }

  // 直接启动监控循环
  monitoringStarted = true;
  logInfo('开始监控 DuckSniff 点赞，每小时检测一次新增点赞作品...');
  
  // 根据参数执行初始获取
  if (getAllPages) {
    logInfo('启动时执行获取全部点赞页面...');
    await fetchDucksniffLikesOptimized(TWITTER_FIXED_CHAT_ID, 999); // 使用一个很大的数字来获取所有页面
  } else if (initialPages > 1) {
    logInfo(`启动时执行初始 ${initialPages} 页点赞数据获取...`);
    await fetchDucksniffLikesOptimized(TWITTER_FIXED_CHAT_ID, initialPages);
  }
  
  sequentialListener().catch(err => {
    logError(`[sequentialListener] 错误: ${err.message}`);
  });

  // 启动 Telegraf Bot
  bot.launch().then(() => {
    console.log('\nTelegram Bot 已启动...');
    console.log('按 Ctrl+C 停止运行。');
  }).catch((err) => {
    logError(`Bot launch failed: ${err.message}`);
  });

  // 如果需要捕获停止信号
  process.once('SIGINT', () => {
    subscription.unsubscribe();
    bot.stop('SIGINT');
  });
  process.once('SIGTERM', () => {
    subscription.unsubscribe();
    bot.stop('SIGTERM');
  });
}

// 运行主函数
main().catch((err) => {
  logError(`[main] 未捕获异常: ${err.message}`);
  process.exit(1);
});

/*******************************************************
 * 同步已有用户监控状态
 *******************************************************/
async function syncExistingUsersSurveillance() {
  logInfo('[syncExistingUsersSurveillance] 开始同步已有用户的监控状态...');
  
  try {
    // 查找所有被DuckSniff关注但未被监控的用户
    const { data, error } = await supabase
      .from('twitter')
      .select('screen_name, user_id')
      .eq('followed_by_ducksniff', true)
      .or('surveillance.is.null, surveillance.eq.false');
      
    if (error) {
      throw new Error(error.message);
    }
    
    if (!data || data.length === 0) {
      logInfo('[syncExistingUsersSurveillance] 没有需要同步的用户');
      return 0;
    }
    
    logInfo(`[syncExistingUsersSurveillance] 找到 ${data.length} 个需要同步的用户`);
    
    // 更新这些用户的监控状态
    for (const user of data) {
      try {
        const { error: updateError } = await supabase
          .from('twitter')
          .update({ surveillance: true })
          .eq('user_id', user.user_id);
          
        if (updateError) {
          logError(`更新用户 @${user.screen_name} 的监控状态失败: ${updateError.message}`);
        } else {
          logInfo(`已将用户 @${user.screen_name} 的监控状态同步为true`);
        }
      } catch (err) {
        logError(`处理用户 @${user.screen_name} 时出错: ${err.message}`);
      }
    }
    
    return data.length;
  } catch (err) {
    logError(`[syncExistingUsersSurveillance] 同步用户监控状态失败: ${err.message}`);
    return -1;
  }
}

/*******************************************************
 * 获取用户媒体推文
 *******************************************************/

/**
 * 获取指定用户的媒体推文（全部或仅第一页）
 * @param {string} screenName - 用户名
 * @param {boolean} allPages - 是否获取全部页面 true=全部页面，false=仅第一页
 * @param {string} chatId - Telegram聊天ID(用于发送消息)
 * @returns {Promise<Array>} 返回媒体推文数组
 */
async function fetchUserMediaTweets(screenName, allPages = false, chatId = null) {
  // 使用环境变量中的TWITTER_COOKIE_USER
  const cookie = process.env.TWITTER_COOKIE_USER;
  if (!cookie) {
    const errorMsg = `[fetchUserMediaTweets] 环境变量 TWITTER_COOKIE_USER 未设置`;
    logError(errorMsg);
    if (chatId) {
      await bot.telegram.sendMessage(chatId, errorMsg);
    }
    return [];
  }
  
  // 提取csrf token
  const csrf = getCsrfToken(cookie);
  if (!csrf) {
    const errorMsg = `[fetchUserMediaTweets] 无法从cookie中提取csrf token`;
    logError(errorMsg);
    if (chatId) {
      await bot.telegram.sendMessage(chatId, errorMsg);
    }
    return [];
  }
  
  logInfo(`[fetchUserMediaTweets] 开始获取用户 @${screenName} 的媒体推文${allPages ? '(全部页面)' : '(仅第一页)'}...`);
  
  // 先获取用户信息(rest_id)
  let userId = null;
  let userName = null;
  
  try {
    // 从数据库查询用户ID
    const { data: userData, error } = await supabase
      .from('twitter')
      .select('user_id, name')
      .eq('screen_name', screenName)
      .limit(1);
      
    if (error) {
      throw new Error(error.message);
    }
    
    if (userData && userData.length > 0) {
      userId = userData[0].user_id;
      userName = userData[0].name;
      logInfo(`[fetchUserMediaTweets] 从数据库获取到用户ID: ${userId}, 名称: ${userName}`);
    } else {
      // 如果数据库没有，则调用API获取
      logInfo(`[fetchUserMediaTweets] 数据库中无用户信息，调用API获取`);
      
      const userVars = {
        "screen_name": screenName,
        "withSafetyModeUserFields": false
      };
      const userFeatures = {
        "hidden_profile_likes_enabled": false,
        "hidden_profile_subscriptions_enabled": false,
        "responsive_web_graphql_exclude_directive_enabled": true,
        "verified_phone_label_enabled": false,
        "subscriptions_verification_info_verified_since_enabled": true,
        "highlights_tweets_tab_ui_enabled": true,
        "creator_subscriptions_tweet_preview_api_enabled": true,
        "responsive_web_graphql_skip_user_profile_image_extensions_enabled": false,
        "responsive_web_graphql_timeline_navigation_enabled": true
      };
      const userFieldToggles = {
        "withAuxiliaryUserLabels": false
      };

      // 正确编码URL参数
      const userUrl = "https://twitter.com/i/api/graphql/xc8f1g7BYqr6VTzTbvNlGw/UserByScreenName" +
        "?variables=" + encodeURIComponent(JSON.stringify(userVars)) +
        "&features=" + encodeURIComponent(JSON.stringify(userFeatures)) +
        "&fieldToggles=" + encodeURIComponent(JSON.stringify(userFieldToggles));
        
      const headers = {
        "authorization": "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
        "cookie": cookie,
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-csrf-token": csrf,
        "referer": `https://twitter.com/${screenName}`
      };

      try {
        const userResp = await axios.get(userUrl, { headers });
        logInfo(`[fetchUserMediaTweets] 获取用户API响应状态码: ${userResp.status}`);
        
        const userData = userResp.data;
        
        if (userData.data?.user?.result) {
          const userResult = userData.data.user.result;
          userId = userResult.rest_id;
          userName = userResult.legacy?.name || screenName;
          
          // 保存用户信息到数据库
          const userObj = {
            user_id: userId,
            screen_name: screenName,
            name: userName,
            description: userResult.legacy?.description || '',
            followers_count: userResult.legacy?.followers_count || 0,
            friends_count: userResult.legacy?.friends_count || 0,
            statuses_count: userResult.legacy?.statuses_count || 0
          };
          await upsertUserToSupabase(userObj);
          
          logInfo(`[fetchUserMediaTweets] API获取到用户ID: ${userId}, 名称: ${userName}`);
        } else {
          logError(`[fetchUserMediaTweets] 无法获取用户信息: ${screenName}`);
          logInfo(`[fetchUserMediaTweets] API响应: ${JSON.stringify(userData).substring(0, 500)}...`);
          if (chatId) {
            await bot.telegram.sendMessage(chatId, `无法获取用户 @${screenName} 的信息，可能不存在或已被保护。`);
          }
          return [];
        }
      } catch (apiErr) {
        logError(`[fetchUserMediaTweets] 获取用户API调用失败: ${apiErr.message}`);
        if (apiErr.response) {
          logError(`[fetchUserMediaTweets] 响应状态: ${apiErr.response.status}`);
          logError(`[fetchUserMediaTweets] 响应数据: ${JSON.stringify(apiErr.response.data).substring(0, 500)}...`);
        }
        throw apiErr; // 重新抛出以便上层捕获
      }
    }
    
    if (!userId) {
      logError(`[fetchUserMediaTweets] 未找到用户ID: ${screenName}`);
      if (chatId) {
        await bot.telegram.sendMessage(chatId, `无法获取用户 @${screenName} 的ID。`);
      }
      return [];
    }
    
    // 通知开始获取推文
    if (chatId) {
      await bot.telegram.sendMessage(chatId, `开始获取用户 @${screenName} (ID: ${userId}) 的媒体推文${allPages ? '(全部页面)' : '(仅第一页)'}...`);
    }
    
    // 准备获取媒体推文
    const headers = {
      "authorization": "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
      "cookie": cookie,
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
      "x-csrf-token": csrf,
      "referer": `https://twitter.com/${screenName}`
    };
    
    // 打印请求头信息(删除敏感信息)
    const logHeaders = {...headers};
    logHeaders.cookie = cookie ? cookie.substring(0, 20) + '...' : 'not set';
    logHeaders.authorization = logHeaders.authorization ? logHeaders.authorization.substring(0, 20) + '...' : 'not set';
    logInfo(`[fetchUserMediaTweets] 请求头: ${JSON.stringify(logHeaders)}`);
    
    // 定义媒体推文API所需的features参数
    const features = {
      "responsive_web_graphql_exclude_directive_enabled": true,
      "verified_phone_label_enabled": false,
      "creator_subscriptions_tweet_preview_api_enabled": true,
      "responsive_web_graphql_timeline_navigation_enabled": true,
      "responsive_web_graphql_skip_user_profile_image_extensions_enabled": false,
      "tweetypie_unmention_optimization_enabled": true,
      "responsive_web_edit_tweet_api_enabled": true,
      "graphql_is_translatable_rweb_tweet_is_translatable_enabled": true,
      "view_counts_everywhere_api_enabled": true,
      "longform_notetweets_consumption_enabled": true,
      "responsive_web_twitter_article_tweet_consumption_enabled": false,
      "tweet_awards_web_tipping_enabled": false,
      "freedom_of_speech_not_reach_fetch_enabled": true,
      "standardized_nudges_misinfo": true,
      "tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled": true,
      "longform_notetweets_rich_text_read_enabled": true,
      "longform_notetweets_inline_media_enabled": true,
      "responsive_web_media_download_video_enabled": false,
      "responsive_web_enhance_cards_enabled": false
    };
    
    let allTweets = [];
    let cursor = null;
    let pageCount = 0;
    
    while (true) {
      pageCount++;
      logInfo(`[fetchUserMediaTweets] 正在获取第 ${pageCount} 页...`);
      
      // 构建variables参数对象
      const variables = {
        "userId": userId,
        "count": 20,
        "includePromotedContent": false,
        "withClientEventToken": false,
        "withBirdwatchNotes": false,
        "withVoice": true,
        "withV2Timeline": true
      };
      
      // 如果有cursor，添加到variables对象
      if (cursor) {
        variables.cursor = cursor;
      }
      
      // 使用encodeURIComponent正确编码参数
      const url = "https://twitter.com/i/api/graphql/Le6KlbilFmSu-5VltFND-Q/UserMedia" + 
                  "?variables=" + encodeURIComponent(JSON.stringify(variables)) +
                  "&features=" + encodeURIComponent(JSON.stringify(features));
      
      // 请求数据
      try {
        const response = await axios.get(url, { headers });
        logInfo(`[fetchUserMediaTweets] 第 ${pageCount} 页API响应状态码: ${response.status}`);
        
        const data = response.data;
        
        // 提取推文数据
        let tweets = [];
        let allTweetsInPage = []; // 保存页面中的所有推文，包括没有媒体的
        let nextCursor = null;
        
        try {
          let rawEntries;
          
          // 不同页面的结构可能不同
          if (pageCount === 1) {
            // 第一页的结构
            const instructions = data.data?.user?.result?.timeline_v2?.timeline?.instructions || [];
            const addEntries = instructions.find(i => i.type === 'TimelineAddEntries');
            
            if (!addEntries) {
              logError(`[fetchUserMediaTweets] 第一页没有找到TimelineAddEntries: ${JSON.stringify(instructions).substring(0, 500)}...`);
              break;
            }
            
            rawEntries = addEntries.entries || [];
            
            // 从第一页提取items
            const items = [];
            for (const entry of rawEntries) {
              if (entry.content?.items) {
                items.push(...entry.content.items);
              }
            }
            rawEntries = items.length > 0 ? items : rawEntries;
          } else {
            // 后续页面的结构
            const instructions = data.data?.user?.result?.timeline_v2?.timeline?.instructions || [];
            const addToModule = instructions.find(i => i.type === 'TimelineAddToModule');
            
            if (!addToModule || !addToModule.moduleItems || addToModule.moduleItems.length === 0) {
              logInfo(`[fetchUserMediaTweets] 第 ${pageCount} 页没有更多推文`);
              break; // 没有更多推文
            }
            
            rawEntries = addToModule.moduleItems;
          }
          
          // 获取下一页cursor
          for (const instruction of data.data?.user?.result?.timeline_v2?.timeline?.instructions || []) {
            if (instruction.type === 'TimelineAddEntries') {
              for (const entry of instruction.entries || []) {
                if (entry.entryId?.includes('cursor-bottom')) {
                  nextCursor = entry.content?.value;
                  break;
                }
              }
            }
          }
          
          // 处理推文 - 修改处理逻辑
          for (const entry of rawEntries) {
            try {
              const entryId = entry.entryId || '';
              
              // 跳过非推文内容
              if (!entryId.includes('tweet') || entryId.includes('promoted-tweet')) {
                continue;
              }
              
              let tweetResult;
              if (entry.item) {
                // 媒体页面的结构
                tweetResult = entry.item?.itemContent?.tweet_results?.result;
              } else if (entry.content) {
                // 常规结构
                tweetResult = entry.content?.itemContent?.tweet_results?.result;
              }
              
              if (!tweetResult) continue;
              
              // 添加所有推文，无论是否有媒体
              allTweetsInPage.push(tweetResult);
              
              // 检查是否有媒体
              const legacy = tweetResult.legacy || (tweetResult.tweet?.legacy) || {};
              if (legacy.extended_entities?.media) {
                // 有媒体的推文添加到处理队列
                tweets.push(tweetResult);
              }
            } catch (err) {
              logError(`[fetchUserMediaTweets] 解析推文条目出错: ${err.message}`);
            }
          }
          
          // 将获取的所有推文(不管是否有媒体)添加到总列表并保存到数据库
          if (allTweetsInPage.length > 0) {
            // 首先将所有推文保存到数据库
            for (const tweet of allTweetsInPage) {
              try {
                // 解析推文数据
                const parsedTweet = parseTweet(tweet, 200, null, null, 'pending');
                if (parsedTweet) {
                  // 确保基本字段
                  parsedTweet.collected_at = new Date().toISOString();
                  // 保存到数据库
                  await upsertTweetToSupabase(parsedTweet);
                  logInfo(`[fetchUserMediaTweets] 推文ID:${parsedTweet.tweet_id} 已保存到数据库`);
                }
              } catch (err) {
                logError(`[fetchUserMediaTweets] 保存推文到数据库出错: ${err.message}`);
              }
            }
            
            allTweets.push(...allTweetsInPage);
            logInfo(`[fetchUserMediaTweets] 第 ${pageCount} 页获取到 ${allTweetsInPage.length} 条推文(其中 ${tweets.length} 条含媒体)，总计 ${allTweets.length} 条`);
            
            if (chatId) {
              await bot.telegram.sendMessage(chatId, `第 ${pageCount} 页获取到 ${allTweetsInPage.length} 条推文(其中 ${tweets.length} 条含媒体)，总计 ${allTweets.length} 条`);
            }
            
            // 只为包含媒体的推文下载并发送到Telegram
            if (tweets.length > 0) {
              for (const tweet of tweets) {
                try {
                  await handleTweet(tweet, chatId, bot);
                  await sleep(1000); // 添加延迟，避免发送消息过快
                } catch (err) {
                  logError(`[fetchUserMediaTweets] 处理媒体推文出错: ${err.message}`);
                }
              }
            }
          } else {
            logInfo(`[fetchUserMediaTweets] 第 ${pageCount} 页未获取到推文`);
          }
          
          // 如果只获取第一页或没有下一页，则退出循环
          if (!allPages || !nextCursor) {
            break;
          }
          
          // 设置下一页cursor
          cursor = nextCursor;
          
          // 添加延迟，避免请求过快
          await sleep(2000);
          
        } catch (parseErr) {
          logError(`[fetchUserMediaTweets] 解析第 ${pageCount} 页数据出错: ${parseErr.message}`);
          logInfo(`[fetchUserMediaTweets] 响应数据片段: ${JSON.stringify(data).substring(0, 500)}...`);
          break;
        }
      } catch (apiErr) {
        logError(`[fetchUserMediaTweets] 获取第 ${pageCount} 页API调用失败: ${apiErr.message}`);
        if (apiErr.response) {
          logError(`[fetchUserMediaTweets] 响应状态: ${apiErr.response.status}`);
          logError(`[fetchUserMediaTweets] 响应数据: ${JSON.stringify(apiErr.response.data).substring(0, 500)}...`);
        }
        break;
      }
    }
    
    // 完成后验证数据库记录数量
    try {
      const { count, error } = await supabase
        .from('tweet')
        .select('tweet_id', { count: 'exact' })
        .eq('author_id', userId);
        
      if (error) {
        logError(`[fetchUserMediaTweets] 统计数据库记录数量出错: ${error.message}`);
      } else {
        logInfo(`[fetchUserMediaTweets] 数据库中共有 ${count} 条该用户的推文记录`);
        if (chatId) {
          await bot.telegram.sendMessage(chatId, `用户 @${screenName} 在数据库中共有 ${count} 条推文记录`);
        }
      }
    } catch (err) {
      logError(`[fetchUserMediaTweets] 统计数据库记录出错: ${err.message}`);
    }
    
    // 返回获取结果
    if (chatId) {
      await bot.telegram.sendMessage(chatId, `用户 @${screenName} 的推文获取完成，共处理 ${allTweets.length} 条推文。`);
    }
    return allTweets;
    
  } catch (err) {
    const errorMsg = `[fetchUserMediaTweets] 获取推文出错: ${err.message}`;
    logError(errorMsg);
    if (chatId) {
      await bot.telegram.sendMessage(chatId, `获取用户 @${screenName} 的媒体推文失败: ${err.message}`);
    }
    return [];
  }
}

/**
 * 通过 Tweet ID 获取推文详情（使用 Twitter GraphQL API）
 * @param {string} tweetId - 推文ID
 * @returns {Promise<object|null>} 返回推文数据或 null
 */
async function getTweetById(tweetId) {
  logInfo(`[getTweetById] 开始获取推文 ${tweetId} 的详情...`);
  
  const cookie = TWITTER_COOKIE_USER;
  if (!cookie) {
    logError('[getTweetById] TWITTER_COOKIE_USER 环境变量未设置');
    return null;
  }
  
  const csrf = getCsrfToken(cookie);
  const headers = {
    "authorization": "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
    "cookie": cookie,
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "x-csrf-token": csrf,
    "referer": `https://twitter.com/i/status/${tweetId}`
  };

  const variables = {
    tweetId,
    withCommunity: true,
    includePromotedContent: false,
    withVoice: true
  };
  
  const features = {
    "creator_subscriptions_tweet_preview_api_enabled": true,
    "c9s_tweet_anatomy_moderator_badge_enabled": true,
    "tweetypie_unmention_optimization_enabled": true,
    "responsive_web_edit_tweet_api_enabled": true,
    "graphql_is_translatable_rweb_tweet_is_translatable_enabled": true,
    "view_counts_everywhere_api_enabled": true,
    "longform_notetweets_consumption_enabled": true,
    "responsive_web_twitter_article_tweet_consumption_enabled": false,
    "tweet_awards_web_tipping_enabled": false,
    "responsive_web_home_pinned_timelines_enabled": true,
    "freedom_of_speech_not_reach_fetch_enabled": true,
    "standardized_nudges_misinfo": true,
    "tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled": true,
    "rweb_video_timestamps_enabled": true,
    "longform_notetweets_rich_text_read_enabled": true,
    "longform_notetweets_inline_media_enabled": true,
    "responsive_web_graphql_exclude_directive_enabled": true,
    "verified_phone_label_enabled": false,
    "responsive_web_graphql_timeline_navigation_enabled": true,
    "responsive_web_graphql_skip_user_profile_image_extensions_enabled": false,
    "responsive_web_enhance_cards_enabled": false
  };
  
  const fieldToggles = {
    "withArticleRichContentState": false
  };

  const baseUrl = "https://twitter.com/i/api/graphql/DJS3BdhUhAJTMLtHYcCHjUs8w/TweetResultByRestId";
  const url = baseUrl + 
    "?variables=" + encodeURIComponent(JSON.stringify(variables)) +
    "&features=" + encodeURIComponent(JSON.stringify(features)) +
    "&fieldToggles=" + encodeURIComponent(JSON.stringify(fieldToggles));

  try {
    const resp = await axios.get(url, { headers });
    logInfo(`[getTweetById] API响应状态: ${resp.status}`);
    
    if (resp.status === 200) {
      const data = resp.data;
      if (data?.data?.tweetResult?.result) {
        logInfo(`[getTweetById] 成功获取推文 ${tweetId} 的数据`);
        return data.data.tweetResult.result;
      } else {
        logError(`[getTweetById] 响应结构异常: ${JSON.stringify(data).substring(0, 500)}...`);
        return null;
      }
    } else {
      logError(`[getTweetById] HTTP ${resp.status}: ${resp.data}`);
      return null;
    }
  } catch (err) {
    logError(`[getTweetById] 请求失败: ${err.message}`);
    if (err.response) {
      logError(`[getTweetById] 响应状态: ${err.response.status}`);
      logError(`[getTweetById] 响应数据: ${JSON.stringify(err.response.data).substring(0, 500)}...`);
    }
    return null;
  }
}
