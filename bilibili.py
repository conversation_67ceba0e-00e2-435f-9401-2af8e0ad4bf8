# -*- coding: utf-8 -*-
from typing import List, Dict, Optional

"""
bilibili.py

说明：
- 提供 API 调用和解析函数
- 提供基于 bdown.py 的下载并合并
- 对最终文件进行截图(ffmpeg)和元信息(ffprobe)，写到 .json
"""

import json
import os
import re
import time
import math
import shutil
import requests
import subprocess
from pathlib import Path
from dataclasses import dataclass
from datetime import datetime
import concurrent.futures
import asyncio

# 如果你已经放置了 bdown.py 文件，可以直接这样导入
from bdown import AsyncBiliDownloader, DownloadResult


@dataclass
class VideoInfo:
    bvid: str
    aid: int
    title: str
    duration: int
    publish_time: datetime
    view_count: int
    danmaku_count: int
    like_count: int
    coin_count: int
    favorite_count: int
    share_count: int
    reply_count: int
    dimension: Dict[str, int]


def parse_bilibili_video_info(response_json: dict) -> dict:
    """
    解析 B 站视频信息的 JSON
    """
    if "data" not in response_json or "data" not in response_json["data"]:
        raise ValueError("Invalid response format (缺少 data.data 节点)")

    videoData = response_json["data"]["data"]

    duration_sec = videoData.get("duration", 0)
    cover_url = videoData.get("pic", "")

    basic = {
        "title": videoData.get("title", ""),
        "bvid": videoData.get("bvid", ""),
        "aid": videoData.get("aid", 0),
        "cid": videoData.get("cid", 0),
        "duration": {
            "seconds": duration_sec,
            "formatted": f"{duration_sec // 60}:{str(duration_sec % 60).zfill(2)}"
        },
        "resolution": {
            "width": videoData.get("dimension", {}).get("width", 0),
            "height": videoData.get("dimension", {}).get("height", 0),
        },
        "category": {
            "main": videoData.get("tname", ""),
            "sub": videoData.get("tname_v2", "")
        },
        "publishTime": datetime.fromtimestamp(videoData.get("pubdate", 0)).isoformat(),
        "description": videoData.get("desc", ""),
        "coverUrl": cover_url,
    }

    owner = videoData.get("owner", {})
    owner_info = {
        "uid": owner.get("mid", 0),
        "username": owner.get("name", ""),
        "avatarUrl": owner.get("face", "")
    }

    stat = videoData.get("stat", {})
    statistics = {
        "views": stat.get("view", 0),
        "danmaku": stat.get("danmaku", 0),
        "comments": stat.get("reply", 0),
        "favorites": stat.get("favorite", 0),
        "coins": stat.get("coin", 0),
        "shares": stat.get("share", 0),
        "likes": stat.get("like", 0),
        "historicalRank": stat.get("his_rank", 0)
    }

    rights = videoData.get("rights", {})
    parsed_rights = {
        "isOriginal": (rights.get("no_reprint", 0) == 1),
        "canDownload": (rights.get("download", 0) == 1),
        "supportsHD": (rights.get("hd5", 0) == 1),
        "canAutoplay": (rights.get("autoplay", 0) == 1)
    }

    subtitle = videoData.get("subtitle", {})
    sub_list = subtitle.get("list", [])
    subtitles = []
    for sub in sub_list:
        subtitles.append({
            "id": sub.get("id_str", ""),
            "language": sub.get("lan_doc", ""),
            "type": "auto-generated" if sub.get("type", 0) == 1 else "manual",
            "url": sub.get("subtitle_url", "")
        })

    honors = []
    honor_reply = videoData.get("honor_reply", {})
    if "honor" in honor_reply:
        for h in honor_reply["honor"]:
            honors.append({
                "type": h.get("type", ""),
                "description": h.get("desc", "")
            })

    parsed_info = {
        "basic": basic,
        "owner": owner_info,
        "statistics": statistics,
        "rights": parsed_rights,
        "subtitles": subtitles,
        "honors": honors
    }
    return parsed_info


def build_bilibili_caption(video_info: dict) -> str:
    """
    构造一个消息文本，用于 Telegram 中的 caption
    """
    lines = []

    basic = video_info["basic"]
    lines.append(f"<b>标题</b>: {basic['title']}")
    lines.append(f"BV号: {basic['bvid']}")
    lines.append(f"AID: {basic['aid']}")
    lines.append(f"CID: {basic['cid']}")
    lines.append(f"时长: {basic['duration']['formatted']} ({basic['duration']['seconds']} 秒)")
    lines.append(f"分辨率: {basic['resolution']['width']} x {basic['resolution']['height']}")
    lines.append(f"分区: {basic['category']['main']}")
    lines.append(f"发布时间: {basic['publishTime']}")
    lines.append(basic['description'])

    owner = video_info["owner"]
    lines.append(f"\n<b>UP主</b>: {owner['username']} (UID: {owner['uid']})")

    stats = video_info["statistics"]
    stats_line = (
        f"播放: {stats['views']} | "
        f"弹幕: {stats['danmaku']} | "
        f"评论: {stats['comments']} | "
        f"收藏: {stats['favorites']} | "
        f"投币: {stats['coins']} | "
        f"分享: {stats['shares']} | "
        f"点赞: {stats['likes']}"
    )
    if stats['historicalRank'] > 0:
        stats_line += f" | 最高排行: {stats['historicalRank']}"
    lines.append(f"\n{stats_line}")

    return "\n".join(lines)


async def fetch_video_from_bdown(bvid: str) -> List[str]:
    """
    调用 bdown.AsyncBiliDownloader 下载该 BV 号下所有分P文件。
    下载完成后，对每个 mp4:
        - 生成预览图 (BVxxxx-cid.jpg)
        - ffprobe 获取分辨率等信息写到 BVxxxx-cid.json
    最后返回所有 mp4 文件的绝对路径列表
    """
    downloader = AsyncBiliDownloader()
    results: List[DownloadResult] = await downloader.download_video(bvid)
    
    saved_files = []
    for r in results:
        if not r.success:
            print(f"[{bvid}] 分P cid={r.cid} 下载失败: {r.error}")
            continue

        mp4_path = Path(r.file_path)
        if not mp4_path.exists():
            print(f"[{bvid}] 分P cid={r.cid} 文件不存在, 跳过截图/ffprobe.")
            continue

        # 1. 生成预览图
        jpg_path = mp4_path.with_suffix('.jpg')
        cmd_thumb = [
            'ffmpeg',
            '-y',
            '-i', str(mp4_path),
            '-ss', '00:00:01',
            '-vframes', '1',
            '-f', 'image2',
            str(jpg_path)
        ]
        try:
            subprocess.run(cmd_thumb, check=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        except Exception as e:
            print(f"[{bvid}] 分P cid={r.cid} 生成预览图失败: {e}")

        # 2. ffprobe
        cmd_probe = [
            'ffprobe',
            '-v', 'quiet',
            '-print_format', 'json',
            '-show_format',
            '-show_streams',
            str(mp4_path)
        ]
        probe_json_path = mp4_path.with_suffix('.json')
        try:
            probe_output = subprocess.check_output(cmd_probe)
            video_info = json.loads(probe_output)
            with open(probe_json_path, 'w', encoding='utf-8') as f:
                json.dump(video_info, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"[{bvid}] 分P cid={r.cid} ffprobe失败: {e}")

        saved_files.append(str(mp4_path.resolve()))
    
    return saved_files


def sync_fetch_one_video_info_and_cid(bv_id: str):
    """
    同步方式从后端接口获取单个视频信息并解析
    返回: (VideoInfo对象 or None, cid, parsed_data or None, is_deleted)
    """
    url = f"http://localhost:8080/api/bilibili/web/fetch_one_video?bv_id={bv_id}"
    resp = requests.get(url).json()

    if resp.get("data", {}).get("code") == 62002:
        return None, 0, None, True  # 稿件不可见/已删除

    from datetime import datetime
    video_data = resp.get("data", {}).get("data", {})
    if not video_data or not video_data.get("bvid"):
        return None, 0, None, False

    stat = video_data.get("stat", {})
    bvid = video_data.get("bvid", "")
    aid = video_data.get("aid", 0)
    duration = video_data.get("duration", 0)
    publish_time = datetime.fromtimestamp(video_data.get("pubdate", 0))
    dimension = video_data.get("dimension", {})

    video_info = VideoInfo(
        bvid=bvid,
        aid=aid,
        title=video_data.get("title", ''),
        duration=duration,
        publish_time=publish_time,
        view_count=stat.get('view', 0),
        danmaku_count=stat.get('danmaku', 0),
        like_count=stat.get('like', 0),
        coin_count=stat.get('coin', 0),
        favorite_count=stat.get('favorite', 0),
        share_count=stat.get('share', 0),
        reply_count=stat.get('reply', 0),
        dimension=dimension
    )
    cid = video_data.get("cid", 0)

    parsed_data = parse_bilibili_video_info(resp)
    return video_info, cid, parsed_data, False


# ========== 保留 fetch_all_bvids 同步函数 + 异步包装 ==========

def _sync_fetch_all_bvids(uid: str) -> List[str]:
    base_url = "http://localhost:8080/api/bilibili/web/fetch_user_post_videos"
    page_size = 20
    all_bvids = []
    try:
        page = 1
        while True:
            url = f"{base_url}?uid={uid}&pn={page}"
            resp = requests.get(url)
            resp.raise_for_status()
            resp_json = resp.json()

            data_data = resp_json.get("data", {}).get("data", {})
            page_info = data_data.get("page", {})
            list_data = data_data.get("list", {})
            vlist = list_data.get("vlist", [])

            if not vlist:
                print(f"第{page}页没有视频或数据结构异常")
                break

            for v in vlist:
                all_bvids.append(v["bvid"])

            total_count = page_info.get("count", 0)
            total_pages = math.ceil(total_count / page_size)
            print(f"第 {page} 页，获取 {len(vlist)} 个视频")

            if page >= total_pages:
                break

            page += 1
            time.sleep(0.5)
        return all_bvids
    except Exception as e:
        print(f"获取B站视频列表失败: {str(e)}")
        return []


thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=20)

async def fetch_all_bvids(uid: str) -> List[str]:
    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(thread_pool, _sync_fetch_all_bvids, uid)


async def fetch_user_collect_folders(uid: str) -> List[dict]:
    def _sync_fetch_user_collect_folders(uid: str) -> List[dict]:
        try:
            url = f"http://localhost:8080/api/bilibili/web/fetch_collect_folders?uid={uid}"
            resp = requests.get(url)
            resp.raise_for_status()
            data = resp.json()
            return data.get("data", {}).get("data", {}).get("list", [])
        except Exception as e:
            print(f"获取收藏夹列表失败: {e}")
            return []

    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(thread_pool, _sync_fetch_user_collect_folders, uid)


async def fetch_folder_bvids(folder_id: str) -> List[str]:
    def _sync_fetch_folder_bvids(folder_id: str) -> List[str]:
        base_url = "http://localhost:8080/api/bilibili/web/fetch_user_collection_videos"
        all_bvids = []
        page = 1
        while True:
            url = f"{base_url}?folder_id={folder_id}&pn={page}"
            try:
                resp = requests.get(url)
                resp.raise_for_status()
                resp_data = resp.json()

                data_data = resp_data.get("data", {}).get("data", {})
                has_more = data_data.get("has_more", False)
                medias = data_data.get("medias", [])

                page_bvids = [m.get("bvid") for m in medias if m.get("bvid")]
                all_bvids.extend(page_bvids)

                if not has_more:
                    break
                page += 1
                time.sleep(0.5)
            except Exception as e:
                print(f"获取收藏夹视频失败: {e}")
                break
        return all_bvids

    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(thread_pool, _sync_fetch_folder_bvids, folder_id)
