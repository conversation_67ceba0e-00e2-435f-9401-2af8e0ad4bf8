const sharp = require('sharp');
const path = require('path');
const fs = require('fs').promises;

// 支持的图片格式
const SUPPORTED_FORMATS = ['.png', '.jpg', '.jpeg', '.webp'];

// 输出文件名
const OUTPUT_FILENAME = 'combined_result.png';
const FINAL_OUTPUT_FILENAME = 'final_result.png';

async function getImagesFromDirectory(dirPath) {
    try {
        const files = await fs.readdir(dirPath);
        return files
            .filter(file => {
                const ext = path.extname(file).toLowerCase();
                // 排除输出文件
                return SUPPORTED_FORMATS.includes(ext) && 
                       !file.startsWith('combined_') &&
                       !file.startsWith('final_') &&
                       file !== OUTPUT_FILENAME;
            })
            .map(file => path.join(dirPath, file))
            .sort(); // 确保处理顺序一致
    } catch (error) {
        console.error('读取目录失败:', error);
        throw error;
    }
}

async function detectBackground(data, width, height, channels) {
    // 采样边缘像素来判断背景色
    const samples = [];
    
    // 采样顶部和底部边缘
    for (let x = 0; x < width; x += Math.floor(width / 20)) {
        // 顶部边缘
        const topIdx = (x * channels);
        samples.push((data[topIdx] + data[topIdx + 1] + data[topIdx + 2]) / 3);
        
        // 底部边缘
        const bottomIdx = ((height - 1) * width * channels) + (x * channels);
        samples.push((data[bottomIdx] + data[bottomIdx + 1] + data[bottomIdx + 2]) / 3);
    }
    
    // 采样左右边缘
    for (let y = 0; y < height; y += Math.floor(height / 20)) {
        // 左边缘
        const leftIdx = (y * width * channels);
        samples.push((data[leftIdx] + data[leftIdx + 1] + data[leftIdx + 2]) / 3);
        
        // 右边缘
        const rightIdx = (y * width * channels) + ((width - 1) * channels);
        samples.push((data[rightIdx] + data[rightIdx + 1] + data[rightIdx + 2]) / 3);
    }
    
    // 计算平均亮度
    const avgBrightness = samples.reduce((sum, val) => sum + val, 0) / samples.length;
    
    // 判断背景类型
    if (avgBrightness > 200) {
        return 'white';
    } else if (avgBrightness < 50) {
        return 'black';
    } else {
        // 如果不确定，返回样本中出现最多的类型
        const brightSamples = samples.filter(s => s > 200).length;
        const darkSamples = samples.filter(s => s < 50).length;
        return brightSamples > darkSamples ? 'white' : 'black';
    }
}

async function processFragment(inputPath) {
    try {
        console.log(`处理碎片: ${path.basename(inputPath)}`);
        
        // 读取图片并转换为 PNG 格式
        const image = sharp(inputPath);
        const metadata = await image.metadata();

        // 获取原始像素数据
        const { data, info } = await image
            .raw()
            .toBuffer({ resolveWithObject: true });

        // 检测背景类型
        const backgroundColor = await detectBackground(data, info.width, info.height, info.channels);
        console.log(`检测到背景色: ${backgroundColor}`);

        // 创建新的 Buffer 来存储 RGBA 数据
        const rgbaData = new Uint8Array(info.width * info.height * 4);

        // 处理每个像素
        for (let i = 0, j = 0; i < data.length; i += 3, j += 4) {
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];
            
            // 计算亮度
            const brightness = (r + g + b) / 3;
            
            // 设置 RGBA 值
            rgbaData[j] = r;     // R
            rgbaData[j + 1] = g; // G
            rgbaData[j + 2] = b; // B

            // 根据背景色类型设置透明度
            if (backgroundColor === 'white') {
                // 白色背景：亮度高的像素设为透明
                rgbaData[j + 3] = brightness > 200 ? 0 : 255;
            } else {
                // 黑色背景：亮度低的像素设为透明
                rgbaData[j + 3] = brightness < 30 ? 0 : 255;
            }
        }

        // 创建新图片
        const processedImage = await sharp(rgbaData, {
            raw: {
                width: info.width,
                height: info.height,
                channels: 4
            }
        })
        .png()
        .toBuffer();

        return {
            buffer: processedImage,
            width: metadata.width,
            height: metadata.height
        };
    } catch (error) {
        console.error(`处理碎片 ${inputPath} 时出错:`, error);
        throw error;
    }
}

async function invertColors(inputBuffer) {
    try {
        return await sharp(inputBuffer)
            .negate({ alpha: false }) // 不反转 alpha 通道
            .toBuffer();
    } catch (error) {
        console.error('反转颜色时出错:', error);
        throw error;
    }
}

async function combineFragments(imagePaths) {
    try {
        console.log(`开始处理 ${imagePaths.length} 个碎片...`);
        
        // 处理所有碎片
        const processedFragments = await Promise.all(
            imagePaths.map(async (imagePath) => {
                return await processFragment(imagePath);
            })
        );

        // 找出最大的宽度和高度
        const maxDimensions = processedFragments.reduce((acc, img) => ({
            width: Math.max(acc.width, img.width),
            height: Math.max(acc.height, img.height)
        }), { width: 0, height: 0 });

        console.log(`创建画布 (${maxDimensions.width}x${maxDimensions.height})`);

        // 创建一个透明的背景
        let composite = sharp({
            create: {
                width: maxDimensions.width,
                height: maxDimensions.height,
                channels: 4,
                background: { r: 0, g: 0, b: 0, alpha: 0 }
            }
        }).png();

        // 准备叠加操作
        const compositeOperations = processedFragments.map(({ buffer }) => ({
            input: buffer,
            blend: 'over'  // 使用 over 混合模式来叠加碎片
        }));

        // 执行叠加
        console.log('正在合成碎片...');
        composite = composite.composite(compositeOperations);

        // 保存中间结果
        const outputDir = path.dirname(imagePaths[0]);
        const combinedPath = path.join(outputDir, OUTPUT_FILENAME);
        const combinedBuffer = await composite.toBuffer();
        await sharp(combinedBuffer).toFile(combinedPath);
        
        // 反转颜色并保存最终结果
        console.log('正在反转颜色...');
        const invertedBuffer = await invertColors(combinedBuffer);
        const finalPath = path.join(outputDir, FINAL_OUTPUT_FILENAME);
        await sharp(invertedBuffer).toFile(finalPath);

        console.log(`处理完成！`);
        console.log(`合成结果已保存至：${combinedPath}`);
        console.log(`最终结果（反转色）已保存至：${finalPath}`);
    } catch (error) {
        console.error('处理图片时出错:', error);
        throw error;
    }
}

// 新的核心函数，供外部调用
async function generateCombinedImage(imagePaths, outputDir, { invert = true, baseOutputName = 'combined_image' } = {}) {
    try {
        if (!imagePaths || imagePaths.length === 0) {
            throw new Error("没有提供图片路径");
        }
        if (!outputDir) {
            throw new Error("没有提供输出目录");
        }
        await fs.mkdir(outputDir, { recursive: true }); // 确保输出目录存在

        console.log(`[pinhaotu] 开始处理 ${imagePaths.length} 个碎片... 输出到 ${outputDir}`);
        
        const processedFragments = await Promise.all(
            imagePaths.map(imagePath => processFragment(imagePath))
        );

        if (processedFragments.some(p => !p || !p.buffer)) {
            throw new Error("部分图片碎片处理失败，无法继续合成。");
        }
        
        const maxDimensions = processedFragments.reduce((acc, img) => ({
            width: Math.max(acc.width, img.width),
            height: Math.max(acc.height, img.height)
        }), { width: 0, height: 0 });

        if (maxDimensions.width === 0 || maxDimensions.height === 0) {
            throw new Error("无法确定合成图片的尺寸。");
        }

        console.log(`[pinhaotu] 创建画布 (${maxDimensions.width}x${maxDimensions.height})`);

        let composite = sharp({
            create: {
                width: maxDimensions.width,
                height: maxDimensions.height,
                channels: 4,
                background: { r: 0, g: 0, b: 0, alpha: 0 }
            }
        }).png();

        const compositeOperations = processedFragments.map(({ buffer }) => ({
            input: buffer,
            blend: 'over'
        }));

        console.log('[pinhaotu] 正在合成碎片...');
        composite = composite.composite(compositeOperations);

        const combinedBuffer = await composite.toBuffer();
        
        // 根据是否反色，决定最终输出路径和是否执行反色
        let finalOutputPath;
        let finalBuffer = combinedBuffer;

        if (invert) {
            console.log('[pinhaotu] 正在反转颜色...');
            finalBuffer = await invertColors(combinedBuffer);
            finalOutputPath = path.join(outputDir, `${baseOutputName}_inverted.png`);
            
            // 也保存一份未反色的作为中间参考 (可选)
            // const uncoloredPath = path.join(outputDir, `${baseOutputName}_uninverted.png`);
            // await sharp(combinedBuffer).toFile(uncoloredPath);
            // console.log(`[pinhaotu] 未反色版本已保存至：${uncoloredPath}`);

        } else {
            finalOutputPath = path.join(outputDir, `${baseOutputName}_uninverted.png`);
        }
        
        await sharp(finalBuffer).toFile(finalOutputPath);
        console.log(`[pinhaotu] 最终图片已保存至：${finalOutputPath}`);
        return finalOutputPath; 

    } catch (error) {
        console.error('[pinhaotu] 处理图片时出错:', error);
        throw error;
    }
}

async function main() {
    const dirPath = process.argv[2];
    
    if (!dirPath) {
        console.log('请提供图片目录的路径');
        console.log('使用方式: node index.js 图片目录路径');
        process.exit(1);
    }

    try {
        // 检查目录是否存在
        const stats = await fs.stat(dirPath);
        if (!stats.isDirectory()) {
            console.error('提供的路径不是目录');
            process.exit(1);
        }

        // 获取目录中的所有图片
        const imagePaths = await getImagesFromDirectory(dirPath);
        
        if (imagePaths.length === 0) {
            console.log('目录中没有找到支持的图片文件');
            console.log(`支持的格式: ${SUPPORTED_FORMATS.join(', ')}`);
            process.exit(1);
        }

        // 处理图片
        await combineFragments(imagePaths);
    } catch (error) {
        console.error('程序执行出错:', error);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = { 
    generateCombinedImage, 
    getImagesFromDirectory, // 如果外部需要的话
    SUPPORTED_FORMATS      // 如果外部需要的话
}; 