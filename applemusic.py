import os
import re
import asyncio
import subprocess
import zipfile
from datetime import datetime
import pytz  # 添加时区支持

import requests
import m3u8
from sanitize_filename import sanitize as sanitize_filename

from telethon import TelegramClient, events
from telethon.errors import RPCError
from telethon.tl.types import DocumentAttributeVideo
from telethon.tl.custom import Button

# Supabase
from supabase import create_client, Client as SupabaseClient



SUPABASE_URL = "https://wjanjmsywbydjbfrdkaz.supabase.co"
SUPABASE_KEY = (
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9."
    "eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndqYW5qbXN5d2J5ZGpiZnJka2F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAxMjc1OTEsImV4cCI6MjAzNTcwMzU5MX0."
    "ny-61EeDBsVRUr6BdWiamt9oV-6z2TC_f01FJff-9fE"
)
supabase: SupabaseClient = create_client(SUPABASE_URL, SUPABASE_KEY)

def get_beijing_time():
    """返回格式化的北京时间字符串"""
    beijing_tz = pytz.timezone('Asia/Shanghai')
    beijing_time = datetime.now(beijing_tz)
    return beijing_time.strftime('%Y-%m-%d %H:%M:%S %Z')

BOT_TOKEN = '7222442621:AAG0T-pl-hLEvzBfHtK3xvZT8S3f9jwoECA'
API_ID = 25432929
API_HASH = '965c5d22f0b9d1d0326e84bbb2bb18c1'

client = TelegramClient('apple_music_downloader_bot', API_ID, API_HASH).start(bot_token=BOT_TOKEN)


#########################
# 2) AppleMusicDownloader
#########################

class AppleMusicDownloader:
    def __init__(self):
        self.session = requests.Session()
        self.update_token()

    def update_token(self):
        print(f"[{get_beijing_time()}][Downloader] Fetching new token from Apple Music page...")
        resp = self.session.get("https://music.apple.com/us/album/positions-deluxe-edition/1553944254")
        jspath = re.search(r'crossorigin src="(/assets/index.+?\.js)"', resp.text)
        if not jspath:
            raise ValueError("Cannot find JS path from Apple Music page.")
        jsres = self.session.get("https://music.apple.com" + jspath.group(1))
        token_match = re.search(r'(eyJhbGc.+?)"', jsres.text)
        if not token_match:
            raise ValueError("Cannot find token from Apple Music JS.")
        token = token_match.group(1)

        self.session.headers.update({
            'authorization': f"Bearer {token}",
            'origin': 'https://music.apple.com'
        })
        print(f"[{get_beijing_time()}][Downloader] Token updated successfully.")

    def get_json(self, id_, country, kind):
        """Call Apple Music API to get metadata."""
        print(f"[{get_beijing_time()}][Downloader] Requesting Apple Music API: id={id_}, country={country}, kind={kind}")
        params = {
            'extend': 'editorialVideo',
            'fields[artists]': 'artwork,editorialVideo,url,name',
            'fields[albums]': 'artwork,editorialVideo,url,name,artistName,releaseDate',
            'fields[playlists]': 'artwork,editorialVideo,url,name,curatorName,lastModifiedDate,description,trackTypes'
        }
        url = f"https://amp-api.music.apple.com/v1/catalog/{country}/{kind}s/{id_}"
        resp = self.session.get(url, params=params)
        if resp.status_code == 401:
            print(f"[{get_beijing_time()}][Downloader] Token might have expired, retrying...")
            self.update_token()
            resp = self.session.get(url, params=params)
        resp.raise_for_status()
        print(f"[{get_beijing_time()}][Downloader] API responded successfully.")
        return resp.json()

    def fix_artwork_url(self, url: str):
        """
        Replace {w}/{h} with 6000, and convert bb.jpg -> bb.png
        to get a high-resolution artwork.
        """
        url = url.replace("{w}", "6000").replace("{h}", "6000")
        if "bb.jpg" in url:
            url = url.replace("bb.jpg", "bb.png")
        return url

    def download_static_artwork(self, url: str, output_path: str):
        """Download static cover."""
        print(f"[{get_beijing_time()}][Downloader] Downloading static artwork from: {url}")
        r = self.session.get(url)
        if r.status_code != 200:
            print(f"[{get_beijing_time()}][Downloader] Failed to download artwork. HTTP status:", r.status_code)
            return None
        with open(output_path, "wb") as f:
            f.write(r.content)
        print(f"[{get_beijing_time()}][Downloader] Artwork saved to {output_path}")
        return output_path

    def download_video(self, m3u8_url: str, output_path: str):
        """Download dynamic cover (m3u8)."""
        print(f"[{get_beijing_time()}][Downloader] Downloading video (m3u8) from: {m3u8_url}")
        try:
            playlist = m3u8.load(m3u8_url)
            # sort by resolution and bandwidth to get best
            streams = sorted(
                playlist.data["playlists"],
                key=lambda x: (
                    tuple(map(int, x['stream_info']['resolution'].split('x'))),
                    int(x['stream_info']['bandwidth'])
                ),
                reverse=True
            )
            best_stream = streams[0]['uri']
            subprocess.run([
                'ffmpeg', '-loglevel', 'quiet',
                '-y', '-i', best_stream,
                '-c', 'copy', output_path
            ], check=True)
            print(f"[{get_beijing_time()}][Downloader] Video saved to {output_path}")
            return output_path
        except Exception as e:
            print(f"[{get_beijing_time()}][Downloader] Error downloading m3u8: {e}")
            return None


#########################
# 2.1) Utility: Get media resolution
#########################

def get_media_resolution(file_path: str) -> str:
    """
    Use ffprobe to get file resolution (width x height).
    Returns an empty string on failure.
    """
    try:
        cmd = [
            'ffprobe', '-v', 'error',
            '-select_streams', 'v:0',
            '-show_entries', 'stream=width,height',
            '-of', 'csv=s=x:p=0', file_path
        ]
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        resolution = result.stdout.strip()  # e.g. "1920x1080"
        return resolution
    except Exception as e:
        print(f"[{get_beijing_time()}][Resolution] ffprobe failed for {file_path}: {e}")
        return ""


#########################
# 3) Extended Downloader
#########################

class AppleMusicDownloaderExtended(AppleMusicDownloader):
    def get_m3u8_urls(self, data_json):
        """
        Retrieve dynamic cover links from editorialVideo with fallback logic,
        so we can handle if some keys are missing.
        """
        base = data_json['data'][0]['attributes'].get('editorialVideo')
        if not base:
            return []

        # 'data'[0]['type'] will be one of: 'albums', 'playlists', 'artists'
        kind = data_json['data'][0]['type']

        # We'll collect possible tall and square links with fallback
        # then return them if they exist.
        # Because Apple naming differs for artists vs albums/playlists.
        tall_url = None
        square_url = None

        if kind == 'artists':
            # For artists, code from 'FetchAll' tries motionArtistFullscreen16x9, then wide16x9, ...
            try:
                tall_url = base['motionArtistFullscreen16x9']['video']
            except KeyError:
                try:
                    tall_url = base['motionArtistWide16x9']['video']
                except KeyError:
                    tall_url = None

            # square
            try:
                square_url = base['motionArtistSquare1x1']['video']
            except KeyError:
                square_url = None

        else:
            # For albums/playlists
            # 'FetchAll' uses motionDetailTall for 'tall', and if KeyError we just skip
            try:
                tall_url = base['motionDetailTall']['video']
            except KeyError:
                tall_url = None

            # square
            # might be motionDetailSquare or fallback to motionSquareVideo1x1
            try:
                square_url = base['motionDetailSquare']['video']
            except KeyError:
                try:
                    square_url = base['motionSquareVideo1x1']['video']
                except KeyError:
                    square_url = None

        results = []
        if tall_url:
            results.append(("tall", tall_url))
        if square_url:
            results.append(("square", square_url))
        return results

    def download_all(self, url: str, root_dir: str):
        """
        Parse country/kind/id -> download static and dynamic artwork.
        Create a ZIP. Return:
          subfolder_path, zip_path, info, downloaded_files
        where downloaded_files = [(filepath, is_video, resolution), ...]
        """
        print(f"[Downloader] Start handling URL: {url}")

        # Modified regex to handle numeric IDs or pl.* IDs (pl.rp, pl.u, etc.)
        match = re.search(r"apple\.com\/(\w\w)\/(playlist|album|artist)\/.+\/(\d+|pl\..+)", url)
        if not match:
            raise ValueError("Invalid Apple Music URL (cannot parse country/kind/id).")

        country, kind_singular, id_ = match.groups()
        # The Apple Music API expects: if we see 'playlist' -> kind=playlist
        # if 'album' -> kind=album, if 'artist' -> kind=artist
        # that is consistent with your original usage.
        kind = kind_singular  # direct usage

        data_json = self.get_json(id_, country, kind)
        attr = data_json['data'][0]['attributes']

        name = attr.get('name', '')
        artist = attr.get('artistName', '') or attr.get('curatorName', '')
        release_date = attr.get('releaseDate', '')
        last_modified = attr.get('lastModifiedDate', '')

        info = {
            "country": country,
            "kind": kind,
            "name": name,
            "artist": artist,
            "release_date": release_date,
            "last_modified": last_modified,
            "url": url
        }

        # Create subfolder
        safe_name = sanitize_filename(id_)
        subfolder_path = os.path.join(root_dir, safe_name)
        if not os.path.exists(subfolder_path):
            os.makedirs(subfolder_path)

        downloaded_files = []

        # Download static artwork
        artwork = attr.get('artwork')
        if artwork:
            art_url = self.fix_artwork_url(artwork['url'])
            static_path = os.path.join(subfolder_path, "cover.png")
            if self.download_static_artwork(art_url, static_path):
                resolution = get_media_resolution(static_path)
                downloaded_files.append((static_path, False, resolution))

        # Download dynamic covers with fallback
        m3u8_urls = self.get_m3u8_urls(data_json)
        for tag, m3u8_url in m3u8_urls:
            video_filename = f"{tag}.mp4"
            video_path = os.path.join(subfolder_path, video_filename)
            saved_video = self.download_video(m3u8_url, video_path)
            if saved_video:
                resolution = get_media_resolution(video_path)
                downloaded_files.append((video_path, True, resolution))

        # Create ZIP
        zip_basename = sanitize_filename(name) or "apple_music"
        zip_filename = f"{zip_basename}.zip"  # no date/time to avoid extra info
        zip_path = os.path.join(root_dir, zip_filename)
        print(f"[Downloader] Creating zip: {zip_path}")
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_ in os.listdir(subfolder_path):
                fullpath = os.path.join(subfolder_path, file_)
                zipf.write(fullpath, arcname=os.path.join(safe_name, file_))
        print("[Downloader] Zip created.")

        return subfolder_path, zip_path, info, downloaded_files


#########################
# 4) Async file sending
#########################
async def send_file_async(client, chat_id, file_info):
    """Send file with resolution info in caption."""
    filepath, is_video, resolution = file_info
    print(f"[{get_beijing_time()}][Sender] Start sending file: {filepath}")

    caption = f"Resolution: {resolution}" if resolution else ""

    thumb_path = None
    if is_video:
        thumb_path = filepath + ".jpg"
        try:
            subprocess.run([
                'ffmpeg', '-loglevel', 'quiet',
                '-y', '-ss', '1',
                '-i', filepath,
                '-vframes', '1',
                '-vf', 'scale=320:-1',  # maintain aspect ratio, width=320
                thumb_path
            ], check=True)
        except Exception as e:
            thumb_path = None
            print(f"[{get_beijing_time()}][Sender] Failed generating thumbnail for {filepath}: {e}")

    try:
        if is_video:
            await client.send_file(
                chat_id,
                file=filepath,
                caption=caption,
                thumb=thumb_path if thumb_path else None,
                attributes=[
                    DocumentAttributeVideo(
                        duration=0, w=0, h=0, supports_streaming=True
                    )
                ]
            )
        else:
            # 首先尝试作为照片发送
            try:
                await client.send_file(
                    chat_id,
                    file=filepath,
                    caption=caption,
                    force_document=False
                )
            except RPCError as e:
                # 如果失败（可能是因为文件太大），则作为文档发送
                if "10MB" in str(e):
                    print(f"[{get_beijing_time()}][Sender] File {filepath} too large for photo, sending as document")
                    await client.send_file(
                        chat_id,
                        file=filepath,
                        caption=caption,
                        force_document=True
                    )
                else:
                    raise  # 如果是其他错误，继续抛出
        print(f"[{get_beijing_time()}][Sender] File sent successfully: {filepath}")
    except Exception as e:
        print(f"[{get_beijing_time()}][Sender] Error sending file {filepath}: {e}")
        raise
    finally:
        if thumb_path and os.path.exists(thumb_path):
            os.remove(thumb_path)
            print(f"[{get_beijing_time()}][Sender] Removed thumbnail: {thumb_path}")


#########################
# 5) Event Handler
#########################
@client.on(events.NewMessage(pattern=r'(http[s]?://.*apple\.com.*)'))
async def handler(event):
    """
    When receiving an Apple Music link:
      1. Log/update user info in Supabase
      2. Download covers -> subfolder
      3. Send media files (show resolution only)
      4. Send ZIP (caption: basic info + button to open Apple Music)
      5. Cleanup
      6. Delete progress/original messages after a delay
    """
    chat_id = event.chat_id
    sender_id = event.sender_id
    sender = await event.get_sender()
    username = sender.username if sender.username else ""
    url = event.pattern_match.group(1).strip()

    print(f"[{get_beijing_time()}][Bot] Handling Apple Music link from user {sender_id} (username={username}): {url}")

    # 1) Supabase log
    try:
        res = supabase.table("users2").select("*").eq("user_id", sender_id).execute()
        if not res.data:
            print(f"[{get_beijing_time()}][Bot] New user, inserting to Supabase.")
            supabase.table("users2").insert({
                "user_id": sender_id,
                "username": username,
                "apple_music": True,
                "already": 1
            }).execute()
        else:
            current_already = res.data[0].get("already", 0)
            print(f"[{get_beijing_time()}][Bot] Existing user, incrementing 'already'.")
            supabase.table("users2").update({
                "apple_music": True,
                "already": current_already + 1
            }).eq("user_id", sender_id).execute()
    except Exception as e:
        print(f"[{get_beijing_time()}][Bot] Supabase error (not fatal): {e}")

    # 2) Download
    downloader = AppleMusicDownloaderExtended()
    downloads_root = "/home/<USER>/XHS/downloads"
    if not os.path.exists(downloads_root):
        os.makedirs(downloads_root)

    progress_msg = await event.reply("Processing your link, please wait...")

    subfolder_path, zip_path, info, downloaded_files = (None, None, None, [])
    try:
        subfolder_path, zip_path, info, downloaded_files = downloader.download_all(url, downloads_root)
    except Exception as e:
        print(f"[{get_beijing_time()}][Bot] Download error: {e}")
        error_msg = await event.reply("Invalid link or cannot handle it for now. Please check the link.")
        # Wait and remove
        await asyncio.sleep(5)
        await error_msg.delete()
        await event.delete()
        return

    print(f"[{get_beijing_time()}][Bot] Downloaded files: {downloaded_files}")
    await progress_msg.edit("Resources downloaded successfully. Uploading now...")

    # 3) Send files
    if downloaded_files:
        tasks = []
        for f_info in downloaded_files:
            tasks.append(send_file_async(client, chat_id, f_info))

        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            print(f"[{get_beijing_time()}][Bot] Error during file upload: {e}")
            error_msg = await progress_msg.edit("Error uploading files. Please try again later.")
            # Wait and remove
            await asyncio.sleep(5)
            await error_msg.delete()
            await event.delete()
            return

    await progress_msg.edit("Media files uploaded. Sending the ZIP file now...")

    # 4) Send ZIP
    zip_caption = (
        f"Type: {info['kind']}\n"
        f"Name: {info['name']}\n"
        f"Artist: {info['artist']}\n"
    )
    if info['release_date']:
        zip_caption += f"Release Date: {info['release_date']}\n"
    if info['last_modified']:
        zip_caption += f"Last Modified: {info['last_modified']}\n"
    zip_caption += f"Country: {info['country']}"

    # Button for Apple Music
    buttons = [[Button.url(text="Open in Apple Music", url=info['url'])]]

    try:
        await client.send_file(
            chat_id,
            zip_path,
            caption=zip_caption,
            force_document=True,
            buttons=buttons
        )
        print(f"[{get_beijing_time()}][Bot] ZIP sent successfully: {zip_path}")
    except Exception as e:
        print(f"[{get_beijing_time()}][Bot] Error while sending ZIP: {e}")
        error_msg = await event.reply("Error sending ZIP file. Please try again later.")
        await asyncio.sleep(5)
        await error_msg.delete()
        await event.delete()
        return

    # 5) Cleanup
    print(f"[{get_beijing_time()}][Bot] Cleaning up ZIP file...")
    if zip_path and os.path.exists(zip_path):
        try:
            os.remove(zip_path)
        except Exception as e:
            print(f"[{get_beijing_time()}][Bot] Error removing ZIP {zip_path}: {e}")

    # 6) Final messages
    await progress_msg.delete()
    completion_msg = await event.reply("Task completed. Thank you for using!")
    await asyncio.sleep(5)
    await completion_msg.delete()
    await event.delete()


#########################
# Main
#########################
def main():
    print(f"[{get_beijing_time()}][Bot] Running... waiting for Apple Music links.")
    client.run_until_disconnected()

if __name__ == "__main__":
    main()
