#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import oci
import sys
import json

# 配置参数 - 请填写您的信息
CONFIG = {
    "user": "ocid1.user.oc1..aaaaaaaabbcf5a4gcbwwawnttbthrwkj2paqyf43ywu3gavric2trcyvf67a",
    "fingerprint": "83:8f:ea:6a:7c:f6:0a:02:a9:7b:7a:d9:a9:51:18:9e",
    "tenancy": "ocid1.tenancy.oc1..aaaaaaaawsytfko4weogdnw37zvfcs6rprmq6hosr2cy7pikzlkettzf5lta",
    "region": "ap-osaka-1",
    "key_file": "/home/<USER>/.oci/oci_api_key.pem",
    # "pass_phrase": "jp",  # 如果您的私钥有密码，请取消注释并填写密码
}

def print_header(message):
    """打印带有分隔线的标题"""
    print("\n" + "=" * 80)
    print(message)
    print("=" * 80)

def get_instance_info(compute_client, compartment_id):
    """获取实例信息"""
    print_header("实例信息")
    try:
        instances = compute_client.list_instances(compartment_id=compartment_id).data
        if not instances:
            print("未找到实例")
            return None
        
        for instance in instances:
            print(f"实例名称: {instance.display_name}")
            print(f"实例ID: {instance.id}")
            print(f"状态: {instance.lifecycle_state}")
            print(f"形状: {instance.shape}")
            print(f"区域: {instance.region}")
            print(f"可用性域: {instance.availability_domain}")
            print(f"创建时间: {instance.time_created}")
            print("-" * 40)
        
        return instances
    except oci.exceptions.ServiceError as e:
        print(f"获取实例信息时出错: {e.message}")
        return None

def get_vnic_attachments(compute_client, compartment_id, instance_id):
    """获取VNIC附件信息"""
    print_header(f"VNIC附件信息 (实例ID: {instance_id})")
    try:
        vnic_attachments = compute_client.list_vnic_attachments(
            compartment_id=compartment_id,
            instance_id=instance_id
        ).data
        
        if not vnic_attachments:
            print("未找到VNIC附件")
            return None
        
        for vnic_attachment in vnic_attachments:
            print(f"VNIC ID: {vnic_attachment.vnic_id}")
            print(f"状态: {vnic_attachment.lifecycle_state}")
            print("-" * 40)
        
        return vnic_attachments
    except oci.exceptions.ServiceError as e:
        print(f"获取VNIC附件信息时出错: {e.message}")
        return None

def get_vnic_details(network_client, vnic_id):
    """获取VNIC详细信息"""
    print_header(f"VNIC详细信息 (VNIC ID: {vnic_id})")
    try:
        vnic = network_client.get_vnic(vnic_id=vnic_id).data
        print(f"VNIC名称: {vnic.display_name}")
        print(f"私有IP: {vnic.private_ip}")
        print(f"公共IP: {vnic.public_ip}")
        print(f"子网ID: {vnic.subnet_id}")
        
        if vnic.nsg_ids:
            print(f"网络安全组IDs: {', '.join(vnic.nsg_ids)}")
        else:
            print("未关联网络安全组")
        
        print("-" * 40)
        return vnic
    except oci.exceptions.ServiceError as e:
        print(f"获取VNIC详细信息时出错: {e.message}")
        return None

def get_nsg_details(network_client, nsg_id):
    """获取网络安全组详细信息"""
    print_header(f"网络安全组详细信息 (NSG ID: {nsg_id})")
    try:
        nsg = network_client.get_network_security_group(network_security_group_id=nsg_id).data
        print(f"NSG名称: {nsg.display_name}")
        print(f"VCN ID: {nsg.vcn_id}")
        print(f"状态: {nsg.lifecycle_state}")
        print("-" * 40)
        return nsg
    except oci.exceptions.ServiceError as e:
        print(f"获取网络安全组详细信息时出错: {e.message}")
        return None

def get_nsg_rules(network_client, nsg_id):
    """获取网络安全组规则"""
    print_header(f"网络安全组规则 (NSG ID: {nsg_id})")
    try:
        # 获取入站规则
        ingress_rules = network_client.list_network_security_group_security_rules(
            network_security_group_id=nsg_id,
            direction="INGRESS"
        ).data
        
        print("入站规则:")
        if not ingress_rules:
            print("  无入站规则")
        else:
            for rule in ingress_rules:
                print(f"  规则ID: {rule.id}")
                print(f"  描述: {rule.description}")
                print(f"  协议: {rule.protocol}")
                
                if rule.protocol == "6" or rule.protocol == "17":  # TCP or UDP
                    if hasattr(rule, 'tcp_options') and rule.tcp_options:
                        if rule.tcp_options.destination_port_range:
                            print(f"  目标端口范围: {rule.tcp_options.destination_port_range.min}-{rule.tcp_options.destination_port_range.max}")
                    elif hasattr(rule, 'udp_options') and rule.udp_options:
                        if rule.udp_options.destination_port_range:
                            print(f"  目标端口范围: {rule.udp_options.destination_port_range.min}-{rule.udp_options.destination_port_range.max}")
                
                if hasattr(rule, 'source') and rule.source:
                    print(f"  源: {rule.source}")
                if hasattr(rule, 'source_type') and rule.source_type:
                    print(f"  源类型: {rule.source_type}")
                
                print("  " + "-" * 30)
        
        # 获取出站规则
        egress_rules = network_client.list_network_security_group_security_rules(
            network_security_group_id=nsg_id,
            direction="EGRESS"
        ).data
        
        print("\n出站规则:")
        if not egress_rules:
            print("  无出站规则")
        else:
            for rule in egress_rules:
                print(f"  规则ID: {rule.id}")
                print(f"  描述: {rule.description}")
                print(f"  协议: {rule.protocol}")
                
                if rule.protocol == "6" or rule.protocol == "17":  # TCP or UDP
                    if hasattr(rule, 'tcp_options') and rule.tcp_options:
                        if rule.tcp_options.destination_port_range:
                            print(f"  目标端口范围: {rule.tcp_options.destination_port_range.min}-{rule.tcp_options.destination_port_range.max}")
                    elif hasattr(rule, 'udp_options') and rule.udp_options:
                        if rule.udp_options.destination_port_range:
                            print(f"  目标端口范围: {rule.udp_options.destination_port_range.min}-{rule.udp_options.destination_port_range.max}")
                
                if hasattr(rule, 'destination') and rule.destination:
                    print(f"  目标: {rule.destination}")
                if hasattr(rule, 'destination_type') and rule.destination_type:
                    print(f"  目标类型: {rule.destination_type}")
                
                print("  " + "-" * 30)
        
        return {"ingress": ingress_rules, "egress": egress_rules}
    except oci.exceptions.ServiceError as e:
        print(f"获取网络安全组规则时出错: {e.message}")
        return None

def main():
    # 验证配置
    for key, value in CONFIG.items():
        if key != "pass_phrase" and not value:  # pass_phrase是可选的
            print(f"错误: 请填写配置中的 {key} 参数")
            sys.exit(1)
    
    # 创建客户端
    compute_client = oci.core.ComputeClient(CONFIG)
    network_client = oci.core.VirtualNetworkClient(CONFIG)
    
    # 使用租户ID作为区间ID
    compartment_id = CONFIG["tenancy"]
    
    # 获取实例信息
    instances = get_instance_info(compute_client, compartment_id)
    if not instances:
        print("无法继续，未找到实例")
        sys.exit(1)
    
    # 对于每个实例，获取VNIC附件
    for instance in instances:
        vnic_attachments = get_vnic_attachments(compute_client, compartment_id, instance.id)
        if not vnic_attachments:
            continue
        
        # 对于每个VNIC附件，获取VNIC详细信息
        for vnic_attachment in vnic_attachments:
            vnic = get_vnic_details(network_client, vnic_attachment.vnic_id)
            if not vnic or not vnic.nsg_ids:
                continue
            
            # 对于每个网络安全组，获取详细信息和规则
            for nsg_id in vnic.nsg_ids:
                nsg = get_nsg_details(network_client, nsg_id)
                if not nsg:
                    continue
                
                get_nsg_rules(network_client, nsg_id)

if __name__ == "__main__":
    main()
