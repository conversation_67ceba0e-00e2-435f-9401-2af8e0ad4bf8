{"name": "douyin-bot", "version": "1.0.0", "description": "Douyin download Telegram Bot with Worker Pool support", "main": "douyin.js", "type": "module", "scripts": {"start": "node douyin.js", "dev": "nodemon douyin.js"}, "dependencies": {"@supabase/supabase-js": "^2.49.8", "adm-zip": "^0.5.16", "amqplib": "^0.10.8", "archiver": "^5.3.2", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "node-fetch": "^2.7.0", "telegraf": "^4.16.3", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^2.0.22"}, "engines": {"node": ">=18.0.0"}}