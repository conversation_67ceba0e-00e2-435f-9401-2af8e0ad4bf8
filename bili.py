# -*- coding: utf-8 -*-

"""
bilibot_combined.py

满足以下需求：
1. 全局仅1个并发下载任务，若正在监控收藏夹，此时收到用户链接请求 => 优先暂停监控，处理链接后再继续；
2. /start 命令后才开始监听（监控）收藏夹；
3. ~~不再允许reset功能~~
4. /check 命令分两段发：先发收藏夹处理进度，再发失败bvid列表；若太长则自动分割多条消息；
5. 如果未 /start，也能处理用户发来的链接（但不监控收藏夹）；
6. 不再显示具体错误原因，只在失败列表中记录 bvid。
7. 不允许更新已经存在于数据库中的条目的信息
"""

import os
import re
import json
import time
import math
import shutil
import asyncio
import requests
import subprocess
import concurrent.futures
import logging
from urllib.parse import urlparse
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass
from typing import List, Dict, Optional, Tuple
from uuid import uuid4
from supabase import create_client, Client as SupabaseClient
import dotenv

# 加载环境变量
dotenv.load_dotenv()

# Pyrogram
from pyrogram import Client as PyroClient, filters
from pyrogram.enums import ParseMode
from pyrogram.types import (
    CallbackQuery,
    Message,
    InlineKeyboardMarkup,
    InlineKeyboardButton
)
from pyrogram.errors import FloodWait

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 隐藏 httpx 的成功请求日志，只显示警告和错误
logging.getLogger("httpx").setLevel(logging.WARNING)

# ---- 全局配置 ----
# 从环境变量获取敏感信息
api_id = os.environ.get('BILI_TELEGRAM_API_ID')
api_hash = os.environ.get('BILI_TELEGRAM_API_HASH')
bot_token = os.environ.get('BILI_TELEGRAM_BOT_TOKEN')

# Supabase configuration
SUPABASE_URL = os.environ.get('BILI_SUPABASE_URL')
SUPABASE_KEY = os.environ.get('BILI_SUPABASE_KEY')
supabase: SupabaseClient = create_client(SUPABASE_URL, SUPABASE_KEY)

# 授权的聊天ID，用于权限验证
AUTHORIZED_CHAT_ID = int(os.environ.get('BILI_AUTHORIZED_CHAT_ID', "235196660"))

# 最大重试次数 - 新增
MAX_RETRY_COUNT = 10

# 数据库字段列表 - 添加这个用于过滤字段
DB_FIELDS = [
    "bvid", "cid", "video_file_id", "file_file_id", "video_caption", 
    "file_caption", "up", "uid", "is_in_fav", "folder_id", 
    "is_failure", "created_at", "folder_name", "status", "url"
]

# 过滤数据字典，只保留数据库中存在的字段
def filter_db_fields(data_dict):
    """过滤字典，只保留数据库表中存在的字段"""
    return {k: v for k, v in data_dict.items() if k in DB_FIELDS}

# 上传进度回调：打印简单进度
def progress_callback(current, total):
    print(f"Uploading: {current / total * 100:.2f}%")


# ========== 1) 异步下载器及下载结果定义 ==========

@dataclass
class DownloadResult:
    bvid: str
    cid: str
    file_path: str
    success: bool
    error: Optional[str] = None

class AsyncBiliDownloader:
    """
    用于异步下载 B 站视频的类。
    - 先获取视频分P信息
    - 再获取播放信息 (DASH 流)
    - 根据预设的清晰度与编码优先级选择最优的视频流 + 音频流
    - 分段并发下载文件，再用 FFmpeg 合并
    """

    # 默认请求头
    headers = {
        "User-Agent": os.environ.get('BILI_USER_AGENT', 
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/91.0.4472.124 Safari/537.36"
        ),
        "Referer": os.environ.get('BILI_REFERER', "https://www.bilibili.com"),
        "Cookie": os.environ.get('BILI_COOKIE', "")
    }

    # 优先匹配 codec: hev1 > avc1 > av01
    quality_priorities = {
        127: ['hev1', 'avc1', 'av01'],  # 8K
        126: ['hev1', 'avc1', 'av01'],  # 杜比视界
        125: ['hev1', 'avc1', 'av01'],  # HDR真彩
        120: ['hev1', 'avc1', 'av01'],  # 4K
        116: ['hev1', 'avc1', 'av01'],  # 1080P60
        112: ['hev1', 'avc1', 'av01'],  # 1080P+
        80:  ['hev1', 'avc1', 'av01'],  # 1080P
        64:  ['hev1', 'avc1', 'av01'],  # 720P
        32:  ['hev1', 'avc1', 'av01'],  # 480P
        16:  ['hev1', 'avc1', 'av01']   # 360P
    }

    quality_names = {
        127: "8K",
        126: "杜比视界",
        125: "HDR真彩",
        120: "4K",
        116: "1080P60",
        112: "1080P+",
        80:  "1080P",
        64:  "720P",
        32:  "480P",
        16:  "360P"
    }

    def __init__(self):
        self.download_path = Path("downloads")
        self.download_path.mkdir(exist_ok=True)

    async def get_video_parts(self, bvid: str) -> List[Dict]:
        url = "https://api.bilibili.com/x/web-interface/view"
        params = {"bvid": bvid}
        for _ in range(3):
            try:
                resp = await asyncio.get_event_loop().run_in_executor(
                    None, lambda: requests.get(url, params=params, headers=self.headers, timeout=10)
                )
                resp.raise_for_status()
                data = resp.json()
                if data["code"] != 0:
                    raise ValueError(data["message"])
                return data["data"]["pages"]
            except Exception as e:
                logger.warning(f"get_video_parts重试: {e}")
                await asyncio.sleep(1)
        raise RuntimeError(f"Failed to get video parts for {bvid}")

    async def get_play_info(self, bvid: str, cid: str) -> dict:
        url = "https://api.bilibili.com/x/player/wbi/playurl"
        params = {
            "bvid": bvid,
            "cid": cid,
            "qn": 127,
            "fnval": 4048,
            "fnver": 0,
            "fourk": 1
        }
        for _ in range(3):
            try:
                resp = await asyncio.get_event_loop().run_in_executor(
                    None, lambda: requests.get(url, params=params, headers=self.headers, timeout=10)
                )
                resp.raise_for_status()
                data = resp.json()
                if data["code"] != 0:
                    raise ValueError(data["message"])
                return data
            except Exception as e:
                logger.warning(f"get_play_info重试: {e}")
                await asyncio.sleep(1)
        raise RuntimeError(f"Failed to get play info for bvid={bvid}, cid={cid}")

    def find_best_stream(self, play_info: dict) -> Tuple[Optional[dict], Optional[dict]]:
        dash = play_info["data"].get("dash", None)
        if not dash:
            return None, None
        video_streams = dash.get("video", [])
        video_streams = sorted(video_streams, key=lambda x: x.get("bandwidth", 0), reverse=True)
        v_stream = None
        for qn in sorted(self.quality_priorities.keys(), reverse=True):
            cands = [v for v in video_streams if v["id"] == qn]
            if cands:
                for codec_pref in self.quality_priorities[qn]:
                    for c in cands:
                        if c["codecs"].startswith(codec_pref):
                            v_stream = c
                            break
                    if v_stream:
                        break
            if v_stream:
                break
        a_stream = None
        if dash.get("flac") and dash["flac"].get("audio"):
            a_stream = dash["flac"]["audio"]
        else:
            auds = dash.get("audio", [])
            auds = sorted(auds, key=lambda x: x.get("bandwidth", 0), reverse=True)
            if auds:
                a_stream = auds[0]
        return v_stream, a_stream

    def list_all_streams(self, play_info: dict) -> List[dict]:
        dash = play_info["data"].get("dash", None)
        if not dash:
            return []
        v_list = dash.get("video", [])
        out = []
        for v in v_list:
            qn = v["id"]
            if qn not in self.quality_names:
                continue
            out.append({
                "qn": qn,
                "codec": v["codecs"],
                "quality_name": self.quality_names[qn],
                "base_url": v["baseUrl"],
                "bandwidth": v.get("bandwidth", 0)
            })
        out = sorted(out, key=lambda x: (x["qn"], x["bandwidth"]), reverse=True)
        return out

    async def download_file(self, url: str, filename: str) -> bool:
        current_bvid = getattr(self, 'current_bvid', 'unknown')
        try:
            r_head = await asyncio.get_event_loop().run_in_executor(
                None, lambda: requests.head(url, headers=self.headers, timeout=10)
            )
            r_head.raise_for_status()
            total_size = int(r_head.headers.get("content-length", 0))
            if total_size == 0:
                raise ValueError("content-length=0")

            chunk_size = 1 * 1024 * 1024  # 1MB
            ranges = [(i, min(i+chunk_size-1, total_size-1)) for i in range(0, total_size, chunk_size)]

            async def fetch_chunk(st, ed) -> Tuple[int, bytes]:
                h = dict(self.headers)
                h["Range"] = f"bytes={st}-{ed}"
                max_retries = 5  # 增加重试次数到5次
                for retry in range(max_retries):
                    try:
                        rr = await asyncio.get_event_loop().run_in_executor(
                            None, lambda: requests.get(url, headers=h, timeout=30)
                        )
                        rr.raise_for_status()
                        return st, rr.content
                    except Exception as e:
                        logger.warning(f"[{current_bvid}] 分段下载失败 ({st}-{ed}): 重试 {retry+1}/{max_retries}, 错误: {e}")
                        if retry < max_retries - 1:
                            # 指数退避重试，每次重试等待时间增加
                            await asyncio.sleep(1 * (2 ** retry))
                        else:
                            logger.error(f"[{current_bvid}] 分段下载最终失败 ({st}-{ed}): {e}")
                raise RuntimeError(f"[{current_bvid}] 下载分段失败: {st}-{ed}, 已重试{max_retries}次")

            tmpfile = f"{filename}.temp"
            with open(tmpfile, "wb") as f:
                f.truncate(total_size)

            # 增加进度显示
            downloaded = 0
            total_chunks = len(ranges)
            
            # 限制并发分段数量以避免过多连接
            semaphore = asyncio.Semaphore(10)  # 最多10个并发分段下载
            
            async def fetch_with_semaphore(s, e):
                async with semaphore:
                    return await fetch_chunk(s, e)
            
            tasks = [fetch_with_semaphore(s, e) for (s, e) in ranges]
            completed_tasks = 0
            
            # 使用as_completed来逐个处理完成的任务，便于显示进度
            for future in asyncio.as_completed(tasks):
                try:
                    result = await future
                    completed_tasks += 1
                    if completed_tasks % 10 == 0 or completed_tasks == total_chunks:
                        logger.info(f"[{current_bvid}] 下载进度: {completed_tasks}/{total_chunks} 分段 ({completed_tasks/total_chunks*100:.1f}%)")
                    
                    if isinstance(result, Exception):
                        raise result
                    st, data = result
                    with open(tmpfile, "r+b") as f:
                        f.seek(st)
                        f.write(data)
                except Exception as e:
                    logger.error(f"[{current_bvid}] 处理分段结果出错: {e}")
                    return False

            os.replace(tmpfile, filename)
            return True
        except Exception as e:
            logger.error(f"[{current_bvid}] download_file失败: {e}")
            if os.path.exists(f"{filename}.temp"):
                os.remove(f"{filename}.temp")
            return False

    async def download_stream(self, bvid: str, cid: str, v_stream: dict, a_stream: dict, up_name: str = "") -> DownloadResult:
        temp_dir = Path("temp") / f"{bvid}_{cid}_{uuid4().hex[:6]}"
        temp_dir.mkdir(parents=True, exist_ok=True)
        try:
            vp = temp_dir / "video.m4s"
            ap = temp_dir / "audio.m4s"
            vt = asyncio.create_task(self.download_file(v_stream["baseUrl"], str(vp)))
            at = asyncio.create_task(self.download_file(a_stream["baseUrl"], str(ap)))
            v_ok, a_ok = await asyncio.gather(vt, at)
            if not (v_ok and a_ok):
                raise RuntimeError("下载视频或音频失败")

            # 修改文件命名格式：bili_bvid-cid_UP主.mp4
            safe_up_name = re.sub(r'[\\/*?:"<>|]', "_", up_name) if up_name else ""
            filename = f"bili_{bvid}-{cid}"
            if safe_up_name:
                filename += f"_{safe_up_name}"
            filename += ".mp4"
            
            outp = self.download_path / filename
            cmd = ["ffmpeg", "-i", str(vp), "-i", str(ap), "-c:v", "copy", "-c:a", "copy", "-y", str(outp)]
            proc = await asyncio.create_subprocess_exec(*cmd, stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE)
            _, stderr = await proc.communicate()
            if proc.returncode != 0:
                raise RuntimeError(f"ffmpeg合并失败: {stderr.decode('utf-8', 'ignore')}")

            for f in temp_dir.iterdir():
                f.unlink()
            temp_dir.rmdir()
            return DownloadResult(bvid, cid, str(outp), True)
        except Exception as e:
            return DownloadResult(bvid, cid, "", False, error=str(e))

    async def download_part(self, bvid: str, cid: str, up_name: str = "") -> DownloadResult:
        # 设置当前正在处理的BVID以便在日志中显示
        self.current_bvid = bvid
        try:
            logger.info(f"开始下载视频: {bvid}, 分P: {cid}")
            info = await self.get_play_info(bvid, cid)
            vs, as_ = self.find_best_stream(info)
            if not vs or not as_:
                raise ValueError(f"[{bvid}] 未找到合适流")
            return await self.download_stream(bvid, cid, vs, as_, up_name)
        except Exception as e:
            logger.error(f"[{bvid}] 下载分P失败: {e}")
            return DownloadResult(bvid, cid, "", False, str(e))

    async def download_video(self, bvid: str, up_name: str = "") -> List[DownloadResult]:
        """串行下载所有分P"""
        self.current_bvid = bvid
        out = []
        try:
            logger.info(f"开始获取视频 {bvid} 的所有分P")
            parts = await self.get_video_parts(bvid)
            logger.info(f"视频 {bvid} 共有 {len(parts)} 个分P")
            for p in parts:
                cid = str(p["cid"])
                logger.info(f"开始下载视频 {bvid} 的分P {cid}")
                r = await self.download_part(bvid, cid, up_name)
                if r.success:
                    logger.info(f"视频 {bvid} 的分P {cid} 下载成功")
                else:
                    logger.error(f"视频 {bvid} 的分P {cid} 下载失败: {r.error}")
                out.append(r)
            return out
        except Exception as e:
            logger.error(f"[{bvid}] 下载视频失败: {e}")
            return [DownloadResult(bvid, "unknown", "", False, error=str(e))]


# ========== 2) 获取视频信息与解析 ==========

@dataclass
class VideoInfo:
    bvid: str
    aid: int
    title: str
    duration: int
    publish_time: datetime
    view_count: int
    danmaku_count: int
    like_count: int
    coin_count: int
    favorite_count: int
    share_count: int
    reply_count: int
    dimension: Dict[str,int]


def parse_bilibili_video_info(json_data: dict) -> dict:
    if "data" not in json_data or "data" not in json_data["data"]:
        raise ValueError("data数据结构异常")

    vd = json_data["data"]["data"]
    dsec = vd.get("duration", 0)
    cover = vd.get("pic", "")
    basic = {
        "title": vd.get("title", ""),
        "bvid": vd.get("bvid", ""),
        "aid": vd.get("aid", 0),
        "cid": vd.get("cid", 0),
        "duration": {
            "seconds": dsec,
            "formatted": f"{dsec//60}:{str(dsec%60).zfill(2)}"
        },
        "resolution": {
            "width": vd.get("dimension",{}).get("width",0),
            "height": vd.get("dimension",{}).get("height",0)
        },
        "category": {
            "main": vd.get("tname",""),
            "sub": vd.get("tname_v2","")
        },
        "publishTime": datetime.fromtimestamp(vd.get("pubdate",0)).isoformat(),
        "description": vd.get("desc",""),
        "coverUrl": cover
    }
    ow = vd.get("owner",{})
    owner_info = {
        "uid": ow.get("mid",0),
        "username": ow.get("name",""),
        "avatarUrl": ow.get("face","")
    }
    st = vd.get("stat",{})
    statistics = {
        "views": st.get("view",0),
        "danmaku": st.get("danmaku",0),
        "comments": st.get("reply",0),
        "favorites": st.get("favorite",0),
        "coins": st.get("coin",0),
        "shares": st.get("share",0),
        "likes": st.get("like",0),
        "historicalRank": st.get("his_rank",0)
    }
    return {
        "basic": basic,
        "owner": owner_info,
        "statistics": statistics
    }


def build_bilibili_caption(parsed_info: dict) -> str:
    b = parsed_info["basic"]
    lines = [
        f"<b>标题</b>: {b['title']}",
        f"BV号: {b['bvid']}",
        f"AID: {b['aid']}",
        f"CID: {b['cid']}",
        f"时长: {b['duration']['formatted']} ({b['duration']['seconds']} 秒)",
        f"分辨率: {b['resolution']['width']}x{b['resolution']['height']}",
        f"分区: {b['category']['main']}",
        f"发布时间: {b['publishTime']}",
        b['description']
    ]
    o = parsed_info["owner"]
    lines.append(f"\n<b>UP主</b>: {o['username']} (UID: {o['uid']})")
    s = parsed_info["statistics"]
    stat_line = (
        f"播放: {s['views']} | 弹幕: {s['danmaku']} | 评论: {s['comments']} | "
        f"收藏: {s['favorites']} | 投币: {s['coins']} | 分享: {s['shares']} | 点赞: {s['likes']}"
    )
    if s['historicalRank']>0:
        stat_line += f" | 最高排行: {s['historicalRank']}"
    lines.append(f"\n{stat_line}")
    return "\n".join(lines)


def sync_fetch_one_video_info_and_cid(bvid: str):
    """在线程池里调用的同步方法"""
    url = f"http://localhost:8080/api/bilibili/web/fetch_one_video?bv_id={bvid}"
    try:
        r = requests.get(url, timeout=10).json()
    except:
        return None,0,None,False

    if r.get("data",{}).get("code") == 62002:
        return None,0,None,True
    v_data = r.get("data",{}).get("data",{})
    if not v_data or not v_data.get("bvid"):
        return None,0,None,False

    p = parse_bilibili_video_info(r)
    cid = v_data.get("cid",0)
    from datetime import datetime
    vi = VideoInfo(
        bvid=v_data.get("bvid",""),
        aid=v_data.get("aid",0),
        title=v_data.get("title",""),
        duration=v_data.get("duration",0),
        publish_time=datetime.fromtimestamp(v_data.get("pubdate",0)),
        view_count=v_data.get("stat",{}).get("view",0),
        danmaku_count=v_data.get("stat",{}).get("danmaku",0),
        like_count=v_data.get("stat",{}).get("like",0),
        coin_count=v_data.get("stat",{}).get("coin",0),
        favorite_count=v_data.get("stat",{}).get("favorite",0),
        share_count=v_data.get("stat",{}).get("share",0),
        reply_count=v_data.get("stat",{}).get("reply",0),
        dimension=v_data.get("dimension",{})
    )
    return vi, cid, p, False


# ========== 3) Telegram Bot 逻辑 ==========

def get_random_session_name():
    return f"bot_session_{uuid4().hex[:8]}"

app = PyroClient(
    name=get_random_session_name(),
    api_id=api_id,
    api_hash=api_hash,
    bot_token=bot_token,
    no_updates=False,
    workers=4,
    in_memory=True
)

def clean_session():
    # 清理Pyrogram本地session等
    import glob
    session_files = glob.glob("bot_session_*.session*") + glob.glob("upload_session_*.session*")
    for sf in session_files:
        try:
            os.remove(sf)
        except:
            pass

# 安全发送方法
async def safe_send_message(client: PyroClient, chat_id, text, **kwargs):
    while True:
        try:
            return await client.send_message(chat_id, text, **kwargs)
        except FloodWait as e:
            logger.warning(f"send_message FloodWait: {e.value}")
            await asyncio.sleep(e.value + 1)
        except Exception as ex:
            logger.warning(f"send_message 出错: {ex}")
            break

async def safe_edit_text(msg: Message, text, **kwargs):
    while True:
        try:
            return await msg.edit_text(text, **kwargs)
        except FloodWait as e:
            logger.warning(f"edit_text FloodWait: {e.value}")
            await asyncio.sleep(e.value + 1)
        except Exception as ex:
            logger.warning(f"edit_text 出错: {ex}")
            break

async def safe_send_video(client: PyroClient, chat_id, video, **kwargs):
    while True:
        try:
            return await client.send_video(chat_id, video, **kwargs)
        except FloodWait as e:
            logger.warning(f"send_video FloodWait: {e.value}")
            await asyncio.sleep(e.value + 1)
        except Exception as ex:
            logger.warning(f"send_video 出错: {ex}")
            break

async def safe_send_document(client: PyroClient, chat_id, document, **kwargs):
    while True:
        try:
            return await client.send_document(chat_id, document, **kwargs)
        except FloodWait as e:
            logger.warning(f"send_document FloodWait: {e.value}")
            await asyncio.sleep(e.value + 1)
        except Exception as ex:
            logger.warning(f"send_document 出错: {ex}")
            break
    return None

async def safe_send_cached_media(client: PyroClient, chat_id, file_id, **kwargs):
    """安全地发送缓存媒体，处理FloodWait错误"""
    while True:
        try:
            return await client.send_cached_media(chat_id, file_id, **kwargs)
        except FloodWait as e:
            logger.warning(f"send_cached_media FloodWait: {e.value}")
            await asyncio.sleep(e.value + 1)
        except Exception as ex:
            logger.warning(f"send_cached_media 出错: {ex}")
            break
    return None

# 全局设置
BV_PATTERN = r"(BV[0-9A-Za-z]+)"
CONCURRENCY = 3  # 增加到3个并发下载任务
pipeline_semaphore = asyncio.Semaphore(CONCURRENCY)
thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=8)  # 增加到8个线程

# 全局统计
PROCESSED_BVID_CIDS: Dict[str, List[str]] = {}
FAILURES: set = set()  # 只存失败的bvid，不含原因
FOLDER_STATS: Dict[str, dict] = {}
# FOLDER_STATS[folder_id] = {
#   "title": str,
#   "bvids": set([...]),
#   "processed_bvids": set([...]),
#   "failed_bvids": set([...])
# }

monitor_started = False        # 是否已启动监控线程
monitor_paused = False         # 监听是否暂停（优先处理用户任务时）
stop_monitor = False           # 如果需要彻底停止监控

# 新增：监控间隔（秒），默认为 5秒
monitor_interval = 3600  # 改为默认1小时

# 要监控的用户ID
target_uid = os.environ.get('BILI_TARGET_UID', "12954775")

def load_processed_entries():
    # This function has been removed as we no longer read from JSON files
    pass

def save_processed_entries():
    # This function has been removed as we no longer write to JSON files
    pass

async def _generate_thumb_and_probe(video_path: Path, bvid: str, cid: str):
    jpg_path = video_path.with_suffix('.jpg')
    cmd_thumb = ['ffmpeg','-y','-i',str(video_path),'-ss','00:00:01','-vframes','1','-f','image2',str(jpg_path)]
    try:
        subprocess.run(cmd_thumb, check=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
    except Exception as e:
        logger.warning(f"生成缩略图失败: {e}")

    cmd_probe = [
        'ffprobe','-v','quiet','-print_format','json',
        '-show_format','-show_streams',str(video_path)
    ]
    probe_json = video_path.with_suffix('.json')
    try:
        out = subprocess.check_output(cmd_probe)
        j = json.loads(out)
        with open(probe_json, 'w', encoding='utf-8') as f:
            json.dump(j, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logger.warning(f"ffprobe失败: {e}")


STREAMS_CACHE: Dict[Tuple[str,str], dict] = {}

async def send_video_to_telegram(
    client: PyroClient, chat_id: int,
    video_path: str,
    caption: str,
    source_url: str="",
    cover_url: str=None,
    bvid: str="",
    cid: int=0,
    show_quality_buttons: bool=False,
    extra_folder_info: str=None
) -> bool:
    try:
        vp = Path(video_path)
        if not vp.exists():
            return False

        await _generate_thumb_and_probe(vp, bvid, str(cid))
        thumb_path = vp.with_suffix('.jpg')
        probe_path = vp.with_suffix('.json')

        format_info = ""
        if probe_path.exists():
            with open(probe_path, 'r', encoding='utf-8') as f:
                j = json.load(f)
            # 获取视频流
            vs = None
            for s in j.get("streams", []):
                if s.get("codec_type") == "video":
                    vs = s
                    break
            if vs:
                w = vs.get("width",0)
                h = vs.get("height",0)
                c = vs.get("codec_name","")
                fr_str = vs.get("r_frame_rate","0/0")
                fr_val=0
                try:
                    n,d=fr_str.split("/")
                    if int(d)!=0:
                        fr_val=round(int(n)/int(d))
                except:
                    pass
                br = float(vs.get("bit_rate",0))
                if br<=0:
                    br = float(j.get("format",{}).get("bit_rate",0))
                mbps = br/1024/1024 if br>0 else 0
                dur = float(j.get("format",{}).get("duration",0))
                size_mb = (br/8)*dur/(1024*1024) if br>0 else 0
                lines = [
                    "<b>[视频流信息]</b>",
                    f"分辨率: {w}x{h}",
                    f"编码: {c}",
                    f"帧率: {fr_val} fps",
                    f"码率: {mbps:.2f} Mbps",
                    f"时长: {int(dur)} 秒",
                    f"预计大小: {size_mb:.2f} MB"
                ]
                format_info="\n".join(lines)

            total_dur = int(float(j.get("format",{}).get("duration",0)))
            if total_dur>0:
                fm = f"{total_dur//60}:{str(total_dur%60).zfill(2)}"
                caption = re.sub(r'时长: \d+:\d+ \(\d+ 秒\)',
                                 f'时长: {fm} ({total_dur} 秒)',
                                 caption)

        if extra_folder_info:
            caption += f"\n\n{extra_folder_info}"

        # 按钮
        row1 = [InlineKeyboardButton("🎬 打开作品", url=source_url)]
        if cover_url:
            row1.append(InlineKeyboardButton("🖼 获取封面", url=cover_url))
        base_btn = [row1]

        uid_match = re.search(r'UID:\s*(\d+)', caption)
        if uid_match:
            uid_val=uid_match.group(1)
            row2 = [
                InlineKeyboardButton("📚 获取全部作品", callback_data=f"get_all_videos:{uid_val}"),
                InlineKeyboardButton("💾 获取收藏夹", callback_data=f"get_user_favorites:{uid_val}")
            ]
            base_btn.append(row2)

        quality_buttons = []
        if show_quality_buttons and (bvid,str(cid)) in STREAMS_CACHE:
            st_data = STREAMS_CACHE[(bvid,str(cid))]
            a_s = st_data["audio_stream"]
            vs_list = st_data["streams"]
            row=[]
            for s in vs_list:
                qn=s["qn"]
                cdc=s["codec"]
                short_c=cdc.split(".")[0]
                cb=f"s:{bvid}:{cid}:{qn}:{cdc}"
                btn_text=f"{s['quality_name']}({short_c})"
                row.append(InlineKeyboardButton(btn_text, callback_data=cb))
                if len(row)==3:
                    quality_buttons.append(row)
                    row=[]
            if row:
                quality_buttons.append(row)

        progress_msg = await safe_send_message(client, chat_id, "正在发送视频...")
        # 决定发送视频的宽高等
        width, height, dur_ = 0,0,0
        if probe_path.exists():
            with open(probe_path,'r',encoding='utf-8') as f:
                jinfo=json.load(f)
            for s_ in jinfo.get("streams",[]):
                if s_.get("codec_type")=="video":
                    width=s_.get("width",0)
                    height=s_.get("height",0)
                    break
            dur_=int(float(jinfo.get("format",{}).get("duration",0)))

        # 发送视频
        video_msg = await safe_send_video(
            client, chat_id, str(vp),
            caption=caption, parse_mode=ParseMode.HTML,
            thumb=str(thumb_path) if thumb_path.exists() else None,
            duration=dur_ if dur_>0 else None,
            width=width if width>0 else None,
            height=height if height>0 else None,
            supports_streaming=True,
            reply_markup=InlineKeyboardMarkup(base_btn),
            progress=progress_callback
        )
        
        # 获取视频的file_id
        video_file_id = None
        if video_msg and hasattr(video_msg, 'video') and video_msg.video:
            video_file_id = video_msg.video.file_id

        doc_lines = []
        if bvid:
            doc_lines.append(f"bv_id:{bvid}")
        if cid:
            doc_lines.append(f"cid:{cid}")
        if format_info:
            doc_caption = f"{format_info}\n\n" + "\n".join(doc_lines)
        else:
            doc_caption = "\n".join(doc_lines)

        # 发送文件
        file_msg = None
        if doc_caption.strip():
            await safe_edit_text(progress_msg, "准备以文件形式发送...")
            file_msg = await safe_send_document(
                client, chat_id, str(vp),
                caption=doc_caption,
                parse_mode=ParseMode.HTML,
                thumb=str(thumb_path) if thumb_path.exists() else None,
                reply_markup=InlineKeyboardMarkup(quality_buttons) if quality_buttons else None,
                progress=progress_callback
            )

        # 获取文件的file_id
        file_file_id = None
        if file_msg and hasattr(file_msg, 'document') and file_msg.document:
            file_file_id = file_msg.document.file_id
        elif file_msg and hasattr(file_msg, 'video') and file_msg.video:
            # 如果作为document发送的文件被识别为视频，使用video file_id
            file_file_id = file_msg.video.file_id
            
        # 如果仍然没有file_file_id但有video_file_id，则复制video_file_id
        if not file_file_id and video_file_id:
            file_file_id = video_file_id
            logger.info(f"文件太小被识别为视频，复制video_file_id到file_file_id: {bvid}/{cid}")

        # 保存到数据库
        folder_id_str = None
        folder_name_str = None
        is_in_fav = False
        if extra_folder_info:
            # 从extra_folder_info中提取folder_id和folder_name
            folder_id_match = re.search(r'ID: ([^)]+)', extra_folder_info)
            folder_name_match = re.search(r'所属收藏夹: (.*?) \(ID:', extra_folder_info)
            if folder_id_match:
                folder_id_str = folder_id_match.group(1)
                is_in_fav = True
            if folder_name_match:
                folder_name_str = folder_name_match.group(1)
                
        # 从caption中提取UP主信息
        up_name = ""
        uid_str = ""
        up_match = re.search(r'<b>UP主</b>: (.*?) \(UID: (\d+)\)', caption)
        if up_match:
            up_name = up_match.group(1)
            uid_str = up_match.group(2)

        # 保存记录到数据库
        await save_to_database(
            bvid=bvid,
            cid=str(cid),
            video_file_id=video_file_id,
            file_file_id=file_file_id,
            video_caption=caption,
            file_caption=doc_caption,
            is_in_fav=is_in_fav,
            folder_id=folder_id_str,
            folder_name=folder_name_str,
            is_failure=False,
            up=up_name,
            uid=uid_str
        )

        await safe_edit_text(progress_msg, "发送完成")
        await asyncio.sleep(2)
        await progress_msg.delete()

        # 不再删除文件，保留在本地
        # try:
        #     vp.unlink(missing_ok=True)
        #     thumb_path.unlink(missing_ok=True)
        #     probe_path.unlink(missing_ok=True)
        # except:
        #     pass
        
        # 只删除缩略图和探测文件，保留视频文件
        try:
            thumb_path.unlink(missing_ok=True)
            probe_path.unlink(missing_ok=True)
        except:
            pass
            
        logger.info(f"文件 {video_path} 已保留在本地")
        return True
    except Exception as e:
        logger.warning(f"send_video_to_telegram失败: {e}")
        
        # 记录失败信息到数据库
        if bvid and cid:
            # 尝试从caption中提取UP主信息（如果有）
            up_name = ""
            uid_str = ""
            if caption:
                up_match = re.search(r'<b>UP主</b>: (.*?) \(UID: (\d+)\)', caption)
                if up_match:
                    up_name = up_match.group(1)
                    uid_str = up_match.group(2)
            
            # 提取收藏夹信息
            folder_id_str = None
            folder_name_str = None
            is_in_fav = False
            if extra_folder_info:
                folder_id_match = re.search(r'ID: ([^)]+)', extra_folder_info)
                folder_name_match = re.search(r'所属收藏夹: (.*?) \(ID:', extra_folder_info)
                if folder_id_match:
                    folder_id_str = folder_id_match.group(1)
                    is_in_fav = True
                if folder_name_match:
                    folder_name_str = folder_name_match.group(1)
                    
            await save_to_database(
                bvid=bvid,
                cid=str(cid),
                is_failure=True,
                up=up_name,
                uid=uid_str,
                is_in_fav=is_in_fav,
                folder_id=folder_id_str,
                folder_name=folder_name_str
            )
            
        return False


async def pipeline_single_bvid(client: PyroClient, chat_id: int, bvid: str,
                               folder_name:str="", folder_id:str="") -> bool:
    """下载并发送 bvid 的所有分P。可带收藏夹信息"""
    # 暂停监控
    global monitor_paused, FOLDER_STATS
    monitor_paused = True

    async with pipeline_semaphore:
        logger.info(f"[pipeline] processing bvid={bvid} ...")
        
        # 首先检查是否已经处理过该视频
        already_processed = await is_video_already_processed(bvid)
        if already_processed:
            logger.info(f"视频 {bvid} 已经处理过，跳过下载")
            
            # 更新全局统计数据
            if folder_id and folder_id in FOLDER_STATS and bvid in FOLDER_STATS[folder_id]["bvids"]:
                FOLDER_STATS[folder_id]["processed_bvids"].add(bvid)
                # 如果之前在失败列表中，移除
                if bvid in FOLDER_STATS[folder_id]["failed_bvids"]:
                    FOLDER_STATS[folder_id]["failed_bvids"].remove(bvid)
                logger.info(f"视频 {bvid} 更新为已处理状态")
            
            # 确保数据库中的每个记录都标记为processed
            try:
                update_data = filter_db_fields({
                    "status": "processed",
                    "is_failure": False
                })
                
                supabase.table("bilibili").update(update_data).eq("bvid", bvid).eq("status", "pending").execute()
                logger.info(f"已将视频 {bvid} 的所有pending记录更新为processed状态")
            except Exception as e:
                logger.error(f"更新视频状态失败 {bvid}: {e}")
                
            monitor_paused = False
            return True
            
        loop = asyncio.get_running_loop()
        vi, cid, parsed_data, is_deleted = await loop.run_in_executor(
            thread_pool, sync_fetch_one_video_info_and_cid, bvid
        )
        if is_deleted:
            logger.info(f"稿件不可见: {bvid}")
            
            # 查询当前重试次数
            try:
                # 记录失败到数据库
                await save_to_database(
                    bvid=bvid,
                    cid="0",  # 没有cid，使用0作为占位符
                    is_failure=True,
                    up="",  # 稿件不可见，无法获取UP主信息
                    uid="",
                    is_in_fav=bool(folder_id),
                    folder_id=folder_id,
                    folder_name=folder_name
                )
                
                # 更新数据库中该bvid所有记录为失败
                update_data = filter_db_fields({
                    "status": "failed",
                    "is_failure": True
                })
                
                supabase.table("bilibili").update(update_data).eq("bvid", bvid).execute()
                logger.info(f"已将视频 {bvid} 的所有记录更新为失败状态（稿件不可见）")
            except Exception as e:
                logger.error(f"更新视频状态失败 {bvid}: {e}")
                
            # 添加到全局失败列表（仅用于日志记录）
            FAILURES.add(bvid)
                
            # 更新全局统计数据
            if folder_id and folder_id in FOLDER_STATS and bvid in FOLDER_STATS[folder_id]["bvids"]:
                FOLDER_STATS[folder_id]["failed_bvids"].add(bvid)
                logger.info(f"视频 {bvid} 不可见，标记为失败")
                
            # 恢复监控
            monitor_paused = False
            return False
            
        if not cid or not vi or not parsed_data:
            logger.info(f"未能获取到video info/cid: {bvid}")
            
            try:
                # 记录失败到数据库
                await save_to_database(
                    bvid=bvid,
                    cid="0",  # 没有cid，使用0作为占位符
                    is_failure=True,
                    up="",  # 无法获取UP主信息
                    uid="",
                    is_in_fav=bool(folder_id),
                    folder_id=folder_id,
                    folder_name=folder_name
                )
                
                # 更新数据库中该bvid所有记录为失败
                update_data = filter_db_fields({
                    "status": "failed",
                    "is_failure": True
                })
                
                supabase.table("bilibili").update(update_data).eq("bvid", bvid).execute()
                logger.info(f"已将视频 {bvid} 的所有记录更新为失败状态（无法获取信息）")
            except Exception as e:
                logger.error(f"更新视频状态失败 {bvid}: {e}")
            
            # 添加到全局失败列表（仅用于日志记录）
            FAILURES.add(bvid)
                
            # 更新全局统计数据
            if folder_id and folder_id in FOLDER_STATS and bvid in FOLDER_STATS[folder_id]["bvids"]:
                FOLDER_STATS[folder_id]["failed_bvids"].add(bvid)
                logger.info(f"视频 {bvid} 信息获取失败，标记为失败")
                
            # 恢复监控
            monitor_paused = False
            return False

        extra_info = None
        if folder_name and folder_id:
            extra_info = f"所属收藏夹: {folder_name} (ID: {folder_id})"

        cover_url = parsed_data["basic"]["coverUrl"]
        cap = build_bilibili_caption(parsed_data)
        source_url = f"https://www.bilibili.com/video/{bvid}"
        
        # 获取UP主名字
        up_name = parsed_data["owner"]["username"] if parsed_data and "owner" in parsed_data else ""

        from_ff = AsyncBiliDownloader()
        results = await from_ff.download_video(bvid, up_name)
        if not results:
            try:
                # 尝试从parsed_data中提取UP主信息
                up_name = ""
                uid_str = ""
                if parsed_data and "owner" in parsed_data:
                    owner = parsed_data["owner"]
                    up_name = owner.get("username", "")
                    uid_str = str(owner.get("uid", ""))
                    
                # 记录下载失败到数据库
                await save_to_database(
                    bvid=bvid,
                    cid=cid if isinstance(cid, str) else str(cid),
                    is_failure=True,
                    up=up_name,
                    uid=uid_str,
                    is_in_fav=bool(folder_id),
                    folder_id=folder_id,
                    folder_name=folder_name
                )
                
                # 更新数据库中该bvid所有记录为失败
                update_data = filter_db_fields({
                    "status": "failed",
                    "is_failure": True
                })
                
                supabase.table("bilibili").update(update_data).eq("bvid", bvid).execute()
                logger.info(f"已将视频 {bvid} 的所有记录更新为失败状态（下载失败）")
            except Exception as e:
                logger.error(f"更新视频状态失败 {bvid}: {e}")
            
            # 添加到全局失败列表（仅用于日志记录）
            FAILURES.add(bvid)
            
            # 更新全局统计数据
            if folder_id and folder_id in FOLDER_STATS and bvid in FOLDER_STATS[folder_id]["bvids"]:
                FOLDER_STATS[folder_id]["failed_bvids"].add(bvid)
                logger.info(f"视频 {bvid} 下载失败，标记为失败")
                
            monitor_paused=False
            return False

        any_ok = False
        for r in results:
            # 检查这个分P是否已经处理过
            part_already_processed = await is_video_already_processed(bvid, r.cid)
            if part_already_processed:
                logger.info(f"视频 {bvid} 的分P {r.cid} 已经处理过，跳过")
                # 确保数据库标记为已处理
                try:
                    update_data = filter_db_fields({
                        "status": "processed",
                        "is_failure": False
                    })
                    
                    supabase.table("bilibili").update(update_data).eq("bvid", bvid).eq("cid", r.cid).eq("status", "pending").execute()
                    logger.info(f"已将视频 {bvid}/{r.cid} 更新为processed状态")
                except Exception as e:
                    logger.error(f"更新视频状态失败 {bvid}/{r.cid}: {e}")
                
                any_ok = True
                continue
                
            if not r.success:
                # 标记单个分P为失败
                try:
                    update_data = filter_db_fields({
                        "status": "failed",
                        "is_failure": True
                    })
                    
                    supabase.table("bilibili").update(update_data).eq("bvid", bvid).eq("cid", r.cid).execute()
                    logger.info(f"已将视频 {bvid}/{r.cid} 更新为失败状态（下载失败）")
                except Exception as e:
                    logger.error(f"更新视频状态失败 {bvid}/{r.cid}: {e}")
                
                continue
            
            # 存流信息
            try:
                pi = await from_ff.get_play_info(bvid, r.cid)
                dash_data = pi["data"].get("dash", {})
                audio_s = None
                if dash_data.get("flac") and dash_data["flac"].get("audio"):
                    audio_s = dash_data["flac"]["audio"]
                else:
                    auds = dash_data.get("audio",[])
                    auds = sorted(auds, key=lambda x: x.get("bandwidth",0), reverse=True)
                    audio_s = auds[0] if auds else None
                st_list = from_ff.list_all_streams(pi)
                if audio_s and st_list:
                    STREAMS_CACHE[(bvid, r.cid)] = {
                        "audio_stream": audio_s,
                        "streams": st_list
                    }
            except:
                pass

            # 发送
            ok = await send_video_to_telegram(
                client, chat_id, r.file_path,
                caption=cap,
                source_url=source_url,
                cover_url=cover_url,
                bvid=bvid,
                cid=int(r.cid),
                show_quality_buttons=True,
                extra_folder_info=extra_info
            )
            
            # 不论上传成功与否，显式地更新这个分P的状态
            if ok:
                try:
                    update_data = filter_db_fields({
                        "status": "processed",
                        "is_failure": False
                    })
                    
                    supabase.table("bilibili").update(update_data).eq("bvid", bvid).eq("cid", r.cid).execute()
                    logger.info(f"已将视频 {bvid}/{r.cid} 更新为processed状态")
                except Exception as e:
                    logger.error(f"更新视频状态失败 {bvid}/{r.cid}: {e}")
                
                any_ok = True
                # 实时更新全局统计
                if folder_id and folder_id in FOLDER_STATS and bvid in FOLDER_STATS[folder_id]["bvids"]:
                    FOLDER_STATS[folder_id]["processed_bvids"].add(bvid)
                    # 如果之前在失败列表中，则移除
                    if bvid in FOLDER_STATS[folder_id]["failed_bvids"]:
                        FOLDER_STATS[folder_id]["failed_bvids"].remove(bvid)
                    logger.info(f"视频 {bvid} 处理成功，更新全局统计")
            else:
                # 标记失败
                try:
                    update_data = filter_db_fields({
                        "status": "failed",
                        "is_failure": True
                    })
                    
                    supabase.table("bilibili").update(update_data).eq("bvid", bvid).eq("cid", r.cid).execute()
                    logger.info(f"已将视频 {bvid}/{r.cid} 更新为失败状态（上传失败）")
                except Exception as e:
                    logger.error(f"更新视频状态失败 {bvid}/{r.cid}: {e}")

        # 如果所有分P都失败，记录到全局失败列表（仅用于日志记录）
        if not any_ok:
            FAILURES.add(bvid)
            # 更新全局统计数据
            if folder_id and folder_id in FOLDER_STATS and bvid in FOLDER_STATS[folder_id]["bvids"]:
                FOLDER_STATS[folder_id]["failed_bvids"].add(bvid)
                logger.info(f"视频 {bvid} 所有分P处理失败，标记为失败")

        # 恢复监控
        monitor_paused = False
        return any_ok


async def fetch_user_collect_folders(uid:str) -> List[dict]:
    """
    异步获取用户收藏夹列表，通过调用本地后端接口。
    
    接口地址：
      http://localhost:8080/api/bilibili/web/fetch_collect_folders?uid={uid}
      
    返回数据的层级结构应为：
      {
        "data": {
          "data": {
            "list": [ ... ]
          }
        }
      }
    """
    def _inner(u:str):
        try:
            logger.info(f"正在获取用户 {u} 的收藏夹列表...")
            url = f"http://localhost:8080/api/bilibili/web/fetch_collect_folders?uid={u}"
            r = requests.get(url, timeout=10).json()
            folders = r.get("data", {}).get("data", {}).get("list", [])
            logger.info(f"获取到 {len(folders)} 个收藏夹")
            for folder in folders:
                logger.info(f"收藏夹: {folder.get('title', '无标题')} (ID: {folder.get('id', 'unknown')})")
            return folders
        except Exception as e:
            logger.error(f"获取收藏夹列表失败: {e}")
            return []
    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(thread_pool, _inner, uid)

async def fetch_folder_bvids(folder_id: str, check_already_processed: bool = False) -> List[str]:
    """
    异步获取指定收藏夹内所有视频的 BV 号。
    
    接口地址：
      http://localhost:8080/api/bilibili/web/fetch_user_collection_videos?folder_id={folder_id}&pn={page}
    
    参数:
      folder_id: 收藏夹ID
      check_already_processed: 是否检查已处理视频并提前结束
    
    返回：
      bvid列表
    """
    def _inner(folder_id: str, check_already_processed: bool) -> List[str]:
        base_url = "http://localhost:8080/api/bilibili/web/fetch_user_collection_videos"
        out = []
        page = 1
        while True:
            try:
                url = f"{base_url}?folder_id={folder_id}&pn={page}"
                j = requests.get(url, timeout=10).json()
                # 从返回的 JSON 中取出数据节点
                dd = j.get("data", {}).get("data", {})
                has_more = dd.get("has_more", False)
                medias = dd.get("medias", [])
                
                # 检查该页视频是否已处理
                if check_already_processed and medias:
                    all_processed = True
                    for m in medias:
                        if "bvid" in m:
                            bvid = m["bvid"]
                            # 检查是否已处理过
                            try:
                                query = supabase.table("bilibili").select("bvid,is_failure,status").eq("bvid", bvid).execute()
                                records = query.data if query.data else []
                                # 如果没有记录，或者所有记录都是失败或待处理，则认为未处理
                                if not records:
                                    all_processed = False
                                    break
                                has_success = any(not r.get("is_failure", False) and r.get("status") != "pending" for r in records)
                                has_pending = any(r.get("status") == "pending" for r in records)
                                if not has_success and not has_pending:
                                    all_processed = False
                                    break
                            except Exception:
                                all_processed = False
                                break
                    
                    # 如果该页全部都已处理过，说明后面的也都处理过，直接返回已收集的bvid
                    if all_processed and out:
                        logger.info(f"收藏夹 {folder_id} 第{page}页开始全部已处理，跳过后续页")
                        return out
                
                # 添加本页视频
                for m in medias:
                    if "bvid" in m:
                        out.append(m["bvid"])
                
                if not has_more:
                    break
                page += 1
            except Exception as e:
                logger.error(f"获取收藏夹视频列表失败: {e}")
                break
        return out

    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(thread_pool, _inner, folder_id, check_already_processed)

async def get_cids_for_bvid(bvid:str) -> List[str]:
    """获取 bvid 对应的所有 cid"""
    try:
        # 首先检查数据库中是否有记录，如果有且is_failure=true，则直接返回空列表
        result = supabase.table("bilibili").select("is_failure").eq("bvid", bvid).execute()
        if result.data:
            # 检查是否所有记录都是失败的
            all_failed = all(record.get("is_failure", False) for record in result.data)
            if all_failed:
                logger.info(f"跳过已知失效视频: {bvid}")
                return []

        logger.info(f"正在获取视频分P信息: {bvid}")
        parts = await AsyncBiliDownloader().get_video_parts(bvid)
        logger.info(f"获取到视频 {bvid} 的 {len(parts)} 个分P")
        return [str(p["cid"]) for p in parts]
    except Exception as e:
        logger.error(f"获取视频 {bvid} 的分P失败: {e}")
        return []

def _sync_fetch_all_bvids(uid: str) -> List[str]:
    """
    同步获取用户所有作品的 BV 号，通过调用本地后端接口。
    
    接口地址：
      http://localhost:8080/api/bilibili/web/fetch_user_post_videos?uid={uid}&pn={page}
      
    返回数据的层级结构应为：
      {
        "data": {
          "data": {
            "page": { ... },
            "list": {
              "vlist": [
                { "bvid": "BV1...", ... },
                { "bvid": "BV2...", ... },
                ...
              ]
            }
          }
        }
      }
    """
    base_url = "http://localhost:8080/api/bilibili/web/fetch_user_post_videos"
    page_size = 20  # 每页数量（根据后端实际情况设置）
    all_bvids = []
    page = 1
    while True:
        try:
            url = f"{base_url}?uid={uid}&pn={page}"
            r = requests.get(url, timeout=10).json()
            # 解析返回数据
            data_data = r.get("data", {}).get("data", {})
            page_info = data_data.get("page", {})
            list_data = data_data.get("list", {})
            vlist = list_data.get("vlist", [])
            if not vlist:
                break  # 如果没有视频数据，则退出循环
            for v in vlist:
                if "bvid" in v:
                    all_bvids.append(v["bvid"])
            total_count = page_info.get("count", 0)
            total_pages = math.ceil(total_count / page_size)
            if page >= total_pages:
                break
            page += 1
        except Exception as e:
            # 发生异常时退出循环
            break
    return all_bvids

async def fetch_all_bvids(uid: str) -> List[str]:
    """
    异步获取用户所有作品的 BV 号，内部调用同步函数 _sync_fetch_all_bvids。
    """
    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(thread_pool, _sync_fetch_all_bvids, uid)

async def monitor_favorites():
    """监控收藏夹，处理标记为pending的视频"""
    global stop_monitor, monitor_paused, monitor_interval, target_uid, FOLDER_STATS
    logger.info(f"开始处理待处理的视频")
    
    # 存储上一次的收藏夹列表，用于比较变化
    last_folders = {}
    
    while not stop_monitor:
        try:
            if monitor_paused:
                logger.info("监控暂停中，等待用户任务完成...")
                while monitor_paused and not stop_monitor:
                    await asyncio.sleep(1)
                if stop_monitor:
                    break
            
            # 每次循环都检查收藏夹的变化
            folders = await fetch_user_collect_folders(target_uid)
            if folders:
                current_folders = {str(folder.get("id")): folder.get("title", "未命名收藏夹") for folder in folders}
                
                # 比较收藏夹变化
                if last_folders:
                    # 检查名称变更
                    for folder_id, folder_name in current_folders.items():
                        if folder_id in last_folders and last_folders[folder_id] != folder_name:
                            logger.info(f"检测到收藏夹名称变更: ID {folder_id} 从 '{last_folders[folder_id]}' 变更为 '{folder_name}'")
                            
                            # 更新数据库中的收藏夹名称
                            try:
                                update_data = filter_db_fields({
                                    "folder_name": folder_name
                                })
                                
                                supabase.table("bilibili").update(update_data).eq("folder_id", folder_id).execute()
                                logger.info(f"已更新数据库中 {folder_id} 的收藏夹名称为 '{folder_name}'")
                            except Exception as e:
                                logger.error(f"更新收藏夹名称失败 {folder_id}: {e}")
                    
                    # 检查新增收藏夹
                    new_folders = set(current_folders.keys()) - set(last_folders.keys())
                    if new_folders:
                        logger.info(f"检测到新增收藏夹: {', '.join([f'{current_folders[fid]} (ID: {fid})' for fid in new_folders])}")
                    
                    # 检查已删除收藏夹
                    removed_folders = set(last_folders.keys()) - set(current_folders.keys())
                    if removed_folders:
                        logger.info(f"检测到删除收藏夹: {', '.join([f'{last_folders[fid]} (ID: {fid})' for fid in removed_folders])}")
                
                # 更新上一次的收藏夹列表
                last_folders = current_folders
            
            # 查询所有状态为pending的记录
            logger.info("查询所有待处理视频...")
            result = supabase.table("bilibili").select("bvid, cid, folder_id, folder_name").eq("status", "pending").execute()
            
            has_pending = result.data and len(result.data) > 0
            
            if has_pending:
                pending_videos = result.data
                logger.info(f"找到 {len(pending_videos)} 个待处理视频")
                
                # 按bvid分组处理
                bvid_groups = {}
                for video in pending_videos:
                    bvid = video.get("bvid")
                    if bvid not in bvid_groups:
                        bvid_groups[bvid] = []
                    bvid_groups[bvid].append(video)
                
                # 逐个处理每个bvid
                for bvid, videos in bvid_groups.items():
                    # 检查是否所有记录都是失败状态
                    check_failure = supabase.table("bilibili").select("is_failure").eq("bvid", bvid).execute()
                    if check_failure.data and all(record.get("is_failure", False) for record in check_failure.data):
                        logger.info(f"跳过已知失效视频: {bvid}")
                        # 将待处理记录标记为失败
                        for video in videos:
                            cid = video.get("cid")
                            try:
                                update_data = filter_db_fields({
                                    "status": "failed",
                                    "is_failure": True
                                })
                                
                                supabase.table("bilibili").update(update_data).eq("bvid", bvid).eq("cid", cid).execute()
                                logger.info(f"将待处理视频 {bvid}/{cid} 标记为失败（已知失效视频）")
                            except Exception as e:
                                logger.error(f"更新视频状态失败 {bvid}/{cid}: {e}")
                        continue
                    
                    # 所有视频应该属于同一个收藏夹，取第一个的folder信息
                    folder_id = videos[0].get("folder_id")
                    folder_name = videos[0].get("folder_name")
                    
                    if monitor_paused:
                        logger.info("监控暂停中，等待用户任务完成...")
                        while monitor_paused and not stop_monitor:
                            await asyncio.sleep(1)
                        if stop_monitor:
                            break
                    
                    logger.info(f"开始处理视频: {bvid}")
                    success = await pipeline_single_bvid(
                        app, 
                        AUTHORIZED_CHAT_ID,  # 使用环境变量定义的授权聊天ID
                        bvid,
                        folder_name=folder_name,
                        folder_id=folder_id
                    )
                    
                    # 处理完成后，更新数据库状态
                    for video in videos:
                        cid = video.get("cid")
                        try:
                            # 根据处理结果更新状态
                            if success:
                                update_data = filter_db_fields({
                                    "status": "processed",
                                    "is_failure": False
                                })
                            else:
                                update_data = filter_db_fields({
                                    "status": "failed",
                                    "is_failure": True
                                })
                                
                            supabase.table("bilibili").update(update_data).eq("bvid", bvid).eq("cid", cid).execute()
                            logger.info(f"更新视频 {bvid}/{cid} 状态为 {'processed' if success else 'failed'}")
                        except Exception as e:
                            logger.error(f"更新视频 {bvid}/{cid} 状态失败: {e}")
                    
                    # 处理完一个视频后，等待一段时间，避免过快发送
                    await asyncio.sleep(2)
            else:
                logger.info("没有待处理的视频，检查收藏夹是否有新增视频...")
                
                # 获取用户收藏夹列表已在上面完成，不需要重复获取
                if not folders:
                    logger.info(f"未能获取到用户 {target_uid} 的收藏夹列表，等待下一轮检查")
                    await asyncio.sleep(monitor_interval)
                    continue
                
                # 遍历每个收藏夹的第一页
                found_new_videos = False
                for folder in folders:
                    folder_id = str(folder.get("id"))
                    folder_name = folder.get("title", "未命名收藏夹")
                    
                    if not folder_id:
                        continue
                    
                    logger.info(f"检查收藏夹 {folder_name} (ID: {folder_id}) 的第一页视频...")
                    
                    # 只获取第一页的视频
                    base_url = "http://localhost:8080/api/bilibili/web/fetch_user_collection_videos"
                    try:
                        url = f"{base_url}?folder_id={folder_id}&pn=1"
                        j = await asyncio.get_event_loop().run_in_executor(
                            thread_pool, 
                            lambda: requests.get(url, timeout=10).json()
                        )
                        
                        # 从返回的 JSON 中取出数据节点
                        dd = j.get("data", {}).get("data", {})
                        medias = dd.get("medias", [])
                        
                        logger.info(f"收藏夹 {folder_name} 第一页有 {len(medias)} 个视频")
                        
                        # 检查每个视频是否已经在数据库中
                        for media in medias:
                            if "bvid" not in media:
                                continue
                                
                            bvid = media["bvid"]
                            
                            # 检查数据库中是否已有记录
                            result = supabase.table("bilibili").select("bvid").eq("bvid", bvid).execute()
                            if not result.data:
                                logger.info(f"发现新视频: {bvid}，准备添加到数据库")
                                found_new_videos = True
                                
                                # 获取视频的分P信息
                                cids = await get_cids_for_bvid(bvid)
                                
                                if not cids:
                                    logger.warning(f"无法获取视频 {bvid} 的分P信息，跳过")
                                    continue
                                
                                # 添加每个分P到数据库
                                for cid in cids:
                                    video_url = f"https://www.bilibili.com/video/{bvid}"
                                    data = filter_db_fields({
                                        "bvid": bvid,
                                        "cid": cid,
                                        "is_in_fav": True,
                                        "folder_id": folder_id,
                                        "folder_name": folder_name,
                                        "is_failure": False,
                                        "status": "pending",
                                        "up": "",  # 处理时会更新
                                        "uid": "",  # 处理时会更新
                                        "url": video_url,
                                        "created_at": datetime.now().isoformat()
                                    })
                                    
                                    try:
                                        supabase.table("bilibili").insert(data).execute()
                                        logger.info(f"已将视频 {bvid}/{cid} 标记为待处理")
                                    except Exception as e:
                                        # 可能是因为记录已存在
                                        logger.error(f"添加视频 {bvid}/{cid} 到数据库失败: {e}")
                        
                    except Exception as e:
                        logger.error(f"获取收藏夹 {folder_name} 内容失败: {e}")
                
                # 根据是否找到新视频决定下次检查的时间间隔
                if found_new_videos:
                    logger.info("发现新的视频，将立即开始处理")
                    # 不等待，立即开始下一轮循环处理新发现的视频
                    continue
                else:
                    logger.info(f"没有找到新增视频，等待 {monitor_interval} 秒后再次检查")
                    await asyncio.sleep(monitor_interval)
            
        except Exception as e:
            logger.error(f"处理视频出错: {e}")
            await asyncio.sleep(10)  # 出错后等待10秒再重试


# ========== 4) Bot命令处理 ==========

def get_or_create_user(chat_id, **kwargs):
    return {"chat_id": chat_id, "username": kwargs.get("username","")}

@app.on_message(filters.command(["start"]))
async def start_command(client: PyroClient, message: Message):
    if message.chat.id != AUTHORIZED_CHAT_ID:
        return
    global monitor_started, stop_monitor
    
    # 检查数据库中有多少待处理的视频
    try:
        result = supabase.table("bilibili").select("bvid,cid").eq("status", "pending").execute()
        count = len(result.data) if result.data else 0
        
        if monitor_started:
            await safe_send_message(client, message.chat.id, f"处理进程已在运行中，共有 {count} 个待处理视频")
        else:
            stop_monitor = False
            monitor_started = True
            # 启动线程
            import threading
            def run_monitor():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(monitor_favorites())
            t=threading.Thread(target=run_monitor, daemon=True)
            t.start()
            await safe_send_message(client, message.chat.id, f"手动启动处理进程，共有 {count} 个待处理视频")
    except Exception as e:
        await safe_send_message(client, message.chat.id, f"查询待处理视频失败: {e}")

@app.on_message(filters.command(["check"]))
async def check_command(client: PyroClient, message: Message):
    if message.chat.id != AUTHORIZED_CHAT_ID:
        return

    # 发送状态消息
    status_msg = await safe_send_message(
        client, 
        message.chat.id, 
        "正在获取收藏夹信息并比对数据库..."
    )

    try:
        # 使用全局统计数据 FOLDER_STATS
        global FOLDER_STATS, monitor_interval, monitor_started, stop_monitor
        
        logger.info("开始获取收藏夹数据并比对数据库")
        # 获取收藏夹列表
        fol_list = await fetch_user_collect_folders(target_uid)
        if not fol_list:
            await safe_edit_text(status_msg, "未获取到收藏夹列表，请检查接口是否正常")
            return
        
        # 首先检查数据库中是否有缺少URL字段的记录，如果有则更新
        try:
            missing_url_result = supabase.table("bilibili").select("bvid,cid").is_("url", "null").execute()
            missing_url_records = missing_url_result.data if missing_url_result.data else []
            
            if missing_url_records:
                await safe_edit_text(status_msg, f"找到 {len(missing_url_records)} 条记录缺少URL字段，正在更新...")
                
                for record in missing_url_records:
                    bvid = record.get("bvid")
                    cid = record.get("cid")
                    if bvid:
                        # 构建标准URL
                        video_url = f"https://www.bilibili.com/video/{bvid}"
                        try:
                            supabase.table("bilibili").update({
                                "url": video_url
                            }).eq("bvid", bvid).eq("cid", cid).execute()
                            logger.info(f"更新视频 {bvid}/{cid} 的URL")
                        except Exception as e:
                            logger.error(f"更新视频 {bvid}/{cid} 的URL失败: {e}")
                
                await safe_edit_text(status_msg, "URL字段更新完毕，继续检查收藏夹...")
        except Exception as e:
            logger.error(f"检查缺少URL字段的记录失败: {e}")
            # 继续执行后续操作
            
        # 临时存储更新的数据
        temp_folder_stats = {}
        
        # 跟踪是否所有收藏夹都已完全处理过
        all_folders_fully_processed = True
        
        # 处理每个收藏夹
        for folder in fol_list:
            fid = str(folder.get("id"))
            folder_title = folder.get("title", "无标题")
            if not fid:
                continue
            
            await safe_edit_text(status_msg, f"正在获取收藏夹 {folder_title} 的视频列表...")
            
            # 使用优化版的获取方法，当遇到已处理的视频时提前结束
            bvids = await fetch_folder_bvids(fid, check_already_processed=True)
            logger.info(f"收藏夹 {folder_title} (ID: {fid}) 有 {len(bvids)} 个视频")
            
            # 创建新的收藏夹统计
            temp_folder_stats[fid] = {
                "title": folder_title,
                "bvids": set(bvids),
                "processed_bvids": set(),
                "failed_bvids": set(),
                "pending_bvids": set()
            }
        
        total_videos_count = sum(len(info["bvids"]) for info in temp_folder_stats.values())
        processed_count = 0
        has_pending_videos = False
        
        # 逐个收藏夹处理
        for fid, info in temp_folder_stats.items():
            folder_title = info["title"]
            all_bvids = list(info["bvids"])
            
            # 检查是否有任何未处理的视频
            has_unprocessed = False
            
            # 逐个视频检查状态
            for i, bvid in enumerate(all_bvids):
                processed_count += 1
                if processed_count % 10 == 0 or processed_count == total_videos_count:
                    percent = (processed_count / total_videos_count) * 100
                    await safe_edit_text(status_msg, f"正在比对数据库 [{folder_title}] {processed_count}/{total_videos_count} ({percent:.1f}%)...")
                
                # 首先检查是否已知失效视频（全部记录都是is_failure=true）
                result = supabase.table("bilibili").select("is_failure").eq("bvid", bvid).execute()
                if result.data and all(record.get("is_failure", False) for record in result.data):
                    logger.info(f"跳过已知失效视频: {bvid}")
                    info["failed_bvids"].add(bvid)
                    continue
                
                # 获取视频的cid列表
                try:
                    cids = await get_cids_for_bvid(bvid)
                    if not cids:
                        logger.warning(f"无法获取视频 {bvid} 的cid")
                        continue
                        
                    # 查询数据库中该bvid的所有记录 - 移除锁定状态检查
                    result = supabase.table("bilibili").select("bvid,cid,is_failure,status,url").eq("bvid", bvid).execute()
                    records = result.data if result.data else []
                    
                    # 根据记录状态更新统计信息
                    if records:
                        # 判断该bvid的处理状态
                        all_failed = True  # 假设所有分P都失败
                        has_pending = False
                        
                        # 检查是否有记录缺少URL
                        for record in records:
                            # 如果URL为空，添加URL
                            if not record.get("url"):
                                record_bvid = record.get("bvid")
                                record_cid = record.get("cid")
                                video_url = f"https://www.bilibili.com/video/{record_bvid}"
                                try:
                                    supabase.table("bilibili").update({
                                        "url": video_url
                                    }).eq("bvid", record_bvid).eq("cid", record_cid).execute()
                                    logger.info(f"为视频 {record_bvid}/{record_cid} 添加URL: {video_url}")
                                except Exception as e:
                                    logger.error(f"更新视频 {record_bvid}/{record_cid} 的URL失败: {e}")
                        
                        for record in records:
                            if record.get("status") == "pending":
                                has_pending = True
                                has_pending_videos = True
                                break
                            if not record.get("is_failure", False):
                                all_failed = False  # 至少有一个分P成功
                        
                        if has_pending:
                            # 有待处理的分P
                            info["pending_bvids"].add(bvid)
                            has_unprocessed = True
                        elif all_failed:
                            # 所有分P都失败
                            info["failed_bvids"].add(bvid)
                            has_unprocessed = True
                        else:
                            # 至少有一个分P成功
                            info["processed_bvids"].add(bvid)
                    else:
                        # 数据库中没有记录，需要添加
                        has_unprocessed = True
                        has_pending_videos = True
                        for cid in cids:
                            # 构建标准格式URL
                            video_url = f"https://www.bilibili.com/video/{bvid}"
                            
                            data = {
                                "bvid": bvid,
                                "cid": cid,
                                "is_in_fav": True,
                                "folder_id": fid,
                                "folder_name": folder_title,
                                "is_failure": False,
                                "status": "pending",
                                "up": "",  # 暂时为空，处理时会更新
                                "uid": "",  # 暂时为空，处理时会更新
                                "url": video_url,  # 添加URL字段
                                "created_at": datetime.now().isoformat()
                            }
                            
                            # 插入记录
                            try:
                                supabase.table("bilibili").insert(data).execute()
                                logger.info(f"将视频 {bvid}/{cid} 标记为待处理")
                            except Exception as e:
                                logger.error(f"添加待处理记录失败 {bvid}/{cid}: {e}")
                        
                        # 更新统计
                        info["pending_bvids"].add(bvid)
                
                except Exception as e:
                    logger.error(f"处理视频 {bvid} 时出错: {e}")
            
            # 检查这个收藏夹是否有未处理的视频
            if has_unprocessed:
                all_folders_fully_processed = False
        
        # 更新全局FOLDER_STATS
        FOLDER_STATS = temp_folder_stats
        logger.info("FOLDER_STATS已更新")
        
        # 如果所有收藏夹都没有未处理的视频，自动切换到1小时监控频率
        if all_folders_fully_processed:
            monitor_interval = 3600  # 1小时
            logger.info("所有收藏夹都已处理完毕，已自动切换到1小时监控频率")
        else:
            # 如果有未处理的视频，切换到5分钟监控频率
            monitor_interval = 300  # 5分钟
            logger.info("有未处理的视频，已自动切换到5分钟监控频率")
        
        # 生成统计报告
        lines = ["收藏夹处理进度统计：\n"]
        
        total_pending = 0
        
        for fid, info in FOLDER_STATS.items():
            folder_title = info["title"]
            total_videos = len(info["bvids"])
            processed_videos = len(info["processed_bvids"])
            failed_videos = len(info["failed_bvids"])
            pending_videos = len(info["pending_bvids"])
            
            total_pending += pending_videos
            
            # 添加到报告中
            lines.append(f"收藏夹: {folder_title} (ID: {fid})")
            lines.append(f" - 总视频数: {total_videos}")
            lines.append(f" - 已处理: {processed_videos}")
            lines.append(f" - 失败: {failed_videos}")
            lines.append(f" - 待处理: {pending_videos}\n")
        
        # 添加总体统计
        lines.append(f"总计待处理视频: {total_pending}")
        if all_folders_fully_processed:
            lines.append("所有收藏夹都已处理完毕，已自动切换到1小时监控频率")
        
        # 自动启动监控处理
        if has_pending_videos:
            # 检查是否已经启动过监控
            if not monitor_started:
                monitor_started = True
                stop_monitor = False
                # 启动处理线程
                import threading
                def run_monitor():
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(monitor_favorites())
                t = threading.Thread(target=run_monitor, daemon=True)
                t.start()
                lines.append("已自动启动处理线程，开始处理待处理视频")
            else:
                lines.append("处理线程已在运行中，继续处理待处理视频")
        else:
            lines.append("没有待处理视频")
            
        status_text = "\n".join(lines)
        
        # 删除状态消息
        await status_msg.delete()
        
        # 使用分块发送功能处理可能较长的消息
        await send_long_text_in_chunks(client, message.chat.id, status_text)
            
    except Exception as e:
        logger.error(f"统计收藏夹处理进度失败: {e}")
        await safe_edit_text(status_msg, f"统计收藏夹处理进度失败: {e}")

async def is_video_already_processed(bvid: str, cid: str = None) -> bool:
    """检查视频是否已经处理过（已存在于数据库中且处理成功）"""
    try:
        query = supabase.table("bilibili").select("bvid,cid,is_failure,status").eq("bvid", bvid)
        
        if cid:
            query = query.eq("cid", cid)
            
        result = query.execute()
        
        # 如果有记录，检查是否有任何非失败的记录
        if result.data:
            for record in result.data:
                # 如果有任何一条记录处理成功，就认为已处理
                if not record.get("is_failure", False) and record.get("status") != "pending":
                    return True
            # 所有记录都是失败的，认为未处理
            return False
        return False
    except Exception as e:
        logger.error(f"查询数据库失败: {e}")
        # 查询失败时，假设未处理过，以便重新处理
        return False

async def save_to_database(
    bvid: str, 
    cid: str, 
    video_file_id: str = None, 
    file_file_id: str = None,
    video_caption: str = None,
    file_caption: str = None,
    is_in_fav: bool = False,
    folder_id: str = None,
    folder_name: str = None,
    is_failure: bool = False,
    up: str = "",  # 上传者用户名
    uid: str = ""  # 上传者UID
):
    """保存记录到Supabase数据库，如果记录已存在则更新"""
    # 如果提供了folder_id，自动设置is_in_fav为True
    if folder_id:
        is_in_fav = True
    
    # 构建标准格式URL
    video_url = f"https://www.bilibili.com/video/{bvid}"
    
    # 首先检查记录是否已存在
    try:
        query = supabase.table("bilibili").select("*").eq("bvid", bvid)
        
        if cid:
            query = query.eq("cid", cid)
            
        result = query.execute()
        
        # 如果记录已存在，更新它
        if result.data:
            logger.info(f"记录已存在于数据库中: {bvid}/{cid}，进行更新")
            
            # 设置状态
            status = "processed" if not is_failure else "failed"
            
            # 准备要更新的数据
            data = {
                "video_file_id": video_file_id,
                "file_file_id": file_file_id,
                "video_caption": video_caption,
                "file_caption": file_caption,
                "is_in_fav": is_in_fav,
                "folder_id": folder_id,
                "folder_name": folder_name,
                "is_failure": is_failure,
                "status": status
            }
            
            # 只更新提供了值的字段
            data = {k: v for k, v in data.items() if v is not None}
            
            # 如果提供了up和uid且当前记录中没有，则更新
            if up and not result.data[0].get("up"):
                data["up"] = up
            
            if uid and not result.data[0].get("uid"):
                data["uid"] = uid
                
            # 确保URL字段存在
            if not result.data[0].get("url"):
                data["url"] = video_url
            
            # 过滤非数据库字段
            data = filter_db_fields(data)
            
            # 更新记录
            supabase.table("bilibili").update(data).eq("bvid", bvid).eq("cid", cid).execute()
            logger.info(f"已更新记录: {bvid}/{cid}, 状态为: {status}")
            return result
    except Exception as e:
        logger.error(f"检查记录是否存在失败 {bvid}/{cid}: {e}")
        # 如果查询失败，继续尝试插入新记录
    
    # 准备要插入的数据
    data = {
        "bvid": bvid,
        "cid": cid,
        "video_file_id": video_file_id,
        "file_file_id": file_file_id,
        "video_caption": video_caption,
        "file_caption": file_caption,
        "is_in_fav": is_in_fav,
        "folder_id": folder_id,
        "folder_name": folder_name,
        "is_failure": is_failure,
        "up": up,
        "uid": uid,
        "url": video_url,  # 添加URL字段
        "status": "processed" if not is_failure else "failed"  # 设置状态
    }
    
    # 过滤非数据库字段
    data = filter_db_fields(data)
    
    # 添加重试机制
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # 插入新记录
            result = supabase.table("bilibili").insert(data).execute()
            logger.info(f"已保存新记录到数据库: {bvid}/{cid}, 状态为: {'processed' if not is_failure else 'failed'}")
            
            # 更新FOLDER_STATS
            if folder_id and folder_id in FOLDER_STATS:
                if is_failure:
                    FOLDER_STATS[folder_id]["failed_bvids"].add(bvid)
                else:
                    FOLDER_STATS[folder_id]["processed_bvids"].add(bvid)
                    # 如果之前在失败列表中，则移除
                    if bvid in FOLDER_STATS[folder_id]["failed_bvids"]:
                        FOLDER_STATS[folder_id]["failed_bvids"].remove(bvid)
            
            return result
        except Exception as e:
            retry_count += 1
            # 可能是主键冲突，尝试更新而不是插入
            if retry_count == 1:
                try:
                    logger.info(f"插入失败，尝试更新 {bvid}/{cid}")
                    result = supabase.table("bilibili").update(data).eq("bvid", bvid).eq("cid", cid).execute()
                    logger.info(f"已更新记录: {bvid}/{cid}")
                    return result
                except Exception as update_e:
                    logger.error(f"更新也失败 {bvid}/{cid}: {update_e}")
            
            logger.error(f"保存到数据库失败 (尝试 {retry_count}/{max_retries}) {bvid}/{cid}: {e}")
            if retry_count < max_retries:
                # 等待一段时间后重试
                await asyncio.sleep(1)
            else:
                logger.error(f"保存到数据库失败，已达到最大重试次数 {bvid}/{cid}: {e}")
                return None


@app.on_callback_query()
async def handle_callback_query(client: PyroClient, callback_query: CallbackQuery):
    """处理回调查询，包括清晰度选择按钮"""
    data = callback_query.data
    
    # 处理清晰度选择按钮
    if data.startswith("s:"):
        # 格式: s:bvid:cid:qn:codec
        parts = data.split(":")
        if len(parts) == 5:
            _, bvid, cid, qn, codec = parts
            
            # 通知用户正在处理
            await callback_query.answer("正在处理您的请求，请稍候...")
            
            # 发送状态消息
            status_msg = await safe_send_message(
                client, 
                callback_query.message.chat.id, 
                f"正在下载 BV{bvid} 的 {qn}({codec}) 清晰度版本..."
            )
            
            try:
                # 检查是否有缓存的流信息
                if (bvid, cid) in STREAMS_CACHE:
                    # 获取音频流和所选视频流
                    cache_data = STREAMS_CACHE[(bvid, cid)]
                    audio_stream = cache_data["audio_stream"]
                    
                    # 调试日志
                    logger.info(f"音频流结构: {audio_stream}")
                    
                    # 找到所选的视频流
                    selected_stream = None
                    for stream in cache_data["streams"]:
                        if str(stream["qn"]) == qn and stream["codec"] == codec:
                            selected_stream = stream
                            break
                    
                    # 调试日志
                    if selected_stream:
                        logger.info(f"选中的视频流结构: {selected_stream}")
                    
                    if selected_stream and audio_stream:
                        # 创建下载器
                        downloader = AsyncBiliDownloader()
                        
                        # 获取视频信息用于标题等
                        loop = asyncio.get_running_loop()
                        vi, _, parsed_data, is_deleted = await loop.run_in_executor(
                            thread_pool, sync_fetch_one_video_info_and_cid, bvid
                        )
                        
                        if not is_deleted and parsed_data:
                            # 构建标题和封面URL
                            cover_url = parsed_data["basic"]["coverUrl"]
                            caption = build_bilibili_caption(parsed_data)
                            source_url = f"https://www.bilibili.com/video/{bvid}"
                            
                            # 获取UP主信息
                            up_name = parsed_data["owner"]["username"] if parsed_data and "owner" in parsed_data else ""
                            
                            # 下载特定清晰度的视频
                            await safe_edit_text(status_msg, f"正在下载 {selected_stream['quality_name']}({codec}) 清晰度版本...")
                            
                            # 创建临时目录
                            temp_dir = Path("temp")
                            temp_dir.mkdir(exist_ok=True)
                            temp_video = temp_dir / f"{bvid}_{cid}_{qn}_{codec}_video.m4s"
                            temp_audio = temp_dir / f"{bvid}_{cid}_audio.m4s"
                            
                            # 使用新的命名格式
                            safe_up_name = re.sub(r'[\\/*?:"<>|]', "_", up_name) if up_name else ""
                            filename = f"bili_{bvid}-{cid}_{qn}_{codec}"
                            if safe_up_name:
                                filename += f"_{safe_up_name}"
                            filename += ".mp4"
                            
                            # 使用downloads目录而不是temp目录
                            download_dir = Path("downloads")
                            download_dir.mkdir(exist_ok=True)
                            output_file = download_dir / filename
                            
                            # 下载视频流
                            video_ok = await downloader.download_file(selected_stream["base_url"], str(temp_video))
                            
                            # 下载音频流
                            # 尝试不同可能的字段名
                            audio_url = audio_stream.get("baseUrl") or audio_stream.get("base_url") or audio_stream.get("url")
                            if audio_url:
                                audio_ok = await downloader.download_file(audio_url, str(temp_audio))
                            else:
                                logger.error(f"无法找到音频流URL字段: {audio_stream}")
                                audio_ok = False
                            
                            if video_ok and audio_ok:
                                # 合并视频和音频
                                await safe_edit_text(status_msg, "正在合并视频和音频...")
                                
                                # 使用FFmpeg合并
                                cmd = [
                                    "ffmpeg", "-y",
                                    "-i", str(temp_video),
                                    "-i", str(temp_audio),
                                    "-c:v", "copy",
                                    "-c:a", "copy",
                                    str(output_file)
                                ]
                                
                                try:
                                    process = await asyncio.create_subprocess_exec(
                                        *cmd,
                                        stdout=asyncio.subprocess.PIPE,
                                        stderr=asyncio.subprocess.PIPE
                                    )
                                    await process.communicate()
                                    
                                    if process.returncode == 0 and output_file.exists():
                                        # 发送视频
                                        await safe_edit_text(status_msg, f"正在发送 {selected_stream['quality_name']}({codec}) 清晰度版本...")
                                        
                                        # 发送到Telegram
                                        ok = await send_video_to_telegram(
                                            client,
                                            callback_query.message.chat.id,
                                            str(output_file),
                                            caption=f"{caption}\n\n[自选清晰度] {selected_stream['quality_name']}({codec})",
                                            source_url=source_url,
                                            cover_url=cover_url,
                                            bvid=bvid,
                                            cid=int(cid),
                                            show_quality_buttons=True
                                        )
                                        
                                        if ok:
                                            await safe_edit_text(status_msg, "发送完成")
                                        else:
                                            await safe_edit_text(status_msg, "发送失败")
                                    else:
                                        await safe_edit_text(status_msg, "合并视频失败")
                                except Exception as e:
                                    logger.error(f"合并视频失败: {e}")
                                    await safe_edit_text(status_msg, f"合并视频失败: {e}")
                            else:
                                await safe_edit_text(status_msg, "下载视频或音频失败")
                            
                            # 只清理临时文件，不删除生成的MP4
                            try:
                                temp_video.unlink(missing_ok=True)
                                temp_audio.unlink(missing_ok=True)
                            except:
                                pass
                            
                            logger.info(f"自选清晰度文件 {output_file} 已保留在本地")
                        else:
                            await safe_edit_text(status_msg, "获取视频信息失败或视频不可见")
                    else:
                        await safe_edit_text(status_msg, "未找到所选清晰度的视频流或音频流")
                else:
                    await safe_edit_text(status_msg, "未找到视频流信息，请重新获取视频")
                
                # 3秒后删除状态消息
                await asyncio.sleep(3)
                await status_msg.delete()
                
            except Exception as e:
                logger.error(f"处理清晰度选择失败: {e}")
                await safe_edit_text(status_msg, f"处理失败: {e}")
    
    # 处理其他类型的回调
    elif data.startswith("get_all_videos:"):
        uid = data.split(":")[1]
        await callback_query.answer(f"正在获取UID {uid} 的全部作品，请稍候...")
        
        # 发送状态消息
        status_msg = await safe_send_message(
            client, 
            callback_query.message.chat.id, 
            f"正在获取UID {uid} 的全部作品，请稍候..."
        )
        
        try:
            # 获取用户所有作品的BV号
            all_bvids = await fetch_all_bvids(uid)
            
            if all_bvids:
                await safe_edit_text(status_msg, f"找到 {len(all_bvids)} 个作品，正在准备下载...")
                
                # 创建一个任务队列
                processed_count = 0
                total_count = len(all_bvids)
                
                # 逐个处理BV号
                for bvid in all_bvids:
                    # 检查是否已经处理过
                    already_processed = await is_video_already_processed(bvid)
                    
                    if already_processed:
                        # 如果已处理过，获取数据库记录并重新发送
                        try:
                            result = supabase.table("bilibili").select("*").eq("bvid", bvid).execute()
                            if result.data:
                                records = result.data
                                success_count = sum(1 for r in records if not r.get("is_failure", False))
                                
                                if success_count > 0:
                                    # 重新发送成功的视频
                                    success_records = [r for r in records if not r.get("is_failure", False)]
                                    for record in success_records:
                                        # 使用file_id重新发送视频
                                        if record.get("video_file_id"):
                                            try:
                                                await safe_send_cached_media(
                                                    client,
                                                    callback_query.message.chat.id,
                                                    record.get("video_file_id"),
                                                    caption=record.get("video_caption") or "",
                                                    parse_mode=ParseMode.HTML
                                                )
                                            except Exception as e:
                                                logger.error(f"重新发送视频失败: {e}")
                        except Exception as e:
                            logger.error(f"查询数据库失败: {e}")
                    else:
                        # 如果未处理过，下载并发送
                        await safe_edit_text(status_msg, f"正在处理 {processed_count+1}/{total_count}: {bvid}")
                        await pipeline_single_bvid(client, callback_query.message.chat.id, bvid)
                    
                    processed_count += 1
                    # 每处理5个视频更新一次状态
                    if processed_count % 5 == 0:
                        await safe_edit_text(status_msg, f"已处理 {processed_count}/{total_count} 个视频")
                
                await safe_edit_text(status_msg, f"全部处理完成，共 {total_count} 个视频")
            else:
                await safe_edit_text(status_msg, f"未找到UID {uid} 的作品")
            
            # 5秒后删除状态消息
            await asyncio.sleep(5)
            await status_msg.delete()
            
        except Exception as e:
            logger.error(f"获取全部作品失败: {e}")
            await safe_edit_text(status_msg, f"获取全部作品失败: {e}")
    
    elif data.startswith("get_user_favorites:"):
        uid = data.split(":")[1]
        await callback_query.answer(f"正在获取UID {uid} 的收藏夹，请稍候...")
        
        # 发送状态消息
        status_msg = await safe_send_message(
            client, 
            callback_query.message.chat.id, 
            f"正在获取UID {uid} 的收藏夹，请稍候..."
        )
        
        try:
            # 获取用户收藏夹列表
            folders = await fetch_user_collect_folders(uid)
            
            if folders:
                await safe_edit_text(status_msg, f"找到 {len(folders)} 个收藏夹，请选择要下载的收藏夹")
                
                # 创建收藏夹选择按钮
                buttons = []
                for folder in folders:
                    folder_id = folder.get("id", "")
                    folder_name = folder.get("title", "未命名收藏夹")
                    folder_count = folder.get("media_count", 0)
                    
                    # 每行最多2个按钮
                    if len(buttons) == 0 or len(buttons[-1]) >= 2:
                        buttons.append([])
                    
                    # 添加按钮
                    buttons[-1].append(
                        InlineKeyboardButton(
                            f"{folder_name} ({folder_count})",
                            callback_data=f"folder:{folder_id}:{folder_name}"
                        )
                    )
                
                # 发送收藏夹选择消息
                await safe_edit_text(
                    status_msg,
                    f"UID {uid} 的收藏夹列表，请选择要下载的收藏夹：",
                    reply_markup=InlineKeyboardMarkup(buttons)
                )
            else:
                await safe_edit_text(status_msg, f"未找到UID {uid} 的收藏夹")
                # 5秒后删除状态消息
                await asyncio.sleep(5)
                await status_msg.delete()
                
        except Exception as e:
            logger.error(f"获取收藏夹失败: {e}")
            await safe_edit_text(status_msg, f"获取收藏夹失败: {e}")
            # 5秒后删除状态消息
            await asyncio.sleep(5)
            await status_msg.delete()
    
    # 处理收藏夹选择
    elif data.startswith("folder:"):
        parts = data.split(":")
        if len(parts) >= 3:
            folder_id = parts[1]
            folder_name = ":".join(parts[2:])  # 处理名称中可能包含冒号的情况
            
            await callback_query.answer(f"正在获取收藏夹 {folder_name} 中的视频，请稍候...")
            
            # 发送状态消息
            status_msg = await safe_send_message(
                client, 
                callback_query.message.chat.id, 
                f"正在获取收藏夹 {folder_name} 中的视频，请稍候..."
            )
            
            try:
                # 获取收藏夹中的所有BV号
                folder_bvids = await fetch_folder_bvids(folder_id)
                
                if folder_bvids:
                    await safe_edit_text(status_msg, f"找到 {len(folder_bvids)} 个视频，正在准备下载...")
                    
                    # 创建一个任务队列
                    processed_count = 0
                    total_count = len(folder_bvids)
                    
                    # 逐个处理BV号
                    for bvid in folder_bvids:
                        # 检查是否已经处理过
                        already_processed = await is_video_already_processed(bvid)
                        
                        if already_processed:
                            # 如果已处理过，记录统计信息但不重新处理
                            processed_count += 1
                            # 更新进度显示
                            if processed_count % 5 == 0:
                                await safe_edit_text(status_msg, f"已处理 {processed_count}/{total_count} 个视频（跳过已存在视频）")
                            continue
                        
                        # 处理视频
                        await safe_edit_text(status_msg, f"正在处理 {processed_count+1}/{total_count}: {bvid}")
                        await pipeline_single_bvid(
                            client, 
                            callback_query.message.chat.id, 
                            bvid,
                            folder_name=folder_name,
                            folder_id=folder_id
                        )
                        
                        processed_count += 1
                        # 每处理5个视频更新一次状态
                        if processed_count % 5 == 0:
                            await safe_edit_text(status_msg, f"已处理 {processed_count}/{total_count} 个视频")
                    
                    await safe_edit_text(status_msg, f"全部处理完成，共 {total_count} 个视频")
                else:
                    await safe_edit_text(status_msg, f"收藏夹 {folder_name} 中没有视频")
                
                # 5秒后删除状态消息
                await asyncio.sleep(5)
                await status_msg.delete()
                
            except Exception as e:
                logger.error(f"获取收藏夹视频失败: {e}")
                await safe_edit_text(status_msg, f"获取收藏夹视频失败: {e}")
                # 5秒后删除状态消息
                await asyncio.sleep(5)
                await status_msg.delete()
    
    else:
        await callback_query.answer("未知操作")

@app.on_message(filters.command(["test_folder"]))
async def test_folder_command(client: PyroClient, message: Message):
    """测试获取指定收藏夹的视频列表"""
    if message.chat.id != AUTHORIZED_CHAT_ID:
        return
    
    # 检查命令参数
    parts = message.text.split()
    if len(parts) < 2:
        await safe_send_message(client, message.chat.id, "请提供收藏夹ID，例如：/test_folder 12345678")
        return
    
    folder_id = parts[1]
    status_msg = await safe_send_message(client, message.chat.id, f"正在测试获取收藏夹 {folder_id} 的视频列表...")
    
    try:
        bvids = await fetch_folder_bvids(folder_id)
        if bvids:
            await safe_edit_text(status_msg, f"成功获取到 {len(bvids)} 个视频，前10个视频：\n" + "\n".join(bvids[:10]))
        else:
            await safe_edit_text(status_msg, f"收藏夹 {folder_id} 中没有视频或获取失败")
    except Exception as e:
        await safe_edit_text(status_msg, f"测试失败: {e}")

@app.on_message(filters.command(["test_user"]))
async def test_user_command(client: PyroClient, message: Message):
    """测试获取指定用户的收藏夹列表"""
    if message.chat.id != AUTHORIZED_CHAT_ID:
        return
    
    # 检查命令参数
    parts = message.text.split()
    if len(parts) < 2:
        await safe_send_message(client, message.chat.id, "请提供用户ID，例如：/test_user 12954775")
        return
    
    uid = parts[1]
    status_msg = await safe_send_message(client, message.chat.id, f"正在测试获取用户 {uid} 的收藏夹列表...")
    
    try:
        folders = await fetch_user_collect_folders(uid)
        if folders:
            folder_lines = [f"成功获取到 {len(folders)} 个收藏夹："]
            for folder in folders:
                folder_lines.append(f"- {folder.get('title', '无标题')} (ID: {folder.get('id', 'unknown')})")
            await safe_edit_text(status_msg, "\n".join(folder_lines))
        else:
            await safe_edit_text(status_msg, f"用户 {uid} 没有收藏夹或获取失败")
    except Exception as e:
        await safe_edit_text(status_msg, f"测试失败: {e}")

@app.on_message(filters.command(["set_user"]))
async def set_user_command(client: PyroClient, message: Message):
    """设置要监控的用户ID"""
    if message.chat.id != AUTHORIZED_CHAT_ID:
        return
    
    # 检查命令参数
    parts = message.text.split()
    if len(parts) < 2:
        await safe_send_message(client, message.chat.id, "请提供用户ID，例如：/set_user 12954775")
        return
    
    uid = parts[1]
    
    # 修改全局变量
    global target_uid
    old_uid = target_uid
    target_uid = uid
    
    await safe_send_message(client, message.chat.id, f"已将监控用户从 {old_uid} 更改为 {target_uid}")
    
    # 清空当前的收藏夹统计
    global FOLDER_STATS
    FOLDER_STATS.clear()
    
    await safe_send_message(client, message.chat.id, "已清空收藏夹统计，将在下一轮监控中获取新用户的收藏夹")

async def send_long_text_in_chunks(client: PyroClient, chat_id: int, text: str):
    """
    如果 text 超过 Telegram 限制(此处设 4000 保险) 则自动分割多段发送
    """
    MAX_MSG_LEN = 4000
    idx = 0
    while idx<len(text):
        chunk = text[idx: idx+MAX_MSG_LEN]
        await safe_send_message(client, chat_id, chunk)
        idx+=MAX_MSG_LEN

@app.on_message(filters.command(["switch"]))
async def switch_command(client: PyroClient, message: Message):
    if message.chat.id != AUTHORIZED_CHAT_ID:
        return
    global monitor_interval
    
    # 获取当前间隔文本描述
    current_interval_text = "1小时" if monitor_interval == 3600 else "5分钟" if monitor_interval == 300 else f"{monitor_interval}秒"
    
    # 检查是否提供了参数
    parts = message.text.split()
    if len(parts) > 1:
        # 用户提供了参数
        arg = parts[1].lower()
        if arg == "hour" or arg == "1h" or arg == "slow":
            monitor_interval = 3600  # 1小时
            await safe_send_message(client, message.chat.id, f"监控间隔已切换为1小时 (之前: {current_interval_text})")
        elif arg == "minute" or arg == "5m" or arg == "fast":
            monitor_interval = 300  # 5分钟
            await safe_send_message(client, message.chat.id, f"监控间隔已切换为5分钟 (之前: {current_interval_text})")
        elif arg == "debug" or arg == "5s" or arg == "test":
            monitor_interval = 5  # 5秒（调试用）
            await safe_send_message(client, message.chat.id, f"监控间隔已切换为5秒 (之前: {current_interval_text})")
        else:
            try:
                seconds = int(arg)
                monitor_interval = seconds
                await safe_send_message(client, message.chat.id, f"监控间隔已设置为{seconds}秒 (之前: {current_interval_text})")
            except:
                await safe_send_message(client, message.chat.id, f"参数无法识别，当前间隔: {current_interval_text}，可用参数: hour/minute/debug 或直接输入秒数")
    else:
        # 简单切换模式
        if monitor_interval == 3600:  # 当前是1小时
            monitor_interval = 300  # 切换到5分钟
            await safe_send_message(client, message.chat.id, f"监控间隔已从1小时切换为5分钟")
        elif monitor_interval == 300:  # 当前是5分钟
            monitor_interval = 3600  # 切换到1小时
            await safe_send_message(client, message.chat.id, f"监控间隔已从5分钟切换为1小时")
        else:  # 其他情况
            await safe_send_message(client, message.chat.id, f"当前监控间隔为{monitor_interval}秒，请使用参数指定要切换的间隔: /switch hour 或 /switch minute 或 /switch 5")

class B23Expander:
    """处理 b23.tv 短链接的类"""
    def __init__(self):
        self.patterns = {
            'random': r'b23\.tv/([0-9A-Za-z]{7})',
            'av': r'b23\.tv/av(\d+)',
            'bv': r'b23\.tv/BV([0-9A-Za-z]+)'
        }
        # BV ID pattern
        self.bv_pattern = r'BV([0-9A-Za-z]+)'
        
    def extract_from_share_text(self, text):
        pat = r'【.*?】\s*(https?://[^\s]+)'
        m = re.search(pat, text)
        if m: return m.group(1)
        return text
        
    def _get_redirect(self, short_url):
        if not short_url.startswith(('http://','https://')):
            short_url="https://"+short_url
        try:
            # Add proper user agent to mimic a browser
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Referer": "https://www.bilibili.com"
            }
            resp=requests.head(short_url, allow_redirects=True, timeout=10, headers=headers)
            resp.raise_for_status()
            return resp.url
        except Exception as e:
            logger.warning(f"无法通过重定向获取完整链接: {e}")
            # If redirect fails, try to extract BV ID directly
            bv_match = re.search(self.bv_pattern, short_url)
            if bv_match:
                bvid = "BV" + bv_match.group(1)
                return f"https://www.bilibili.com/video/{bvid}"
            raise
            
    def expand_url(self, text):
        url = self.extract_from_share_text(text).strip()
        
        # First check if the URL already contains a BV ID
        bv_match = re.search(self.bv_pattern, url)
        if bv_match:
            bvid = "BV" + bv_match.group(1)
            return f"https://www.bilibili.com/video/{bvid}"
            
        # Otherwise try to expand b23.tv links
        pu=urlparse(url)
        if not (pu.netloc=="b23.tv" or url.startswith("b23.tv")):
            return url
        for _, pat in self.patterns.items():
            if re.search(pat, url):
                return self._get_redirect(url)
        raise ValueError("无法识别的 b23.tv 链接")

@app.on_message(filters.text & ~filters.command(["start","help","id","check","switch"]))
async def handle_text(client: PyroClient, message: Message):
    if message.chat.id != AUTHORIZED_CHAT_ID:
        return
    text_in=message.text.strip()
    if not text_in:
        await message.delete()
        return

    # 展开b23.tv
    if "b23.tv" in text_in or "【" in text_in:
        try:
            e=B23Expander()
            text_in=e.expand_url(text_in)
        except Exception as e_:
            logger.info(f"展开b23链接失败: {e_}")

    # 判断是否是B站链接
    if "bilibili.com/video/" in text_in or re.search(BV_PATTERN, text_in):
        bvid = re.search(BV_PATTERN, text_in).group(1)
        
        # 检查是否已经处理过
        already_processed = await is_video_already_processed(bvid)
        if already_processed:
            info_msg = await safe_send_message(client, message.chat.id, f"BV({bvid})已经处理过，正在从数据库获取...")
            
            # 查询数据库获取详细信息
            try:
                result = supabase.table("bilibili").select("*").eq("bvid", bvid).execute()
                if result.data:
                    records = result.data
                    success_count = sum(1 for r in records if not r.get("is_failure", False))
                    failure_count = sum(1 for r in records if r.get("is_failure", False))
                    
                    if success_count > 0:
                        await safe_edit_text(info_msg, f"BV({bvid})已经处理过，共{len(records)}个分P，成功{success_count}个，失败{failure_count}个。正在重新发送...")
                        
                        # 重新发送成功的视频
                        success_records = [r for r in records if not r.get("is_failure", False)]
                        for record in success_records:
                            # 使用file_id重新发送视频和文件
                            if record.get("video_file_id"):
                                try:
                                    await safe_send_cached_media(
                                        client,
                                        message.chat.id,
                                        record.get("video_file_id"),
                                        caption=record.get("video_caption") or "",
                                        parse_mode=ParseMode.HTML
                                    )
                                    logger.info(f"使用缓存重新发送视频: {bvid}/{record.get('cid')}")
                                except Exception as e:
                                    logger.error(f"重新发送视频失败: {e}")
                            
                            # 如果有文件ID，也重新发送文件
                            if record.get("file_file_id"):
                                try:
                                    await safe_send_cached_media(
                                        client,
                                        message.chat.id,
                                        record.get("file_file_id"),
                                        caption=record.get("file_caption") or "",
                                        parse_mode=ParseMode.HTML
                                    )
                                    logger.info(f"使用缓存重新发送文件: {bvid}/{record.get('cid')}")
                                except Exception as e:
                                    logger.error(f"重新发送文件失败: {e}")
                        
                        await safe_edit_text(info_msg, "重新发送完成")
                    else:
                        await safe_edit_text(info_msg, f"BV({bvid})之前处理失败，正在重新尝试...")
                        # 如果全部失败，则重新处理
                        suc = await pipeline_single_bvid(client, message.chat.id, bvid)
                        if suc:
                            await safe_edit_text(info_msg, "重新处理完成")
                        else:
                            await safe_edit_text(info_msg, "重新处理失败或稿件不可见")
            except Exception as e:
                logger.error(f"查询数据库失败: {e}")
                await safe_edit_text(info_msg, f"查询数据库失败，将重新处理BV({bvid})...")
                # 如果查询失败，则重新处理
                suc = await pipeline_single_bvid(client, message.chat.id, bvid)
                if suc:
                    await safe_edit_text(info_msg, "处理完成")
                else:
                    await safe_edit_text(info_msg, "处理失败或稿件不可见")
        else:
            # 未处理过，开始处理
            info_msg = await safe_send_message(client, message.chat.id, f"开始处理BV({bvid})，请稍候...")
            suc = await pipeline_single_bvid(client, message.chat.id, bvid)
            if suc:
                await safe_edit_text(info_msg, "处理完成")
            else:
                await safe_edit_text(info_msg, "处理失败或稿件不可见")
        
        await asyncio.sleep(3)
        await info_msg.delete()
        await message.delete()

# 添加数据库字段
@app.on_message(filters.command(["add_lock_field"]))
async def add_lock_field_command(client: PyroClient, message: Message):
    """仅用于给数据库添加processing_lock字段，只需运行一次"""
    if message.chat.id != AUTHORIZED_CHAT_ID:
        return
    
    try:
        # 查询所有记录
        result = supabase.table("bilibili").select("bvid,cid").execute()
        total = len(result.data)
        
        if total > 0:
            status_msg = await safe_send_message(client, message.chat.id, f"正在为 {total} 条记录添加processing_lock字段...")
            
            # 逐条更新
            updated = 0
            for record in result.data:
                bvid = record.get("bvid")
                cid = record.get("cid")
                if bvid and cid:
                    try:
                        # 添加处理锁字段
                        supabase.table("bilibili").update({
                            "processing_lock": False
                        }).eq("bvid", bvid).eq("cid", cid).execute()
                        updated += 1
                        
                        if updated % 100 == 0 or updated == total:
                            await safe_edit_text(status_msg, f"已更新 {updated}/{total} 条记录...")
                    except Exception as e:
                        logger.error(f"更新记录失败 {bvid}/{cid}: {e}")
            
            await safe_edit_text(status_msg, f"处理完成，共更新 {updated}/{total} 条记录")
        else:
            await safe_send_message(client, message.chat.id, "数据库中没有记录")
    except Exception as e:
        await safe_send_message(client, message.chat.id, f"操作失败: {e}")

@app.on_message(filters.command(["add_missed_fields"]))
async def add_missed_fields_command(client: PyroClient, message: Message):
    """检查数据库记录中是否有缺失的必要字段并添加"""
    if message.chat.id != AUTHORIZED_CHAT_ID:
        return
    
    try:
        # 查询所有记录
        result = supabase.table("bilibili").select("bvid,cid,url").execute()
        total = len(result.data)
        
        if total > 0:
            status_msg = await safe_send_message(client, message.chat.id, f"正在检查 {total} 条记录的必要字段...")
            
            # 检查并更新缺失的 URL 字段
            missing_url = 0
            
            for record in result.data:
                bvid = record.get("bvid")
                cid = record.get("cid")
                url = record.get("url")
                
                if bvid and cid and not url:
                    # 添加 URL 字段
                    video_url = f"https://www.bilibili.com/video/{bvid}"
                    update_data = filter_db_fields({
                        "url": video_url
                    })
                    
                    try:
                        supabase.table("bilibili").update(update_data).eq("bvid", bvid).eq("cid", cid).execute()
                        missing_url += 1
                        
                        if missing_url % 50 == 0 or missing_url == total:
                            await safe_edit_text(status_msg, f"已为 {missing_url} 条记录添加缺失的URL字段...")
                    except Exception as e:
                        logger.error(f"更新记录字段失败 {bvid}/{cid}: {e}")
            
            # 更新缺失的状态字段
            missing_status = 0
            result = supabase.table("bilibili").select("bvid,cid,status,is_failure").execute()
            
            for record in result.data:
                bvid = record.get("bvid")
                cid = record.get("cid")
                status = record.get("status")
                is_failure = record.get("is_failure", False)
                
                if bvid and cid and not status:
                    # 根据 is_failure 添加状态字段
                    update_data = filter_db_fields({
                        "status": "failed" if is_failure else "processed"
                    })
                    
                    try:
                        supabase.table("bilibili").update(update_data).eq("bvid", bvid).eq("cid", cid).execute()
                        missing_status += 1
                    except Exception as e:
                        logger.error(f"更新记录状态字段失败 {bvid}/{cid}: {e}")
            
            # 最终报告
            report = [
                f"字段补全完成:",
                f"总记录数: {total}",
                f"添加URL字段: {missing_url} 条",
                f"添加状态字段: {missing_status} 条"
            ]
            
            await safe_edit_text(status_msg, "\n".join(report))
        else:
            await safe_send_message(client, message.chat.id, "数据库中没有记录")
    except Exception as e:
        await safe_send_message(client, message.chat.id, f"操作失败: {e}")

# 添加清理锁定命令
@app.on_message(filters.command(["clear_locks"]))
async def clear_locks_command(client: PyroClient, message: Message):
    """清理所有中断的处理状态（处理被中断的任务）"""
    if message.chat.id != AUTHORIZED_CHAT_ID:
        return
    
    try:
        # 数据库中没有 processing_started_at 和 processing_completed_at 字段
        # 将所有状态不为 processed 和 failed 的记录重置为 pending
        status_msg = await safe_send_message(client, message.chat.id, "正在查找中断的处理任务...")
        
        # 查询所有未完成的处理记录
        result = supabase.table("bilibili").select("bvid,cid").not_.eq("status", "processed").not_.eq("status", "failed").execute()
        
        count = len(result.data) if result.data else 0
        
        if count > 0:
            await safe_edit_text(status_msg, f"找到 {count} 个中断或未完成的处理任务，正在重置为待处理状态...")
            
            # 将所有记录重置为pending状态
            update_data = filter_db_fields({
                "status": "pending"
            })
            
            try:
                supabase.table("bilibili").update(update_data).not_.eq("status", "processed").not_.eq("status", "failed").execute()
                await safe_edit_text(status_msg, f"已将 {count} 个中断或未完成的处理任务重置为待处理状态")
            except Exception as e:
                logger.error(f"重置未完成任务状态失败: {e}")
                await safe_edit_text(status_msg, f"重置未完成任务状态失败: {e}")
        else:
            await safe_edit_text(status_msg, "没有找到中断或未完成的处理任务")
            
    except Exception as e:
        await safe_send_message(client, message.chat.id, f"清理处理状态失败: {e}")

# 添加手动设置所有pending状态命令
@app.on_message(filters.command(["reset_pending"]))
async def reset_pending_command(client: PyroClient, message: Message):
    """将指定条件的视频状态重置为pending"""
    if message.chat.id != AUTHORIZED_CHAT_ID:
        return
    
    try:
        # 检查参数
        parts = message.text.split()
        if len(parts) < 2:
            await safe_send_message(client, message.chat.id, "请指定类型: all/failed/processing")
            return
            
        action_type = parts[1].lower()
        
        if action_type == "all":
            # 重置所有记录为pending
            update_data = filter_db_fields({
                "status": "pending"
            })
            
            result = supabase.table("bilibili").update(update_data).execute()
            
            await safe_send_message(client, message.chat.id, "已将所有记录重置为pending状态")
            
        elif action_type == "failed":
            # 重置所有失败记录为pending
            update_data = filter_db_fields({
                "status": "pending",
                "is_failure": False
            })
            
            result = supabase.table("bilibili").update(update_data).eq("is_failure", True).execute()
            
            await safe_send_message(client, message.chat.id, "已将所有失败记录重置为pending状态")
            
        elif action_type == "processing":
            # 重置所有处理中的记录为pending
            # 注意：processing_started_at 和 processing_completed_at 字段在当前表结构中不存在
            # 所以这里只能重置status字段
            update_data = filter_db_fields({
                "status": "pending"
            })
            
            # 由于不再记录 processing_started_at，简单地重置所有非 processed 和 failed 状态的记录
            result = supabase.table("bilibili").update(update_data).not_.eq("status", "processed").not_.eq("status", "failed").execute()
            
            await safe_send_message(client, message.chat.id, "已将所有处理中记录重置为pending状态")
            
        else:
            await safe_send_message(client, message.chat.id, "未知操作类型，请使用: all/failed/processing")
            
    except Exception as e:
        await safe_send_message(client, message.chat.id, f"操作失败: {e}")

@app.on_message(filters.command(["db_stats"]))
async def db_stats_command(client: PyroClient, message: Message):
    """统计数据库中各种状态的视频数量"""
    if message.chat.id != AUTHORIZED_CHAT_ID:
        return
    
    try:
        status_msg = await safe_send_message(client, message.chat.id, "正在查询数据库状态...")
        
        # 查询总记录数
        total_result = supabase.table("bilibili").select("bvid,cid").execute()
        total_count = len(total_result.data) if total_result.data else 0
        
        # 查询pending状态数量
        pending_result = supabase.table("bilibili").select("bvid,cid").eq("status", "pending").execute()
        pending_count = len(pending_result.data) if pending_result.data else 0
        
        # 查询processed状态数量
        processed_result = supabase.table("bilibili").select("bvid,cid").eq("status", "processed").execute()
        processed_count = len(processed_result.data) if processed_result.data else 0
        
        # 查询failed状态数量
        failed_result = supabase.table("bilibili").select("bvid,cid").eq("is_failure", True).execute()
        failed_count = len(failed_result.data) if failed_result.data else 0
        
        # 检查缺少URL字段的记录数量
        missing_url_result = supabase.table("bilibili").select("bvid,cid").is_("url", "null").execute()
        missing_url_count = len(missing_url_result.data) if missing_url_result.data else 0
        
        # 检查缺少状态字段的记录数量
        missing_status_result = supabase.table("bilibili").select("bvid,cid").is_("status", "null").execute()
        missing_status_count = len(missing_status_result.data) if missing_status_result.data else 0
        
        # 统计不同收藏夹的记录数量
        folder_stats = {}
        folder_result = supabase.table("bilibili").select("folder_id,folder_name").not_.is_("folder_id", "null").execute()
        if folder_result.data:
            for record in folder_result.data:
                folder_id = record.get("folder_id")
                folder_name = record.get("folder_name", "未命名收藏夹")
                
                if folder_id:
                    if folder_id not in folder_stats:
                        folder_stats[folder_id] = {
                            "name": folder_name,
                            "count": 1
                        }
                    else:
                        folder_stats[folder_id]["count"] += 1
        
        # 生成统计报告
        stats = [
            "数据库统计：",
            f"总记录数: {total_count}",
            f"待处理 (pending): {pending_count}",
            f"已处理 (processed): {processed_count}",
            f"失败 (failed): {failed_count}",
            f"缺少URL字段: {missing_url_count}",
            f"缺少状态字段: {missing_status_count}"
        ]
        
        # 添加收藏夹统计
        if folder_stats:
            stats.append("\n收藏夹统计:")
            for folder_id, info in sorted(folder_stats.items(), key=lambda x: x[1]["count"], reverse=True):
                stats.append(f"- {info['name']} (ID: {folder_id}): {info['count']} 个视频")
        
        await safe_edit_text(status_msg, "\n".join(stats))
        
    except Exception as e:
        await safe_send_message(client, message.chat.id, f"查询失败: {e}")

@app.on_message(filters.command(["check_folders"]))
async def check_folders_command(client: PyroClient, message: Message):
    """手动检查收藏夹变更并更新数据库"""
    if message.chat.id != AUTHORIZED_CHAT_ID:
        return
    
    status_msg = await safe_send_message(client, message.chat.id, "正在检查收藏夹变更...")
    
    try:
        # 获取数据库中的所有收藏夹信息
        db_folders_result = supabase.table("bilibili").select("folder_id, folder_name").not_.is_("folder_id", "null").execute()
        db_folders = {}
        if db_folders_result.data:
            # 按folder_id分组记录
            for record in db_folders_result.data:
                folder_id = record.get("folder_id")
                folder_name = record.get("folder_name")
                if folder_id and folder_id not in db_folders:
                    db_folders[folder_id] = folder_name
        
        # 获取当前实际的收藏夹列表
        current_folders = await fetch_user_collect_folders(target_uid)
        if not current_folders:
            await safe_edit_text(status_msg, f"未能获取到用户 {target_uid} 的收藏夹列表")
            return
            
        current_folder_map = {str(folder.get("id")): folder.get("title", "未命名收藏夹") for folder in current_folders}
        
        # 比较变化并生成报告
        name_changes = []
        new_folders = []
        missing_folders = []
        
        # 检查名称变更和新增收藏夹
        for folder_id, folder_name in current_folder_map.items():
            if folder_id in db_folders:
                if db_folders[folder_id] != folder_name:
                    name_changes.append({
                        "id": folder_id,
                        "old_name": db_folders[folder_id],
                        "new_name": folder_name
                    })
            else:
                new_folders.append({
                    "id": folder_id,
                    "name": folder_name
                })
        
        # 检查已不存在的收藏夹
        for folder_id, folder_name in db_folders.items():
            if folder_id not in current_folder_map:
                missing_folders.append({
                    "id": folder_id,
                    "name": folder_name
                })
        
        # 更新数据库中的收藏夹名称
        for change in name_changes:
            folder_id = change["id"]
            new_name = change["new_name"]
            try:
                supabase.table("bilibili").update({
                    "folder_name": new_name,
                    "updated_at": datetime.now().isoformat()
                }).eq("folder_id", folder_id).execute()
                logger.info(f"已更新数据库中收藏夹 {folder_id} 的名称为 '{new_name}'")
            except Exception as e:
                logger.error(f"更新收藏夹名称失败 {folder_id}: {e}")
        
        # 生成报告
        report_lines = ["收藏夹检查结果:"]
        
        if name_changes:
            report_lines.append("\n名称变更的收藏夹:")
            for change in name_changes:
                report_lines.append(f"- ID: {change['id']}")
                report_lines.append(f"  旧名称: {change['old_name']}")
                report_lines.append(f"  新名称: {change['new_name']}")
        
        if new_folders:
            report_lines.append("\n新增的收藏夹:")
            for folder in new_folders:
                report_lines.append(f"- {folder['name']} (ID: {folder['id']})")
        
        if missing_folders:
            report_lines.append("\n已删除的收藏夹:")
            for folder in missing_folders:
                report_lines.append(f"- {folder['name']} (ID: {folder['id']})")
        
        if not (name_changes or new_folders or missing_folders):
            report_lines.append("\n未检测到收藏夹变更")
            
        # 添加总结统计
        total_current = len(current_folder_map)
        total_db = len(db_folders)
        
        report_lines.append(f"\n总计:")
        report_lines.append(f"- 当前收藏夹数量: {total_current}")
        report_lines.append(f"- 数据库记录收藏夹数量: {total_db}")
        report_lines.append(f"- 名称变更数量: {len(name_changes)}")
        report_lines.append(f"- 新增收藏夹数量: {len(new_folders)}")
        report_lines.append(f"- 已删除收藏夹数量: {len(missing_folders)}")
        
        # 发送报告
        await safe_edit_text(status_msg, "\n".join(report_lines))
        
    except Exception as e:
        logger.error(f"检查收藏夹变更失败: {e}")
        await safe_edit_text(status_msg, f"检查收藏夹变更失败: {e}")

def main():
    print("Starting BiliBot with custom requirements...")
    import atexit
    atexit.register(clean_session)

    # 检查必要的环境变量是否已设置
    required_env_vars = [
        'BILI_TELEGRAM_API_ID', 
        'BILI_TELEGRAM_API_HASH', 
        'BILI_TELEGRAM_BOT_TOKEN',
        'BILI_SUPABASE_URL',
        'BILI_SUPABASE_KEY'
    ]
    
    missing_vars = [var for var in required_env_vars if not os.environ.get(var)]
    if missing_vars:
        print(f"错误: 缺少以下环境变量: {', '.join(missing_vars)}")
        print("请确保已设置所有必要的环境变量。")
        return
    
    # 启动自动检测线程
    import threading
    def auto_start_monitor():
        # 等待一段时间让bot先完成初始化
        time.sleep(5) 
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # 先检查数据库中是否有待处理的视频
        try:
            result = loop.run_until_complete(check_pending_videos())
            has_pending = result > 0
            
            if has_pending:
                print(f"系统启动时发现有 {result} 个待处理的视频，自动启动监控...")
                
                # 启动监控，注意不要重复启动
                global monitor_started, stop_monitor
                if not monitor_started:
                    monitor_started = True
                    stop_monitor = False
                    
                    # 启动独立的监控线程
                    def run_monitor():
                        monitor_loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(monitor_loop)
                        monitor_loop.run_until_complete(monitor_favorites())
                    
                    t = threading.Thread(target=run_monitor, daemon=True)
                    t.start()
                    print("系统启动: 已启动监控线程")
                
            else:
                # 即使没有待处理视频，也需要检查一次收藏夹是否有变化
                print("系统启动时没有发现待处理视频，准备检查收藏夹变化...")
                loop.run_until_complete(check_folders())
        except Exception as e:
            print(f"自动检测失败: {e}")
        finally:
            loop.close()
    
    # 启动自动检测线程
    auto_detect_thread = threading.Thread(target=auto_start_monitor, daemon=True)
    auto_detect_thread.start()
    
    app.run()

# 用于自动检测的函数
async def check_pending_videos():
    """检查数据库中是否有待处理的视频，返回待处理视频数量"""
    try:
        result = supabase.table("bilibili").select("bvid,cid").eq("status", "pending").execute()
        count = len(result.data) if result.data else 0
        
        logger.info(f"自动检测: 发现 {count} 个待处理视频")
        return count
    except Exception as e:
        logger.error(f"检查待处理视频失败: {e}")
        return 0

async def check_folders():
    """检查收藏夹是否有新增视频"""
    global FOLDER_STATS, monitor_interval, monitor_started, stop_monitor
    
    try:
        logger.info("自动检测: 开始获取收藏夹数据并检查变化")
        # 获取收藏夹列表
        folders = await fetch_user_collect_folders(target_uid)
        if not folders:
            logger.warning("自动检测: 未获取到收藏夹列表，请检查接口是否正常")
            return
        
        logger.info(f"自动检测: 成功获取 {len(folders)} 个收藏夹")
        
        # 创建临时存储更新的数据
        temp_folder_stats = {}
        has_new_videos = False
        
        # 处理每个收藏夹
        for folder in folders:
            fid = str(folder.get("id"))
            folder_title = folder.get("title", "无标题")
            if not fid:
                continue
            
            logger.info(f"自动检测: 正在获取收藏夹 {folder_title} (ID: {fid}) 的第一页视频...")
            
            # 只检查第一页视频
            base_url = "http://localhost:8080/api/bilibili/web/fetch_user_collection_videos"
            try:
                url = f"{base_url}?folder_id={fid}&pn=1"
                j = await asyncio.get_event_loop().run_in_executor(
                    thread_pool, 
                    lambda: requests.get(url, timeout=10).json()
                )
                
                # 从返回的 JSON 中取出数据节点
                dd = j.get("data", {}).get("data", {})
                medias = dd.get("medias", [])
                
                logger.info(f"自动检测: 收藏夹 {folder_title} 第一页有 {len(medias)} 个视频")
                
                # 收集所有BV号
                folder_bvids = []
                for media in medias:
                    if "bvid" in media:
                        folder_bvids.append(media["bvid"])
                
                # 更新统计信息
                temp_folder_stats[fid] = {
                    "title": folder_title,
                    "bvids": set(folder_bvids),
                    "processed_bvids": set(),
                    "failed_bvids": set(),
                    "pending_bvids": set()
                }
                
                # 检查是否有新视频需要处理
                for bvid in folder_bvids:
                    # 检查数据库中是否已有记录
                    result = supabase.table("bilibili").select("bvid").eq("bvid", bvid).execute()
                    if not result.data:
                        logger.info(f"自动检测: 发现新视频: {bvid}，准备添加到数据库")
                        has_new_videos = True
                        
                        # 获取视频的分P信息
                        cids = await get_cids_for_bvid(bvid)
                        
                        if not cids:
                            logger.warning(f"自动检测: 无法获取视频 {bvid} 的分P信息，跳过")
                            continue
                        
                        # 添加每个分P到数据库
                        for cid in cids:
                            video_url = f"https://www.bilibili.com/video/{bvid}"
                            data = filter_db_fields({
                                "bvid": bvid,
                                "cid": cid,
                                "is_in_fav": True,
                                "folder_id": fid,
                                "folder_name": folder_title,
                                "is_failure": False,
                                "status": "pending",
                                "up": "",  # 处理时会更新
                                "uid": "",  # 处理时会更新
                                "url": video_url,
                                "created_at": datetime.now().isoformat()
                            })
                            
                            try:
                                supabase.table("bilibili").insert(data).execute()
                                logger.info(f"自动检测: 已将视频 {bvid}/{cid} 标记为待处理")
                                
                                # 更新statistic
                                temp_folder_stats[fid]["pending_bvids"].add(bvid)
                            except Exception as e:
                                # 可能是因为记录已存在
                                logger.error(f"自动检测: 添加视频 {bvid}/{cid} 到数据库失败: {e}")
                
            except Exception as e:
                logger.error(f"自动检测: 获取收藏夹 {folder_title} 内容失败: {e}")
        
        # 更新全局FOLDER_STATS
        FOLDER_STATS = temp_folder_stats
        logger.info("自动检测: FOLDER_STATS已更新")
        
        # 如果发现新视频，自动启动监控
        if has_new_videos:
            logger.info("自动检测: 发现新视频，正在启动监控线程...")
            # 启动监控，注意不要重复启动
            if not monitor_started:
                monitor_started = True
                stop_monitor = False
                
                # 启动监控线程
                def run_monitor():
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(monitor_favorites())
                
                t = threading.Thread(target=run_monitor, daemon=True)
                t.start()
                logger.info("自动检测: 已启动监控线程")
            else:
                logger.info("自动检测: 监控线程已在运行中")
        else:
            logger.info("自动检测: 未发现需要处理的新视频")
            
    except Exception as e:
        logger.error(f"自动检测: 检测收藏夹失败: {e}")

if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s %(levelname)s: %(message)s"
    )
    main()