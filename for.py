#!/usr/bin/env python3
# for.py

import asyncio
import json
import logging
import os
import re
import shutil
import subprocess
import time
import uuid
import zipfile
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union, Any

import httpx
from pyrogram import Client
from pyrogram.types import InputMediaPhoto, InputMediaVideo, InputMediaDocument, InputMediaAudio
from pyrogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from pyrogram.enums import ParseMode
from pyrogram.errors import FloodWait
from supabase import create_client, Client as SupabaseClient
import io
from dotenv import load_dotenv

# .env 文件路径(按需调整)
ENV_PATH = '.env'

# =============== 日志配置 ===============
logger = logging.getLogger("douyin-fullscan-for")
logger.setLevel(logging.INFO)

# 控制台日志
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

# 文件日志
file_handler = logging.FileHandler('douyin-fullscan-for.log')
file_handler.setLevel(logging.INFO)
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)

logger.info(f"正在从 {ENV_PATH} 加载环境变量...")
dotenv_loaded = load_dotenv(dotenv_path=ENV_PATH)
if not dotenv_loaded:
    logger.error(f"无法加载 .env 文件: {ENV_PATH}")

# 必需的环境变量
required_env_vars = [
    'API_ID',
    'API_HASH',
    'SUPABASE_URL',
    'SUPABASE_KEY',
    'BOT_TOKEN_SECONDARY'
]

config = {}
missing_vars = []
for var in required_env_vars:
    value = os.getenv(var)
    if value:
        config[var] = value
        logger.info(f"成功加载环境变量: {var}")
    else:
        missing_vars.append(var)
        logger.error(f"必需的环境变量缺失: {var}")

if missing_vars:
    raise ValueError(f"启动失败，缺少以下环境变量: {', '.join(missing_vars)}")

# 读取并赋值
API_ID = config['API_ID']
API_HASH = config['API_HASH']
BOT_TOKEN = config['BOT_TOKEN_SECONDARY']  # 注意，这里只使用 BOT_TOKEN_SECONDARY
SUPABASE_URL = config['SUPABASE_URL']
SUPABASE_KEY = config['SUPABASE_KEY']

# 可选配置
API_BASE_URL = os.getenv('API_BASE_URL', "http://localhost:8080")
DELETE_FILES = os.getenv('DELETE_FILES', 'True').lower() == 'true'
SCAN_INTERVAL = int(os.getenv('SCAN_INTERVAL', '0'))
PROCESS_DELAY = int(os.getenv('PROCESS_DELAY', '0'))
VIDEO_THUMBNAIL_THRESHOLD = int(os.getenv('VIDEO_THUMBNAIL_THRESHOLD', '9'))
TELEGRAM_CHAT_ID = int(os.getenv('TELEGRAM_CHAT_ID', "0"))  # 如果你需要在此脚本中发送到同一个群组
VIDEO_SPLIT_THRESHOLD_MB = int(os.getenv('VIDEO_SPLIT_THRESHOLD_MB', '1980')) # 新增：视频分割阈值 (MB)

# 创建 Supabase 客户端
supabase = create_client(SUPABASE_URL, SUPABASE_KEY)

# =============== 抖音解析器 ===============
class DouyinParser:
    @staticmethod
    def _sort_bitrate_items(bit_rate_list):
        def sort_key(item):
            play_addr = item.get('play_addr', {})
            width = play_addr.get('width', 0)
            height = play_addr.get('height', 0)
            resolution = width * height
            fps = item.get('FPS', 0)
            bitrate = item.get('bit_rate', 0)
            return (-resolution, -fps, -bitrate)
        return sorted(bit_rate_list, key=sort_key)

    @staticmethod
    def _get_first_url(url_data):
        url_list = url_data.get('url_list', []) if url_data else []
        if url_list and isinstance(url_list, list) and len(url_list) > 0:
            return url_list[0]
        return ""

    @staticmethod
    def _get_backup_urls(url_data, max_backup=2):
        """获取备用URL列表（最多max_backup个）"""
        url_list = url_data.get('url_list', []) if url_data else []
        if not url_list or not isinstance(url_list, list) or len(url_list) <= 1:
            return []
        # 返回从第二个URL开始，最多max_backup个
        return url_list[1:min(len(url_list), 1+max_backup)]

    @staticmethod
    def _get_best_play_url(video_info, return_backups=False):
        """
        获取最佳播放URL
        Args:
            video_info: 视频信息字典
            return_backups: 是否返回备用链接
        
        Returns:
            如果return_backups=False，返回最佳URL字符串
            如果return_backups=True，返回(最佳URL, [备用URL1, 备用URL2, ...])
        """
        bit_rate_list = video_info.get('bit_rate', []) if video_info else []
        best_url = ""
        backup_urls = []
        
        if bit_rate_list:
            sorted_items = DouyinParser._sort_bitrate_items(bit_rate_list)
            
            # 找到最佳URL
            for item in sorted_items:
                play_addr = item.get('play_addr', {})
                url = DouyinParser._get_first_url(play_addr)
                if url and "watermark" not in url:
                    best_url = url
                    if return_backups:
                        # 收集该条目的所有备用URL
                        backup_urls.extend(DouyinParser._get_backup_urls(play_addr))
                    break # 找到最佳无水印就停止
            
            # 如果没找到无水印URL，尝试寻找其他码率的URL作为备用
            if return_backups and not backup_urls and len(sorted_items) > 1:
                for item in sorted_items[1:]:  # 从第二个开始
                    play_addr = item.get('play_addr', {})
                    url = DouyinParser._get_first_url(play_addr)
                    if url and url != best_url and "watermark" not in url:
                        backup_urls.append(url)
                        if len(backup_urls) >= 2:  # 最多2个备用
                            break

        # 如果bit_rate_list没有找到合适的，尝试使用默认play_addr
        if not best_url:
            play_addr = video_info.get('play_addr', {})
            best_url = DouyinParser._get_first_url(play_addr)
            if return_backups:
                backup_urls = DouyinParser._get_backup_urls(play_addr)
                
        # 最后检查一下 best_url 是否有水印，如果 fallback_url 没有，用 fallback_url
        fallback_url = DouyinParser._get_first_url(video_info.get('play_addr', {}))
        if best_url and "watermark" in best_url and fallback_url and "watermark" not in fallback_url:
             best_url = fallback_url
             if return_backups:
                 backup_urls = DouyinParser._get_backup_urls(video_info.get('play_addr', {}))

        if return_backups:
            # 最多返回2个备用URL
            return best_url, backup_urls[:2]
        return best_url

    @staticmethod
    def _get_best_image_url(img_data):
        url_list = img_data.get('url_list', []) if img_data else []
        no_water_urls = [u for u in url_list if "water" not in u]
        if no_water_urls:
            return no_water_urls[0]
        if url_list:
            return url_list[0]
        return ""

    @staticmethod
    def parse_aweme(resp_data):
        try:
            data = resp_data.get('data', {}) if resp_data else {}
            if not isinstance(data, dict):
                return {}
            aweme_id = data.get('aweme_id', "")
            desc = data.get('desc', "")
            create_time_ts = data.get('create_time', 0)

            create_time_str = ""
            try:
                # 转换为北京时间（UTC+8）
                from datetime import timezone, timedelta
                beijing_tz = timezone(timedelta(hours=8))
                # 先创建UTC时间，然后转换为北京时间
                utc_time = datetime.fromtimestamp(create_time_ts, tz=timezone.utc)
                beijing_time = utc_time.astimezone(beijing_tz)
                create_time_str = beijing_time.strftime('%Y-%m-%d %H:%M:%S')
            except:
                pass

            author = data.get('author', {})
            author_info = {
                'nickname': author.get('nickname', ""),
                'uid': author.get('uid', ""),
                'sec_uid': author.get('sec_uid', ""),
                'unique_id': author.get('unique_id', ""),
                'follower_count': author.get('follower_count', 0),
                'total_favorited': author.get('total_favorited', 0),
            }

            location_info = {}
            anchor_info = data.get('anchor_info', {})
            try:
                if isinstance(anchor_info.get('extra'), str):
                    extra_data = json.loads(anchor_info.get('extra'))
                    if isinstance(extra_data, dict):
                        address_info = extra_data.get('address_info', {})
                        location_info = {
                            'province': address_info.get('province', ""),
                            'city': address_info.get('city', "")
                        }
            except:
                pass

            statistics = data.get('statistics', {})
            stats = {
                'comment_count': statistics.get('comment_count', 0),
                'digg_count': statistics.get('digg_count', 0),
                'collect_count': statistics.get('collect_count', 0),
                'share_count': statistics.get('share_count', 0)
            }

            music = data.get('music', {})
            music_info = {
                'title': music.get('title', ""),
                'author': music.get('author', ""),
                'play_url': DouyinParser._get_first_url(music.get('play_url', {}))
            }

            images = data.get('images', [])
            video_info = data.get('video', {})
            duration_ms = data.get('duration', 0)
            duration_s = duration_ms / 1000.0

            if images:
                media_info = {
                    'cover_url': "",
                    'play_url': "",
                    'width': 0,
                    'height': 0,
                    'duration': duration_s,
                    'images': []
                }
                for img in images:
                    img_info = {
                        'url': DouyinParser._get_best_image_url(img),
                        'width': img.get('width', 0),
                        'height': img.get('height', 0),
                        'video': None
                    }
                    vid_data = img.get('video')
                    if vid_data and isinstance(vid_data, dict):
                        best_play_url, vid_backup_urls = DouyinParser._get_best_play_url(vid_data, return_backups=True)
                        img_info['video'] = {
                            'url': best_play_url,
                            'width': vid_data.get('width', 0),
                            'height': vid_data.get('height', 0),
                            'backup_urls': vid_backup_urls
                        }
                    media_info['images'].append(img_info)
            else:
                media_info = {
                    'cover_url': DouyinParser._get_first_url(video_info.get('cover', {})),
                    'play_url': DouyinParser._get_best_play_url(video_info),
                    'width': video_info.get('width', 0),
                    'height': video_info.get('height', 0),
                    'duration': duration_s,
                    'images': [],
                    'backup_urls': []
                }

            return {
                'base_info': {
                    'aweme_id': aweme_id,
                    'desc': desc,
                    'create_time': create_time_str
                },
                'author_info': author_info,
                'location_info': location_info,
                'statistics': stats,
                'music_info': music_info,
                'media_info': media_info
            }
        except Exception as e:
            logger.error(f"parse_aweme error: {str(e)}")
            return {}

    @staticmethod
    def parse_user(resp_data):
        try:
            data = resp_data.get('data', {}) if resp_data else {}
            if not isinstance(data, dict):
                return None
            author = data.get('author', {})
            if not author or not author.get('uid') or not author.get('sec_uid'):
                return None
            user_data = {
                'uid': author.get('uid'),
                'sec_uid': author.get('sec_uid'),
                'short_id': author.get('short_id'),
                'unique_id': author.get('unique_id', ""),
                'nickname': author.get('nickname'),
                'signature': author.get('signature'),
                'user_age': author.get('user_age'),
                'avatar_thumb_uri': author.get('avatar_thumb', {}).get('uri'),
                'avatar_thumb_url': (author.get('avatar_thumb', {}).get('url_list') or [None])[0],
                'create_time': datetime.fromtimestamp(author.get('create_time', 0)).isoformat() if author.get('create_time') else None,
                'follower_count': author.get('follower_count', 0),
                'following_count': author.get('following_count', 0),
                'total_favorited': author.get('total_favorited', 0),
                'favoriting_count': author.get('favoriting_count', 0),
                'status': author.get('status', 1),
                'verification_type': author.get('verification_type'),
                'user_canceled': author.get('user_canceled', False),
                'mate_add_permission': author.get('mate_add_permission'),
                'custom_verify': author.get('custom_verify'),
                'enterprise_verify_reason': author.get('enterprise_verify_reason'),
                'prevent_download': author.get('prevent_download', False),
                'contacts_status': author.get('contacts_status'),
                'cover_url': (author.get('cover_url', [{}])[0].get('url_list') or [None])[0],
                'last_fetched_at': datetime.now().isoformat()
            }
            return user_data
        except Exception as e:
            logger.error(f"parse_user error: {str(e)}")
            return None

# =============== 一些辅助的函数和下载逻辑 ===============
async def download_file(url, max_retries=3, backup_urls=None):
    """
    下载文件，支持备用URL和更精细的重试逻辑
    Args:
        url: 主URL
        max_retries: 每个URL对于非连接中断/403错误的最大重试次数
        backup_urls: 备用URL列表
    """
    attempt = 1
    current_url = url
    tried_urls = set()  # 记录因达到最大重试次数而失败的URL

    # 备用URL为空时初始化为空列表
    if backup_urls is None:
        backup_urls = []

    # 备用URL队列
    backup_queue = list(backup_urls)

    while True:
        if not current_url:
            logger.error("[downloadFile] 当前URL为空，无法下载")
            return None

        # 如果当前 URL 已经因达到最大重试次数（非连接/403错误）而失败过，则尝试下一个
        if current_url in tried_urls:
            if backup_queue:
                next_url = backup_queue.pop(0)
                logger.info(f"[downloadFile] URL {current_url} 达到重试上限，切换到备用URL: {next_url}")
                current_url = next_url
                attempt = 1 # 重置尝试次数
                continue
            else:
                logger.error(f"[downloadFile] 主URL {url} 及所有备用URL都已尝试失败")
                return None

        try:
            logger.info(f"[downloadFile] GET {current_url}, attempt {attempt}")
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.get(current_url)

                # 处理HTTP状态码错误
                if not response.is_success:
                    # 403错误特殊处理
                    if response.status_code == 403:
                        logger.warning(f"[downloadFile] 遇到403错误 (attempt {attempt}/{max_retries}) for url: {current_url}")
                        if attempt < max_retries:
                            # 403错误但尝试次数未达到上限，继续重试当前URL
                            wait_sec = 2 ** attempt
                            logger.info(f"等待 {wait_sec} 秒后重试同一URL...")
                            await asyncio.sleep(wait_sec)
                            attempt += 1
                            continue # 重试当前URL
                        else:
                            # 达到尝试上限，标记此URL为失败，尝试切换URL
                            logger.warning(f"[downloadFile] URL {current_url} 403错误达到最大重试次数 ({max_retries})")
                            tried_urls.add(current_url) # 标记为失败
                            # 让循环在下一次迭代时处理切换逻辑
                            continue
                    else:
                        # 其他HTTP错误
                        logger.warning(f"[downloadFile] HTTP error! status: {response.status_code} (attempt {attempt}/{max_retries}) for url: {current_url}")
                        if attempt < max_retries:
                            wait_sec = 2 ** attempt
                            logger.info(f"等待 {wait_sec} 秒后重试同一URL...")
                            await asyncio.sleep(wait_sec)
                            attempt += 1
                            continue # 重试当前URL
                        else:
                            logger.error(f"[downloadFile] URL {current_url} 达到最大重试次数 ({max_retries})，标记失败")
                            tried_urls.add(current_url) # 标记为失败
                            continue # 让循环在下一次迭代时处理切换逻辑

                # 下载成功
                binary_data = response.content
                content_type = response.headers.get("content-type", "")
                return {"binary_data": binary_data, "content_type": content_type}

        except Exception as error:
            error_str = str(error)

            # 连接中断错误处理 - 无限重试当前URL
            if "peer closed connection without sending complete message" in error_str:
                logger.warning(f"[downloadFile] 连接中断错误: {error_str} for url: {current_url}")
                wait_sec = 2 # 短暂等待后重试
                logger.info(f"等待 {wait_sec} 秒后重试同一URL...")
                await asyncio.sleep(wait_sec)
                # attempt 保持不变，继续尝试当前 URL
                continue

            # 检查是否为403错误（从异常中判断）
            if "403" in error_str:
                 logger.warning(f"[downloadFile] 从异常中检测到403错误 (attempt {attempt}/{max_retries}) for url: {current_url}")
                 if attempt < max_retries:
                     wait_sec = 2 ** attempt
                     logger.info(f"等待 {wait_sec} 秒后重试同一URL...")
                     await asyncio.sleep(wait_sec)
                     attempt += 1
                     continue # 重试当前URL
                 else:
                     logger.warning(f"[downloadFile] URL {current_url} 403错误达到最大重试次数 ({max_retries})")
                     tried_urls.add(current_url) # 标记为失败
                     continue # 让循环在下一次迭代时处理切换逻辑

            # 其他所有异常
            logger.warning(f"[downloadFile] 遇到其他错误: {error_str} (attempt {attempt}/{max_retries}) for url: {current_url}")
            if attempt < max_retries:
                wait_sec = 2 ** attempt
                logger.info(f"等待 {wait_sec} 秒后重试...")
                await asyncio.sleep(wait_sec)
                attempt += 1
                continue # 重试当前URL
            else:
                # 当前URL达到最大重试次数
                logger.error(f"[downloadFile] URL {current_url} 达到其他错误最大重试次数 ({max_retries})，标记失败")
                tried_urls.add(current_url) # 标记为失败
                continue # 让循环在下一次迭代时处理切换逻辑

    # 如果循环结束仍未成功下载 (理论上应该在循环内部返回或记录错误后返回 None)
    logger.error(f"[downloadFile] 下载失败，已尝试所有URL和重试次数")
    return None

def build_caption_for_single(parsed_data):
    base = parsed_data.get('base_info', {})
    author = parsed_data.get('author_info', {})
    stats = parsed_data.get('statistics', {})
    music = parsed_data.get('music_info', {})
    location = parsed_data.get('location_info', {})

    lines = []
    if base.get('aweme_id'):
        lines.append(f"作品ID: {base.get('aweme_id')}")
    if base.get('desc'):
        lines.append(f"描述: {base.get('desc')}")
    if base.get('create_time'):
        lines.append(f"发布时间: {base.get('create_time')} (北京时间)")
    if author.get('nickname'):
        lines.append(f"作者昵称: {author.get('nickname')}")
    if author.get('unique_id'):
        lines.append(f"抖音号: {author.get('unique_id')}")
    if author.get('uid'):
        lines.append(f"作者UID: {author.get('uid')}")

    fc = author.get('follower_count')
    tf = author.get('total_favorited')
    if fc is not None and tf is not None:
        lines.append(f"粉丝数: {fc} | 获赞: {tf}")

    province = location.get('province', "")
    city = location.get('city', "")
    if province or city:
        lines.append(f"地点: {province} {city}".strip())

    digg = stats.get('digg_count')
    cmt = stats.get('comment_count')
    shr = stats.get('share_count')
    col = stats.get('collect_count')
    stats_parts = []
    if digg is not None:
        stats_parts.append(f"点赞: {digg}")
    if cmt is not None:
        stats_parts.append(f"评论: {cmt}")
    if shr is not None:
        stats_parts.append(f"分享: {shr}")
    if col is not None:
        stats_parts.append(f"收藏: {col}")
    if stats_parts:
        lines.append(" | ".join(stats_parts))

    if music.get('title') and music.get('author'):
        lines.append(f"音乐: {music.get('title')} - {music.get('author')}")

    return "\n".join(lines).strip()

def is_image_file(filename):
    ext = os.path.splitext(filename.lower())[1]
    return ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp']

def is_video_file(filename):
    ext = os.path.splitext(filename.lower())[1]
    return ext in ['.mp4', '.mov', '.avi', '.mkv', '.webm']

def is_audio_file(filename):
    ext = os.path.splitext(filename.lower())[1]
    return ext in ['.mp3', '.wav', '.m4a', '.flac', '.aac']

async def get_video_dimensions(video_path):
    try:
        cmd = f'ffprobe -v error -select_streams v:0 -show_entries stream=width,height -of json "{video_path}"'
        process = await asyncio.create_subprocess_shell(
            cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()
        if process.returncode != 0:
            logger.error(f"FFprobe error: {stderr.decode()}")
            return None, None
        data = json.loads(stdout.decode())
        stream = data.get('streams', [{}])[0]
        return stream.get('width'), stream.get('height')
    except Exception as e:
        logger.error(f"Error getting video dimensions: {str(e)}")
        return None, None

async def get_video_file_size_in_mb(file_path):
    try:
        stat_result = os.stat(file_path)
        file_size_mb = stat_result.st_size / (1024 * 1024)
        return file_size_mb
    except Exception as e:
        logger.error(f"获取文件大小失败: {str(e)}")
        return 0

async def kill_ffmpeg_processes():
    try:
        if os.name == 'nt':  # Windows
            cmd = 'taskkill /F /IM ffmpeg.exe'
        else:  # Linux/Unix
            cmd = "pkill -9 ffmpeg"
        process = await asyncio.create_subprocess_shell(
            cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        await process.communicate()
    except Exception as e:
        logger.error(f"终止 ffmpeg 进程时出错: {str(e)}")

async def extract_video_thumbnail(video_path, output_path=None):
    if output_path is None:
        base_path = os.path.splitext(video_path)[0]
        output_path = f"{base_path}_thumb.jpg"
    process = None
    try:
        cmd = f'ffmpeg -threads 1 -ss 1 -i "{video_path}" -vframes 1 -q:v 5 -f image2 "{output_path}" -y'
        process = await asyncio.create_subprocess_shell(cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        try:
            await asyncio.wait_for(process.communicate(), timeout=10.0)
        except asyncio.TimeoutError:
            logger.warning("提取预览图超时，终止进程")
            try:
                process.terminate()
                await asyncio.wait_for(process.wait(), timeout=1.0)
            except:
                pass
            await kill_ffmpeg_processes()
            return None
        if process.returncode != 0:
            logger.warning("尝试提取第1秒失败，改为第0.1秒")
            cmd2 = f'ffmpeg -threads 1 -ss 0.1 -i "{video_path}" -vframes 1 -q:v 5 -f image2 "{output_path}" -y'
            process2 = await asyncio.create_subprocess_shell(cmd2,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            try:
                await asyncio.wait_for(process2.communicate(), timeout=10.0)
            except asyncio.TimeoutError:
                logger.warning("提取预览图第二次尝试也超时")
                try:
                    process2.terminate()
                    await asyncio.wait_for(process2.wait(), timeout=0.5)
                except:
                    pass
                await kill_ffmpeg_processes()
                return None
            if process2.returncode != 0:
                logger.error("仍然失败，放弃生成预览图")
                return None
        if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
            return output_path
        else:
            logger.error("生成缩略图为空")
            return None
    except Exception as e:
        logger.error(f"提取视频缩略图失败: {str(e)}")
        if process:
            try:
                process.terminate()
            except:
                pass
        await kill_ffmpeg_processes()
        return None

async def get_video_duration(video_path: str) -> Optional[float]:
    """使用 ffprobe 获取视频时长（秒）"""
    try:
        cmd = f'ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "{video_path}"'
        process = await asyncio.create_subprocess_shell(
            cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()
        if process.returncode != 0:
            logger.error(f"FFprobe 获取时长错误 for {video_path}: {stderr.decode()}")
            return None
        return float(stdout.decode().strip())
    except Exception as e:
        logger.error(f"获取视频 {video_path} 时长时出错: {str(e)}")
        return None

async def split_video_if_too_large(video_path: str, output_dir: str, max_size_mb: int = VIDEO_SPLIT_THRESHOLD_MB) -> List[str]:
    """
    如果视频文件大小超过 max_size_mb，则将其分割。
    返回文件路径列表。如果未分割或失败，则列表只包含原始 video_path。
    """
    try:
        file_size_mb = await get_video_file_size_in_mb(video_path)
        if file_size_mb <= max_size_mb:
            return [video_path]  # 无需分割

        logger.info(f"视频 {video_path} ({file_size_mb:.2f}MB) 超过 {max_size_mb}MB，尝试分割。")
        os.makedirs(output_dir, exist_ok=True)

        duration = await get_video_duration(video_path)
        if duration is None:
            logger.error(f"无法获取视频 {video_path} 时长，无法分割。")
            return [video_path]

        # 估算分段数量和每段时长
        # 目标是使每段大小约等于 max_size_mb / 1.05 (留一些余量)
        # num_segments = int(file_size_mb / (max_size_mb / 1.05)) + 1
        # segment_duration_seconds = duration / num_segments
        # segment_duration_seconds = max(10, segment_duration_seconds) # 每段至少10秒

        # 使用 ffmpeg 的 -fs (文件大小限制) 参数尝试分割，但这与 -c copy 配合不佳，可能不准确。
        # 改为按时长分割，动态计算分段数量。
        # 更稳健的方法是基于目标分段大小和视频平均比特率来计算分段时长。
        # avg_bitrate_bps = (file_size_mb * 1024 * 1024 * 8) / duration
        # target_segment_size_bytes = (max_size_mb * 1024 * 1024) * 0.95 # 95% of max size
        # segment_duration_seconds = target_segment_size_bytes * 8 / avg_bitrate_bps

        # 简化：假设线性关系，计算分段时长，确保每个分段时长合理
        # 我们希望每个分段大致为 max_size_mb。
        # 一个更简单的方式是固定一个合理的分段时长，比如10-15分钟，除非视频很短。
        # 或者，根据总大小和目标大小，计算出大致需要多少段。
        num_expected_segments = int(file_size_mb / max_size_mb) + 1
        segment_duration_seconds = int(duration / num_expected_segments)
        segment_duration_seconds = max(60, segment_duration_seconds) # 每段至少60秒，除非总时长不足
        if duration <= segment_duration_seconds * 1.2 and num_expected_segments > 1 : # 如果总时长接近单段时长且期望分段，调整
             segment_duration_seconds = int(duration / 2) + 1 # 尝试分成两半

        base_name = os.path.splitext(os.path.basename(video_path))[0]
        output_pattern = os.path.join(output_dir, f"{base_name}_part%03d.mp4")

        cmd = (
            f'ffmpeg -threads 1 -i "{video_path}" -c copy -map 0 -segment_time {segment_duration_seconds} '
            f'-f segment -reset_timestamps 1 "{output_pattern}" -y'
        )
        logger.info(f"执行视频分割命令: {cmd}")
        process = await asyncio.create_subprocess_shell(
            cmd, stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE
        )
        stdout_data, stderr_data = await process.communicate()

        if process.returncode != 0:
            logger.error(f"ffmpeg 分割错误 for {video_path}: {stderr_data.decode()}")
            await kill_ffmpeg_processes() # 尝试杀死残留的 ffmpeg 进程
            return [video_path]

        segment_files = sorted([
            os.path.join(output_dir, f) for f in os.listdir(output_dir)
            if f.startswith(f"{base_name}_part") and f.endswith(".mp4")
        ])

        if not segment_files:
            logger.error(f"视频分割 {video_path} 未产生任何分段文件。")
            return [video_path]

        valid_segments = []
        for seg_path in segment_files:
            if os.path.exists(seg_path) and os.path.getsize(seg_path) > 0:
                seg_size_mb = await get_video_file_size_in_mb(seg_path)
                if seg_size_mb > max_size_mb * 1.1: # 允许10%的超出
                    logger.warning(f"分段 {seg_path} ({seg_size_mb:.2f}MB) 仍然过大 (阈值 {max_size_mb}MB)。可能导致发送失败。")
                valid_segments.append(seg_path)
            else:
                logger.warning(f"分段 {seg_path} 在分割后为空或不存在，已跳过。")
        
        if not valid_segments:
            logger.error(f"视频分割 {video_path} 未产生有效的片段文件。")
            return [video_path]

        logger.info(f"成功将 {video_path} 分割为 {len(valid_segments)} 个片段 (在 {output_dir})。")
        return valid_segments

    except Exception as e:
        logger.error(f"视频分割过程中发生意外错误 ({video_path}): {str(e)}", exc_info=True)
        return [video_path] # 发生任何错误都返回原始路径

async def download_music_file(music_url, output_dir, aweme_id):
    if not music_url:
        return None
    try:
        music_data = await download_file(music_url)
        if not music_data or not music_data.get("binary_data"):
            return None
        music_path = os.path.join(output_dir, f"{aweme_id}_music.mp3")
        os.makedirs(output_dir, exist_ok=True)
        with open(music_path, "wb") as f:
            f.write(music_data.get("binary_data"))
        logger.info(f"下载音乐文件成功: {music_path}")
        return music_path
    except Exception as e:
        logger.error(f"下载音乐文件失败: {str(e)}")
        return None

async def create_zip_archive(files, output_path):
    try:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file in files:
                if os.path.exists(file):
                    zipf.write(file, os.path.basename(file))
                else:
                    logger.warning(f"归档时跳过不存在的文件: {file}")
        logger.info(f"ZIP 文件创建完成: {output_path}, 大小: {os.path.getsize(output_path)} bytes")
        return output_path
    except Exception as e:
        logger.error(f"创建 ZIP 归档错误: {str(e)}")
        return None

# =============== 发送媒体逻辑 ===============
async def send_single_file(app, chat_id, file_path, caption, reply_markup=None):
    while True: # 添加循环以支持重试
        try:
            if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
                logger.info(f"文件无效或大小为0: {file_path}")
                return None
            if is_video_file(file_path):
                file_size_mb = await get_video_file_size_in_mb(file_path)
                width, height = await get_video_dimensions(file_path)
                thumbnail_path = None
                if file_size_mb >= VIDEO_THUMBNAIL_THRESHOLD:
                    logger.info(f"视频 {file_path} 大小 {file_size_mb:.2f}MB 超过阈值 {VIDEO_THUMBNAIL_THRESHOLD}MB, 生成预览图")
                    try:
                        thumbnail_path = await extract_video_thumbnail(file_path)
                        if not thumbnail_path or not os.path.exists(thumbnail_path):
                            logger.warning(f"未能成功生成视频 {file_path} 的预览图。")
                            thumbnail_path = None
                    except Exception as thumb_err:
                        logger.error(f"生成视频 {file_path} 预览图时出错: {thumb_err}")
                        thumbnail_path = None
                message = await app.send_video(
                    chat_id,
                    file_path,
                    caption=caption,
                    width=width,
                    height=height,
                    thumb=thumbnail_path,
                    reply_markup=reply_markup,
                    parse_mode=ParseMode.HTML
                )
                if DELETE_FILES and thumbnail_path and os.path.exists(thumbnail_path):
                    try:
                        os.unlink(thumbnail_path)
                    except Exception as del_err:
                        logger.error(f"删除缩略图失败: {del_err}")
                if message and hasattr(message, 'video'):
                    return message.video.file_id
                return None
            elif is_image_file(file_path):
                message = await app.send_photo(
                    chat_id,
                    file_path,
                    caption=caption,
                    reply_markup=reply_markup,
                    parse_mode=ParseMode.HTML
                )
                if message and hasattr(message, 'photo'):
                    return message.photo.file_id
                return None
            elif is_audio_file(file_path):
                message = await app.send_audio(
                    chat_id,
                    file_path,
                    caption=caption,
                    reply_markup=reply_markup,
                    parse_mode=ParseMode.HTML
                )
                if message and hasattr(message, 'audio'):
                    return message.audio.file_id
                return None
            else:
                message = await app.send_document(
                    chat_id,
                    file_path,
                    caption=caption,
                    reply_markup=reply_markup,
                    parse_mode=ParseMode.HTML
                )
                if message and hasattr(message, 'document'):
                    return message.document.file_id
                return None
        except FloodWait as e:
            wait_time = e.value # Pyrogram specific attribute for wait time
            logger.warning(f"Telegram API FloodWait: 要求等待 {wait_time} 秒. 将等待 {wait_time + 1} 秒后重试发送 {os.path.basename(file_path)}...")
            await asyncio.sleep(wait_time + 1)
            # continue loop to retry
        except Exception as e:
            logger.error(f"send_single_file error for {file_path}: {str(e)}")
            return None # Exit loop on other errors

async def send_media_group(app, chat_id, media_files, caption=None):
    media_group = []
    generated_thumbnails = []
    while True: # 添加循环以支持重试
        try:
            for file_path in media_files:
                if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
                    continue
                if is_image_file(file_path):
                    media_group.append(InputMediaPhoto(media=file_path))
                elif is_video_file(file_path):
                    width, height = await get_video_dimensions(file_path)
                    file_size_mb = await get_video_file_size_in_mb(file_path)
                    thumbnail_path = None
                    if file_size_mb >= VIDEO_THUMBNAIL_THRESHOLD:
                        thumbnail_path = await extract_video_thumbnail(file_path)
                        if thumbnail_path and os.path.exists(thumbnail_path):
                            generated_thumbnails.append(thumbnail_path)
                    media_group.append(InputMediaVideo(
                        media=file_path,
                        width=width,
                        height=height,
                        thumb=thumbnail_path
                    ))
                elif is_audio_file(file_path):
                    media_group.append(InputMediaAudio(media=file_path))
                else:
                    media_group.append(InputMediaDocument(media=file_path))
            if caption and media_group:
                media_group[-1].caption = caption
                media_group[-1].parse_mode = ParseMode.HTML
            if not media_group:
                logger.warning("没有有效的媒体文件可发送")
                return []
            messages = await app.send_media_group(chat_id, media_group)
            file_ids = []
            for msg in messages:
                # pyrogram里 photo 通常是list；video等直接是对象
                if hasattr(msg, 'photo') and msg.photo:
                    if isinstance(msg.photo, list):
                        file_ids.append(msg.photo[-1].file_id)
                    else:
                        file_ids.append(msg.photo.file_id)
                elif hasattr(msg, 'video') and msg.video:
                    file_ids.append(msg.video.file_id)
                elif hasattr(msg, 'audio') and msg.audio:
                    file_ids.append(msg.audio.file_id)
                elif hasattr(msg, 'document') and msg.document:
                    file_ids.append(msg.document.file_id)
            return file_ids # 成功则返回
        except FloodWait as e:
            wait_time = e.value
            logger.warning(f"Telegram API FloodWait: 要求等待 {wait_time} 秒. 将等待 {wait_time + 1} 秒后重试发送媒体组 ({len(media_files)}个文件)...")
            # 清空已构建的 media_group 和缩略图列表，以便重试时重新构建
            media_group = []
            generated_thumbnails = []
            await asyncio.sleep(wait_time + 1)
            # continue loop to retry
        except Exception as e:
            logger.error(f"发送媒体组失败: {str(e)}")
            return [] # 发生其他错误则返回空列表
        finally:
            if DELETE_FILES:
                for thumb in generated_thumbnails:
                    if thumb and os.path.exists(thumb):
                        try:
                            os.unlink(thumb)
                        except Exception as del_err:
                            logger.error(f"删除媒体组缩略图失败: {thumb}, {del_err}")

async def send_media_files(
    app,
    chat_id,
    media_files,
    douyin_url,
    caption_text,
    sec_uid,
    music_link="",
    from_author_posts=False,
    user_settings=None,
    aweme_id=None,
):
    if not user_settings:
        user_settings = {"send_file": True}
    
    # 初始时过滤有效文件
    current_valid_files = [f for f in media_files if os.path.exists(f) and os.path.getsize(f) > 0]
    if not current_valid_files:
        logger.info("无可发送文件。")
        return ""
    
    all_file_ids = []
    
    music_files = [f for f in current_valid_files if f.endswith("_music.mp3")]
    # 先发送音乐文件
    for music_file in music_files:
        music_file_id = await send_single_file(app, chat_id, music_file, "[音乐文件]")
        if music_file_id:
            all_file_ids.append(music_file_id)
            # current_valid_files.remove(music_file) # 不在此处移除，统一在后续处理

    # 非音乐文件列表
    non_music_downloaded_files = [f for f in current_valid_files if f not in music_files]

    # --- 开始视频分割逻辑 ---
    processed_non_music_files = []  # 将包含原始非视频文件、小视频文件和视频分段
    original_large_videos_to_delete_if_split = [] # 存储被成功分割的原始大视频文件路径

    for file_path in non_music_downloaded_files:
        if is_video_file(file_path):
            file_size_mb = await get_video_file_size_in_mb(file_path)
            if file_size_mb > VIDEO_SPLIT_THRESHOLD_MB:
                logger.info(f"视频 {file_path} ({file_size_mb:.2f}MB) 大小超过 {VIDEO_SPLIT_THRESHOLD_MB}MB，准备分割。")
                # 分割文件的输出目录，应位于当前作品的工作目录内
                base_name_no_ext = os.path.splitext(os.path.basename(file_path))[0]
                # os.path.dirname(file_path) 通常是 "downloads/for_{aweme_id}"
                segment_output_dir = os.path.join(os.path.dirname(file_path), f"{base_name_no_ext}_segments")
                
                segments = await split_video_if_too_large(file_path, segment_output_dir) # 默认使用全局 VIDEO_SPLIT_THRESHOLD_MB

                if segments and segments[0] != file_path:  # 表示分割成功且产生了新文件
                    logger.info(f"视频 {file_path} 已成功分割为 {len(segments)} 个片段。")
                    processed_non_music_files.extend(segments)
                    if DELETE_FILES: # 仅当 DELETE_FILES 为 True 时才标记原始大文件以便后续删除
                        original_large_videos_to_delete_if_split.append(file_path)
                else:  # 分割失败，或无需分割（例如函数返回原始路径），或未产生有效片段
                    logger.warning(f"视频 {file_path} 分割未进行或失败，将使用原始文件。")
                    processed_non_music_files.append(file_path)
            else:  # 视频文件大小未超限
                processed_non_music_files.append(file_path)
        else:  # 非视频文件（如图图片）
            processed_non_music_files.append(file_path)
    
    # processed_non_music_files 现在包含了所有图片、小视频和（可能的）视频片段
    # --- 结束视频分割逻辑 ---

    # 发送处理后的非音乐文件 (图片、视频、片段)
    if len(processed_non_music_files) == 1:
        file_id = await send_single_file(app, chat_id, processed_non_music_files[0], caption_text)
        if file_id:
            all_file_ids.append(file_id)
    elif len(processed_non_music_files) > 1:
        # 如果一个视频被分割，这里可能会有多个文件
        # Telegram 媒体组最多10个项目
        media_files_to_send_in_group = processed_non_music_files[:10]
        if len(processed_non_music_files) > 10:
             logger.warning(f"作品 {aweme_id or 'N/A'}: 有 {len(processed_non_music_files)} 个非音乐文件/片段。将在媒体组中发送前10个，其余尝试打包进ZIP。")
        
        file_ids_from_group = await send_media_group(app, chat_id, media_files_to_send_in_group, caption_text)
        all_file_ids.extend(file_ids_from_group)
        # 注意: 如果文件/片段超过10个，剩余的当前不会直接发送，而是会被尝试加入ZIP。

    # 打包文件 (ZIP)
    zip_file_sent_and_deleted = [] # 改为列表，记录所有已发送并计划删除的ZIP文件路径
    zip_files_created_paths = [] # 存储所有创建的ZIP文件路径

    if user_settings.get("send_file", True):
        try:
            # 判断是否有视频分段
            is_video_split = len(original_large_videos_to_delete_if_split) > 0
            
            # 情况1: 视频被分段，为每个分段创建一个单独的ZIP文件
            if is_video_split and processed_non_music_files:
                logger.info(f"视频已分段，为每个分段创建单独的ZIP文件")
                
                # 获取所有视频分段文件
                video_segments = [f for f in processed_non_music_files if is_video_file(f)]
                other_files = [f for f in processed_non_music_files if not is_video_file(f)]
                
                # 为每个视频分段创建一个ZIP
                for seg_idx, video_segment in enumerate(video_segments):
                    if not os.path.exists(video_segment) or os.path.getsize(video_segment) == 0:
                        continue
                        
                    # 当前ZIP包含: 当前视频分段 + 所有音乐文件 + 所有非视频文件(如图片)
                    current_segment_files = [video_segment] + music_files + other_files
                    
                    # 从文件名中提取段号
                    segment_base_name = os.path.splitext(os.path.basename(video_segment))[0]
                    part_match = re.search(r'_part(\d+)$', segment_base_name)
                    part_suffix = f"_part{part_match.group(1)}" if part_match else f"_part{seg_idx+1}"
                    
                    # ZIP文件命名
                    base_name = aweme_id or re.sub(r'_part\d+$', '', segment_base_name) or "douyin_archive"
                    zip_dir = os.path.dirname(video_segment)
                    os.makedirs(zip_dir, exist_ok=True)
                    zip_path = os.path.join(zip_dir, f"{base_name}{part_suffix}.zip")
                    
                    # 创建ZIP
                    try:
                        zip_file = await create_zip_archive(current_segment_files, zip_path)
                        if zip_file and os.path.exists(zip_file) and os.path.getsize(zip_file) > 0:
                            zip_files_created_paths.append(zip_file)
                            
                            # 检查ZIP大小
                            zip_size_mb = await get_video_file_size_in_mb(zip_file)
                            if zip_size_mb > VIDEO_SPLIT_THRESHOLD_MB:
                                logger.error(f"ZIP文件 {zip_file} ({zip_size_mb:.2f}MB) 过大无法发送 (限制 {VIDEO_SPLIT_THRESHOLD_MB}MB)。")
                                if DELETE_FILES:
                                    try:
                                        os.unlink(zip_file)
                                        logger.info(f"已删除过大的ZIP文件: {zip_file}")
                                    except Exception as e_del_zip:
                                        logger.error(f"删除过大ZIP文件 {zip_file} 失败: {e_del_zip}")
                            else:
                                # 发送ZIP
                                reply_markup = None
                                if aweme_id:
                                    reply_markup = InlineKeyboardMarkup([
                                        [InlineKeyboardButton("打开作品", url=f"https://www.douyin.com/video/{aweme_id}")]
                                    ])
                                
                                zip_caption = f"媒体文件打包 ({aweme_id or base_name}) - 片段 {part_match.group(1) if part_match else seg_idx+1}"
                                
                                # 发送ZIP文件并处理可能的FloodWait
                                while True:
                                    try:
                                        message = await app.send_document(chat_id, zip_file, caption=zip_caption, reply_markup=reply_markup)
                                        if message and hasattr(message, 'document'):
                                            all_file_ids.append(message.document.file_id)
                                        if DELETE_FILES:
                                            zip_file_sent_and_deleted.append(zip_file)
                                            if os.path.exists(zip_file):
                                                try:
                                                    os.unlink(zip_file)
                                                    logger.info(f"已发送并删除ZIP文件: {zip_file}")
                                                except Exception as e_del_sent_zip:
                                                    logger.error(f"删除已发送的ZIP文件 {zip_file} 失败: {e_del_sent_zip}")
                                        break
                                    except FloodWait as e:
                                        wait_time = e.value
                                        logger.warning(f"Telegram API FloodWait: 要求等待 {wait_time} 秒. 将等待 {wait_time + 1} 秒后重试发送 ZIP 文件 {os.path.basename(zip_file)}...")
                                        await asyncio.sleep(wait_time + 1)
                                    except Exception as zip_send_err:
                                        logger.error(f"发送 ZIP 文件 {zip_file} 时发生错误: {zip_send_err}")
                                        break
                    except Exception as zip_create_err:
                        logger.error(f"为分段 {video_segment} 创建ZIP文件时发生错误: {zip_create_err}")
            
            # 情况2: 视频未分段，使用原有逻辑创建单个ZIP
            else:
                # 要打包的文件
                files_to_zip = processed_non_music_files[:] + music_files
                
                if files_to_zip:
                    first_file_for_naming = files_to_zip[0]
                    # 从文件名中提取基础名
                    base_name_for_zip_raw = os.path.splitext(os.path.basename(first_file_for_naming))[0]
                    base_name_for_zip = re.sub(r'_part\d+$', '', base_name_for_zip_raw)
                    base_name_for_zip = aweme_id or base_name_for_zip or "douyin_archive"
                    
                    # ZIP存放目录
                    zip_dir = os.path.dirname(first_file_for_naming) if first_file_for_naming else os.path.join("downloads", f"for_{aweme_id or 'zip_temp'}")
                    os.makedirs(zip_dir, exist_ok=True)
                    zip_path = os.path.join(zip_dir, f"{base_name_for_zip}.zip")
                    
                    # 创建ZIP
                    zip_file = await create_zip_archive(files_to_zip, zip_path)
                    if zip_file and os.path.exists(zip_file) and os.path.getsize(zip_file) > 0:
                        zip_files_created_paths.append(zip_file)
                        
                        # 检查ZIP大小
                        zip_size_mb = await get_video_file_size_in_mb(zip_file)
                        if zip_size_mb > VIDEO_SPLIT_THRESHOLD_MB:
                            logger.error(f"ZIP文件 {zip_file} ({zip_size_mb:.2f}MB) 过大无法发送 (限制 {VIDEO_SPLIT_THRESHOLD_MB}MB)。")
                            if DELETE_FILES and os.path.exists(zip_file):
                                try:
                                    os.unlink(zip_file)
                                    logger.info(f"已删除过大的ZIP文件: {zip_file}")
                                except Exception as e_del_zip:
                                    logger.error(f"删除过大ZIP文件 {zip_file} 失败: {e_del_zip}")
                        else:
                            # 发送ZIP
                            reply_markup = None
                            if aweme_id:
                                reply_markup = InlineKeyboardMarkup([
                                    [InlineKeyboardButton("打开作品", url=f"https://www.douyin.com/video/{aweme_id}")]
                                ])
                            
                            zip_caption = f"媒体文件打包 ({aweme_id or base_name_for_zip})"
                            
                            # 发送ZIP文件并处理可能的FloodWait
                            while True:
                                try:
                                    message = await app.send_document(chat_id, zip_file, caption=zip_caption, reply_markup=reply_markup)
                                    if message and hasattr(message, 'document'):
                                        all_file_ids.append(message.document.file_id)
                                    if DELETE_FILES:
                                        zip_file_sent_and_deleted.append(zip_file)
                                        if os.path.exists(zip_file):
                                            try:
                                                os.unlink(zip_file)
                                                logger.info(f"已发送并删除ZIP文件: {zip_file}")
                                            except Exception as e_del_sent_zip:
                                                logger.error(f"删除已发送的ZIP文件 {zip_file} 失败: {e_del_sent_zip}")
                                    break
                                except FloodWait as e:
                                    wait_time = e.value
                                    logger.warning(f"Telegram API FloodWait: 要求等待 {wait_time} 秒. 将等待 {wait_time + 1} 秒后重试发送 ZIP 文件 {os.path.basename(zip_file)}...")
                                    await asyncio.sleep(wait_time + 1)
                                except Exception as zip_send_err:
                                    logger.error(f"发送 ZIP 文件 {zip_file} an时发生错误: {zip_send_err}")
                                    break
                    elif zip_file:
                        logger.warning(f"创建的 ZIP 文件无效或为空: {zip_file}")
                    else:
                        logger.warning(f"创建 ZIP 文件失败。")
                else:
                    logger.info("没有文件可供打包进ZIP。")
        
        except Exception as e:
            logger.error(f"打包或发送 ZIP 失败: {str(e)}", exc_info=True)

    # 清理文件
    if DELETE_FILES:
        # 1. 删除被成功分割的原始大视频文件
        for orig_vid_path in original_large_videos_to_delete_if_split:
            if os.path.exists(orig_vid_path):
                try:
                    os.unlink(orig_vid_path)
                    logger.info(f"已删除被分割的原始大视频文件: {orig_vid_path}")
                except Exception as e_del_orig:
                    logger.error(f"删除原始大视频文件 {orig_vid_path} 失败: {e_del_orig}")
        
        # 2. 删除所有已处理的非音乐文件（包括片段、小视频、图片）和音乐文件
        files_to_delete_after_processing = music_files + processed_non_music_files
        
        for file_path_to_delete in files_to_delete_after_processing:
            if os.path.exists(file_path_to_delete):
                try:
                    os.unlink(file_path_to_delete)
                except Exception as e_del_proc:
                    logger.error(f"删除处理过的文件失败: {file_path_to_delete}, {str(e_del_proc)}")
        
        # 3. 检查所有创建的ZIP文件，删除未发送或发送失败的
        for zip_path in zip_files_created_paths:
            if zip_path not in zip_file_sent_and_deleted and os.path.exists(zip_path):
                try:
                    os.unlink(zip_path)
                    logger.info(f"已删除未发送或发送失败的ZIP文件: {zip_path}")
                except Exception as e_del_zip_leftover:
                    logger.error(f"删除残留ZIP文件 {zip_path} 失败: {e_del_zip_leftover}")

    # 更新数据库中的file_id
    if aweme_id:
        file_id_string = ';'.join(filter(None, all_file_ids)) if all_file_ids else "NO_FILE_ID"
        for retry in range(3):
            try:
                supabase.table("douyin").update({
                    "file_id": file_id_string,
                    "bot_token": BOT_TOKEN
                }).eq("aweme_id", aweme_id).execute()
                logger.info(f"成功更新作品 {aweme_id} 的 file_id => {file_id_string}")
                break
            except Exception as e:
                if retry < 2:
                    logger.warning(f"更新作品 {aweme_id} 第 {retry+1} 次失败: {str(e)}，重试...")
                    await asyncio.sleep(1)
                else:
                    logger.error(f"更新作品 {aweme_id} file_id 最终失败: {str(e)}")
    return ';'.join(all_file_ids)

# =============== 数据库逻辑 ===============
async def save_douyin_to_database(parsed_data, is_full_scan=False, failed_reason=None):
    try:
        base_info = parsed_data.get('base_info', {})
        author_info = parsed_data.get('author_info', {})
        location_info = parsed_data.get('location_info', {})
        music_info = parsed_data.get('music_info', {})
        media_info = parsed_data.get('media_info', {})
        aweme_id = base_info.get('aweme_id', "")
        if not aweme_id:
            logger.error("保存数据库失败: 缺少作品ID")
            return {"status": "error", "message": "Missing aweme_id"}

        create_time = base_info.get('create_time', "") or None
        data = {
            "aweme_id": aweme_id,
            "description": base_info.get('desc', ""),
            "create_time": create_time,
            "nickname": author_info.get('nickname', ""),
            "uid": author_info.get('uid', ""),
            "sec_uid": author_info.get('sec_uid', ""),
            "unique_id": author_info.get('unique_id', ""),
            "follower_count": author_info.get('follower_count', 0),
            "total_favorited": author_info.get('total_favorited', 0),
            "province": location_info.get('province', ""),
            "city": location_info.get('city', ""),
            "music_title": music_info.get('title', ""),
            "music_author": music_info.get('author', ""),
            "music_play_url": music_info.get('play_url', ""),
            "cover_url": media_info.get('cover_url', ""),
            "media_play_url": media_info.get('play_url', ""),
            "duration": media_info.get('duration', 0),
            "bot_token": BOT_TOKEN,
            "failed": failed_reason
        }

        existing_data = None
        try:
            response = supabase.table("douyin").select("*").eq("aweme_id", aweme_id).limit(1).execute()
            existing_data = response.data[0] if response.data else None
        except Exception as e:
            logger.error(f"检查作品 {aweme_id} 是否存在失败: {str(e)}")

        if existing_data:
            logger.info(f"作品 {aweme_id} 已存在，执行 upsert 覆盖更新")

        try:
            supabase.table("douyin").upsert(data).execute()
            if failed_reason:
                logger.info(f"记录作品 {aweme_id} 的失败原因: {failed_reason}")
            else:
                logger.info(f"成功保存/更新作品 {aweme_id} 到数据库")
            return {"status": "saved"}
        except Exception as e:
            err_msg = f"数据库 upsert 异常: {str(e)}"
            logger.error(err_msg)
            return {"status": "error", "message": err_msg}
    except Exception as e:
        error_detail = str(e)
        logger.error(f"保存数据库异常: {error_detail}")
        return {"status": "error", "message": error_detail}

async def save_douyin_user_to_database(user_data):
    try:
        if not user_data or not user_data.get('uid') or not user_data.get('sec_uid'):
            logger.error("保存用户数据失败: 缺少必要字段 uid 或 sec_uid")
            return False
        # 先获取现有 surveillance, once
        try:
            response = supabase.table("douyin_user").select("surveillance, once").eq("uid", user_data.get('uid')).limit(1).execute()
            existing_user = response.data
            if existing_user and len(existing_user) > 0:
                user_data['surveillance'] = existing_user[0].get('surveillance')
                user_data['once'] = existing_user[0].get('once')
        except Exception as e:
            logger.error(f"获取用户现有数据失败: {str(e)}")
        supabase.table("douyin_user").upsert(user_data).execute()
        logger.info(f"成功更新用户 {user_data.get('uid')} ({user_data.get('nickname')}) 到数据库")
        return True
    except Exception as e:
        logger.error(f"保存用户数据库异常: {str(e)}")
        return False

# =============== API调用，用于获取视频 JSON ===============
async def fetch_one_video_api(aweme_id, retry_count=0):
    try:
        base_url = f"{API_BASE_URL}/api/hybrid/video_data"
        douyin_url = f"https://www.douyin.com/video/{aweme_id}"
        params = {"url": douyin_url, "minimal": "false"}
        query_str = "&".join([f"{k}={v}" for k,v in params.items()])
        url = f"{base_url}?{query_str}"
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(url)
            if not response.is_success:
                if response.status_code == 400:
                    logger.warning(f"主API返回400，尝试备用接口: {aweme_id}")
                    return await fetch_one_video_backup_api(aweme_id)
                else:
                    raise Exception(f"HTTP error! status: {response.status_code}")
            json_data = response.json()
            if is_unavailable_content(json_data):
                logger.warning(f"作品 {aweme_id} 存在但不可用: {get_unavailable_reason(json_data)}")
                return build_unavailable_response(json_data)
            return json_data
    except Exception as e:
        logger.error(f"fetch_one_video_api error: {str(e)}")
        if '429' in str(e) and retry_count < 3:
            wait_seconds = 15
            logger.info(f"重试: 等待 {wait_seconds} 秒后再次请求 {aweme_id}")
            await asyncio.sleep(wait_seconds)
            return await fetch_one_video_api(aweme_id, retry_count + 1)
        return {}

async def fetch_one_video_backup_api(aweme_id):
    try:
        backup_url = f"{API_BASE_URL}/api/douyin/web/fetch_one_video?aweme_id={aweme_id}"
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(backup_url)
            if not response.is_success:
                raise Exception(f"备用API HTTP error! status: {response.status_code}")
            json_data = response.json()
            if is_unavailable_content(json_data):
                logger.warning(f"备用API: 作品 {aweme_id} 不可用: {get_unavailable_reason(json_data)}")
                return build_unavailable_response(json_data)
            return json_data
    except Exception as e:
        logger.error(f"备用API获取作品失败: {str(e)}")
        return {}

def is_unavailable_content(resp_data):
    if not resp_data:
        return False
    data = resp_data.get('data', {})
    if "aweme_detail" in data and data["aweme_detail"] is None and "filter_detail" in data:
        return True
    if not data.get('aweme_id') and "filter_detail" in data:
        return True
    return False

def get_unavailable_reason(resp_data):
    data = resp_data.get('data', {})
    filter_detail = data.get('filter_detail', {})
    detail_msg = filter_detail.get('detail_msg', '')
    filter_reason = filter_detail.get('filter_reason', '')
    notice = filter_detail.get('notice', '')
    if detail_msg:
        return detail_msg
    elif notice and filter_reason:
        return f"{notice}: {filter_reason}"
    elif filter_reason:
        return filter_reason
    else:
        return "作品不可用，无具体原因"

def build_unavailable_response(resp_data):
    data = resp_data.get('data', {})
    filter_detail = data.get('filter_detail', {})
    aweme_id = filter_detail.get('aweme_id', '')
    error_message = get_unavailable_reason(resp_data)
    return {
        "data": {
            "aweme_id": aweme_id,
            "desc": error_message,
            "create_time": 0,
            "author": {},
            "video": {},
            "images": [],
            "unavailable": True
        }
    }

# =============== 作品处理相关 ===============
async def process_single_aweme(app, aweme_id, current_author_info=None):
    """
    本脚本内版本的单个作品处理函数。
    """
    logger.info(f"开始处理作品: {aweme_id} (full-scan mode)")

    wdir = os.path.join("downloads", f"for_{aweme_id}")
    os.makedirs(wdir, exist_ok=True)
    success = False
    current_sec_uid = current_author_info.get('sec_uid') if current_author_info else None

    try:
        resp_data = await fetch_one_video_api(aweme_id)
        if resp_data.get('data', {}).get('unavailable'):
            error_msg = resp_data.get('data', {}).get('desc', '作品不可用')
            logger.warning(f"作品 {aweme_id} 不可用: {error_msg}")
            minimal_data = {
                'base_info': {'aweme_id': aweme_id, 'desc': error_msg},
                'author_info': {'sec_uid': current_sec_uid},
            }
            await save_douyin_to_database(minimal_data, is_full_scan=True, failed_reason="not available")
            return True

        parsed_data = DouyinParser.parse_aweme(resp_data)
        if not parsed_data or not parsed_data.get('base_info', {}).get('aweme_id'):
            error_msg = f"作品 {aweme_id} 解析失败或无 aweme_id"
            logger.error(error_msg)
            minimal_data = {
                'base_info': {'aweme_id': aweme_id},
                'author_info': {'sec_uid': current_sec_uid},
            }
            await save_douyin_to_database(minimal_data, is_full_scan=True, failed_reason=error_msg)
            return False

        # 保存到数据库（初步）
        save_result = await save_douyin_to_database(parsed_data, is_full_scan=True, failed_reason=None)
        if save_result.get("status") == "error":
            logger.error(f"保存作品 {aweme_id} 到数据库失败: {save_result.get('message')}")
            return False

        # 下载并发送
        try:
            caption_text = build_caption_for_single(parsed_data)
            media_info = parsed_data.get('media_info', {})
            images = media_info.get('images', [])
            play_url = media_info.get('play_url', "")
            backup_urls = media_info.get('backup_urls', [])
            music_url = parsed_data.get('music_info', {}).get('play_url', "")

            download_tasks = []
            file_paths = []
            if images:
                for idx, img_obj in enumerate(images):
                    img_url = img_obj.get('url')
                    if img_url:
                        img_path = os.path.join(wdir, f"{aweme_id}_{idx}.jpg")
                        download_tasks.append((img_path, download_file(img_url)))
                        file_paths.append(img_path)
                    vid = img_obj.get('video')
                    if vid and vid.get('url'):
                        vid_path = os.path.join(wdir, f"{aweme_id}_{idx}.mp4")
                        vid_backup_urls = vid.get('backup_urls', [])
                        download_tasks.append((vid_path, download_file(vid.get('url'), backup_urls=vid_backup_urls)))
                        file_paths.append(vid_path)
            elif play_url:
                vid_path = os.path.join(wdir, f"{aweme_id}.mp4")
                download_tasks.append((vid_path, download_file(play_url, backup_urls=backup_urls)))
                file_paths.append(vid_path)

            if music_url:
                music_path = os.path.join(wdir, f"{aweme_id}_music.mp3")
                download_tasks.append((music_path, download_file(music_url)))
                file_paths.append(music_path)

            any_download_failed = False
            downloaded_files = []
            if download_tasks:
                logger.info(f"作品 {aweme_id} 有 {len(download_tasks)} 个文件需要下载")
                paths = [d[0] for d in download_tasks]
                tasks = [d[1] for d in download_tasks]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                for i, result in enumerate(results):
                    path = paths[i]
                    if isinstance(result, Exception):
                        logger.error(f"下载失败: {path}: {str(result)}")
                        any_download_failed = True
                        continue
                    if not result or not result.get("binary_data"):
                        logger.error(f"下载失败: {path}, 空响应")
                        any_download_failed = True
                        continue
                    try:
                        with open(path, "wb") as f:
                            f.write(result["binary_data"])
                        downloaded_files.append(path)
                    except Exception as e:
                        logger.error(f"保存文件 {path} 失败: {str(e)}")
                        any_download_failed = True

            if downloaded_files:
                file_id_str = await send_media_files(
                    app, TELEGRAM_CHAT_ID, downloaded_files,
                    f"https://www.douyin.com/video/{aweme_id}",
                    caption_text,
                    current_sec_uid,
                    aweme_id=aweme_id
                )
                if file_id_str:
                    success = True
                    if any_download_failed:
                        partial_msg = f"部分媒体下载失败，但其余已发送"
                        logger.warning(f"作品 {aweme_id} {partial_msg}")
                        await save_douyin_to_database(parsed_data, is_full_scan=True, failed_reason=partial_msg)
                else:
                    err_msg = f"文件发送或数据库更新不成功"
                    logger.error(err_msg)
                    await save_douyin_to_database(parsed_data, is_full_scan=True, failed_reason=err_msg)
            else:
                err_msg = f"作品 {aweme_id} 没有可发送媒体文件"
                logger.error(err_msg)
                await save_douyin_to_database(parsed_data, is_full_scan=True, failed_reason=err_msg)
                success = False

        except Exception as e:
            err_msg = f"处理作品 {aweme_id} 下载/发送阶段异常: {str(e)}"
            logger.error(err_msg, exc_info=True)
            await save_douyin_to_database(parsed_data, is_full_scan=True, failed_reason=err_msg)
            success = False

    except Exception as e:
        err_msg = f"获取或解析作品 {aweme_id} 异常: {str(e)}"
        logger.error(err_msg, exc_info=True)
        minimal_data = {
            'base_info': {'aweme_id': aweme_id},
            'author_info': {'sec_uid': current_sec_uid},
        }
        await save_douyin_to_database(minimal_data, is_full_scan=True, failed_reason=err_msg)
        success = False
    finally:
        if DELETE_FILES and os.path.exists(wdir):
            try:
                shutil.rmtree(wdir)
            except Exception as e:
                logger.error(f"删除目录 {wdir} 失败: {str(e)}")

    return success

async def fetch_user_post_videos_api(sec_user_id, max_cursor=0, count=40):
    try:
        base_url = f"{API_BASE_URL}/api/douyin/web/fetch_user_post_videos"
        params = {
            "sec_user_id": sec_user_id,
            "max_cursor": max_cursor,
            "count": count
        }
        query_str = "&".join([f"{k}={v}" for k,v in params.items()])
        url = f"{base_url}?{query_str}"
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(url)
            if not response.is_success:
                raise Exception(f"HTTP error! status: {response.status_code}")
            return response.json()
    except Exception as e:
        logger.error(f"fetch_user_post_videos_api error: {str(e)}")
        return {}

async def fetch_user_post_videos_from_tikhub(sec_user_id, max_cursor=0, count=40):
    try:
        base_url = "https://beta.tikhub.io/api/v1/douyin/app/v3/fetch_user_post_videos"
        params = {
            "sec_user_id": sec_user_id,
            "max_cursor": max_cursor,
            "count": count
        }
        query_str = "&".join([f"{k}={v}" for k,v in params.items()])
        url = f"{base_url}?{query_str}"
        tikhub_api_token = os.getenv('TIKHUB_API_TOKEN', '')
        headers = {
            'Authorization': f'Bearer {tikhub_api_token}'
        } if tikhub_api_token else {}
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(url, headers=headers)
            if not response.is_success:
                raise Exception(f"TikHub API HTTP error! status: {response.status_code}")
            return response.json()
    except Exception as e:
        logger.error(f"TikHub API error: {str(e)}")
        return {}

async def process_author_works(app, author_info):
    """
    专门处理作者的"全量作品"逻辑：surveillance=true & once=false.
    处理完成后将 once=true。
    """
    sec_user_id = author_info.get('sec_uid')
    author_name = author_info.get('nickname') or author_info.get('unique_id') or sec_user_id
    logger.info(f"[全量] 开始处理作者 {author_name} ({sec_user_id}) 的作品...")

    aweme_ids = []
    max_cursor = 0
    has_more = True
    page_count = 0
    use_tikhub_api = False

    while has_more:
        page_count += 1
        resp_json = None
        if not use_tikhub_api:
            resp_json = await fetch_user_post_videos_api(sec_user_id, max_cursor, 40)
        else:
            resp_json = await fetch_user_post_videos_from_tikhub(sec_user_id, max_cursor, 40)

        data = resp_json.get('data', {})
        aweme_list = data.get('aweme_list', [])
        if page_count > 1 and not aweme_list and has_more and not use_tikhub_api:
            logger.warning(f"第 {page_count} 页原API返回空，尝试切换到 TikHub API 获取")
            use_tikhub_api = True
            max_cursor = 0
            page_count = 0
            aweme_ids = []
            continue

        original_count = len(aweme_list)
        filtered_aweme_list = []
        for item in aweme_list:
            if isinstance(item, dict):
                item_sec_uid = item.get('author', {}).get('sec_uid')
                item_aweme_id = item.get('aweme_id')
                if item_sec_uid == sec_user_id and item_aweme_id:
                    filtered_aweme_list.append(item)
        filtered_count = len(filtered_aweme_list)
        if original_count != filtered_count:
            logger.info(f"第 {page_count} 页原始 {original_count} 个作品，过滤后 {filtered_count} 个")

        for item in filtered_aweme_list:
            if item.get('aweme_id'):
                aweme_ids.append(item['aweme_id'])

        has_more = bool(data.get('has_more'))
        max_cursor = data.get('max_cursor', 0)
        logger.info(f"作者 {author_name}: 第 {page_count} 页有效作品 {filtered_count} 个, 累计 {len(aweme_ids)}, has_more={has_more}")
        await asyncio.sleep(1)

    if not aweme_ids:
        logger.info(f"作者 {author_name} 未检索到任何作品，设 once=true")
        try:
            supabase.table("douyin_user").update({"once": True}).eq("sec_uid", sec_user_id).execute()
        except Exception as e:
            logger.error(f"更新 once=true 出错: {str(e)}")
        return

    logger.info(f"作者 {author_name} 共有 {len(aweme_ids)} 个作品需要处理 (全量)")

    processed = 0
    failed = 0
    for i, aweme_id in enumerate(aweme_ids):
        logger.info(f"[{author_name}] 全量处理作品 {i+1}/{len(aweme_ids)}: {aweme_id}")
        try:
            ok = await process_single_aweme(app, aweme_id, author_info)
            if ok:
                processed += 1
            else:
                failed += 1
        except Exception as e:
            logger.error(f"处理 {aweme_id} 时发生异常: {e}", exc_info=True)
            failed += 1
        await asyncio.sleep(PROCESS_DELAY)

    logger.info(f"[{author_name}] 全量处理完成: 成功 {processed} / 失败 {failed}")
    # once=true
    try:
        supabase.table("douyin_user").update({"once": True}).eq("sec_uid", sec_user_id).execute()
        logger.info(f"作者 {author_name} once 字段更新为 true")
    except Exception as e:
        logger.error(f"更新 once=true 时出错: {str(e)}")

# =============== 下面是新增的"重试任务"部分 ===============
async def retry_file_id_leq_one(app):
    """
    处理「file_id <= 1」条目（file_id是NULL、空字符串、或不含分号），并且 failed != 'not available'
    使用数据库视图 view_retry_needed 进行查询
    """
    logger.info("开始处理 file_id <= 1 的作品条目 (重试)")
    try:
        # 使用视图查询
        response = supabase.from_("view_retry_needed") \
            .select("aweme_id, sec_uid, file_id, failed") \
            .order('create_time', desc=True) \
            .execute()

        tasks = response.data or []
        total_tasks = len(tasks)
        if not tasks:
            logger.info("无符合条件的 file_id <= 1 条目")
            return

        logger.info(f"共有 {total_tasks} 个作品需重试（file_id <= 1）")
        processed_count = 0
        for item in tasks:
            aweme_id = item.get("aweme_id")
            task_sec_uid = item.get("sec_uid")
            if not aweme_id:
                continue

            file_id_val = item.get("file_id", "")
            logger.info(f"重试作品 {aweme_id}, file_id={file_id_val!r} (第 {processed_count+1}/{total_tasks})")

            try:
                author_info = {"sec_uid": task_sec_uid} if task_sec_uid else None
                ok = await process_single_aweme(app, aweme_id, author_info)
                if ok:
                    logger.info(f"作品 {aweme_id} 重试成功 (file_id <= 1)")
                else:
                    logger.warning(f"作品 {aweme_id} 重试未成功 (file_id <= 1)")
            except Exception as e:
                logger.error(f"重试作品 {aweme_id} 时发生异常: {e}", exc_info=True)
            processed_count += 1
            await asyncio.sleep(PROCESS_DELAY)

    except Exception as e:
        logger.error(f"处理 file_id <= 1 重试过程出错: {e}", exc_info=True)

async def retry_failed_not_na(app):
    """
    处理「failed 不为空且 != 'not available'」的作品重试
    """
    logger.info("开始处理 failed != 'not available' 的作品条目 (重试)")
    try:
        response = supabase.table("douyin") \
                           .select("aweme_id, sec_uid, failed") \
                           .not_.is_("failed", "null") \
                           .neq("failed", "not available") \
                           .order('create_time', desc=True) \
                           .execute()

        tasks = response.data or []
        total_tasks = len(tasks)
        if not tasks:
            logger.info("没有标记为 failed (非 not available) 的条目")
            return

        logger.info(f"找到 {total_tasks} 个作品，需重新处理 (failed 重试)")
        processed_count = 0
        for item in tasks:
            aweme_id = item.get("aweme_id")
            sec_uid = item.get("sec_uid")
            if not aweme_id:
                continue

            logger.info(f"重新处理 failed 作品: {aweme_id} (第 {processed_count+1}/{total_tasks})")
            try:
                author_info = {"sec_uid": sec_uid} if sec_uid else None
                ok = await process_single_aweme(app, aweme_id, author_info)
                if ok:
                    logger.info(f"Failed 作品 {aweme_id} 已成功重试")
                else:
                    logger.warning(f"Failed 作品 {aweme_id} 重试后仍未成功")
            except Exception as e:
                logger.error(f"重试作品 {aweme_id} 时出现异常: {e}", exc_info=True)

            processed_count += 1
            await asyncio.sleep(PROCESS_DELAY)
    except Exception as e:
        logger.error(f"处理 failed (!= not available) 重试过程出错: {e}", exc_info=True)


# =============== 程序主入口 ===============
async def main():
    """
    for.py 入口：
    1) 使用 BOT_TOKEN_SECONDARY 登录一个 Bot
    2) 获取 douyin_user 表中 surveillance=true 且 once=false 的作者列表
    3) 若有作者 => 全量扫描
       若无作者 => 依次处理 file_id <=1 重试 和 failed != 'not available' 重试
    4) 休眠后重复
    """
    logger.info("===== for.py 全量扫描程序启动 =====")

    app = Client(
        "douyin_fullscan_for",
        api_id=API_ID,
        api_hash=API_HASH,
        bot_token=BOT_TOKEN
    )

    await app.start()
    try:
        me = await app.get_me()
        logger.info(f"for.py 已登录 Bot: @{me.username}")
    except:
        logger.error("无法 get_me()，请检查 BOT_TOKEN_SECONDARY 是否正确")
        await app.stop()
        return

    # 设置循环间隔(秒)
    LOOP_INTERVAL = int(os.getenv('LOOP_INTERVAL', '300'))
    logger.info(f"程序将循环运行，每次扫描之间休眠 {LOOP_INTERVAL} 秒")

    try:
        while True:
            logger.info("开始查找 once=false 的作者...")
            try:
                resp = supabase.table("douyin_user").select("*") \
                    .eq("surveillance", True) \
                    .eq("once", False) \
                    .execute()
                authors = resp.data or []
                if not authors:
                    logger.info("没有作者需要全量扫描，先处理 file_id<=1 与 failed!=not available 的重试任务...")
                    await retry_file_id_leq_one(app)
                    await retry_failed_not_na(app)
                else:
                    logger.info(f"发现 {len(authors)} 个作者需要全量扫描")
                    for author in authors:
                        try:
                            await process_author_works(app, author)
                        except Exception as e:
                            logger.error(f"作者 {author.get('nickname') or author.get('sec_uid')} 处理出错: {e}", exc_info=True)
                        await asyncio.sleep(3)

            except Exception as e:
                logger.error(f"查询作者失败: {e}", exc_info=True)

            logger.info(f"本轮扫描/重试完成，休眠 {LOOP_INTERVAL} 秒后继续...")
            await asyncio.sleep(LOOP_INTERVAL)

    except KeyboardInterrupt:
        logger.info("检测到 Ctrl+C，程序准备退出...")
    finally:
        await app.stop()
        logger.info("===== for.py 全量扫描程序结束 =====")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("检测到 Ctrl+C，程序准备退出...")
