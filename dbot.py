# bot.py - 抖音下载 Telegram Bot
import os
import re
import time
import asyncio
import logging
import shutil
import json
import requests
from uuid import uuid4
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import zipfile
import subprocess

from pyrogram import Client as PyroClient, filters
from pyrogram.enums import ParseMode
from pyrogram.types import (
    CallbackQuery,
    Message,
    InlineKeyboardMarkup,
    InlineKeyboardButton,
    InputMediaPhoto,
    InputMediaVideo,
    InputMediaAudio,
    InputMediaDocument
)

from supabase import create_client, Client as SupabaseClient

# =============== 日志配置 ===============
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("douyin_bot")

# =============== Supabase 配置 ===============
SUPABASE_URL = "https://wjanjmsywbydjbfrdkaz.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndqYW5qbXN5d2J5ZGpiZnJka2F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAxMjc1OTEsImV4cCI6MjAzNTcwMzU5MX0.ny-61EeDBsVRUr6BdWiamt9oV-6z2TC_f01FJff-9fE"
supabase: SupabaseClient = create_client(SUPABASE_URL, SUPABASE_KEY)

# =============== 全局变量 ===============
DELETE_FILES = True
AUTHOR_SEC_UID_MAP = {}
RETRY_CACHE = {}
MUSIC_CACHE = {}

# =============== 抖音解析器 ===============
@dataclass
class DouyinParser:
    @staticmethod
    def _sort_bitrate_items(bit_rate_list: List[Dict]) -> List[Dict]:
        """
        新的排序逻辑：
        1) 先根据视频分辨率（play_addr 中 width*height）降序排序
        2) 再根据 FPS 降序排序
        3) 最后根据 bit_rate 降序排序
        """
        def get_priority(item: Dict) -> Tuple[int, int, int]:
            play_addr = item.get("play_addr", {})
            width = play_addr.get("width", 0)
            height = play_addr.get("height", 0)
            resolution = width * height  # 分辨率面积
            fps = item.get("FPS", 0)
            bitrate = item.get("bit_rate", 0)
            return (resolution, fps, bitrate)
        sorted_list = sorted(bit_rate_list, key=get_priority, reverse=True)
        return sorted_list

    @staticmethod
    def _get_first_url(url_data: Dict) -> str:
        url_list = url_data.get('url_list', [])
        if isinstance(url_list, list) and url_list:
            # 如果需要判断是否带 watermark，可以在这里二次校验
            return url_list[0]
        return ""

    @staticmethod
    def _get_best_play_url(video_info: Dict) -> str:
        """
        获取最高质量、无水印的视频播放地址。
        - 优先根据分辨率、帧率、码率综合排序
        - 排除带水印标识的地址（如有需要）
        - 若找不到合适的，fallback到默认 play_addr
        """
        bit_rate_list = video_info.get("bit_rate", [])
        if bit_rate_list:
            # 根据自定义规则排序
            sorted_items = DouyinParser._sort_bitrate_items(bit_rate_list)
            for item in sorted_items:
                play_addr = item.get("play_addr", {})
                best_url = DouyinParser._get_first_url(play_addr)
                # 如果地址里有 watermark 标识，可在此处排除
                if best_url and "watermark" not in best_url:
                    return best_url

        # 兜底：使用默认的 play_addr
        fallback_url = DouyinParser._get_first_url(video_info.get("play_addr", {}))
        if fallback_url and "watermark" not in fallback_url:
            return fallback_url
        # 如果都没有，只能返回 fallback_url
        return fallback_url

    @staticmethod
    def _get_best_image_url(img_data: Dict) -> str:
        """
        获取最佳图片URL（无水印 + 尽量高分辨率）
        """
        url_list = img_data.get("url_list", [])
        width = img_data.get("width", 0)
        height = img_data.get("height", 0)
        # 优先选取不包含 "water" 字样的 URL
        no_water_urls = [u for u in url_list if "water" not in u]
        if no_water_urls:
            return no_water_urls[0]
        if url_list:
            return url_list[0]
        return ""

    @staticmethod
    def parse_aweme(resp_data: Dict) -> Dict:
        """
        主解析函数，从解析结果中提取基本信息、作者信息、位置信息、统计、音乐及媒体信息
        """
        try:
            data = resp_data.get("data", {})
            if not isinstance(data, dict):
                return {}

            aweme_id = data.get("aweme_id", "")
            desc = data.get("desc", "")
            create_time_ts = data.get("create_time", 0)
            try:
                create_time_str = datetime.fromtimestamp(create_time_ts).strftime("%Y-%m-%d %H:%M:%S")
            except:
                create_time_str = ""

            author = data.get("author", {})
            author_info = {
                "nickname": author.get("nickname", ""),
                "uid": author.get("uid", ""),
                "sec_uid": author.get("sec_uid", ""),
                "unique_id": author.get("unique_id", ""),
                "follower_count": author.get("follower_count", 0),
                "total_favorited": author.get("total_favorited", 0),
            }

            # location
            location_info = {}
            anchor_info = data.get("anchor_info", {})
            try:
                if isinstance(anchor_info.get("extra"), str):
                    extra_data = json.loads(anchor_info["extra"])
                    if isinstance(extra_data, dict):
                        address_info = extra_data.get("address_info", {})
                        location_info = {
                            "province": address_info.get("province", ""),
                            "city": address_info.get("city", "")
                        }
            except:
                pass

            # statistics
            statistics = data.get("statistics", {})
            stats = {
                "comment_count": statistics.get("comment_count", 0),
                "digg_count": statistics.get("digg_count", 0),
                "collect_count": statistics.get("collect_count", 0),
                "share_count": statistics.get("share_count", 0)
            }

            # music
            music = data.get("music", {})
            music_info = {
                "title": music.get("title", ""),
                "author": music.get("author", ""),
                "play_url": DouyinParser._get_first_url(music.get("play_url", {}))
            }

            # media
            images = data.get("images", [])
            video_info = data.get("video", {})
            duration_ms = data.get("duration", 0)
            duration_s = duration_ms / 1000.0

            if images:
                # 图集
                media_info = {
                    "cover_url": "",  # 图集没有单独的cover
                    "play_url": "",
                    "width": 0,
                    "height": 0,
                    "duration": duration_s,
                    "images": []
                }
                for img in images:
                    img_info = {
                        "url": DouyinParser._get_best_image_url(img),
                        "width": img.get("width", 0),
                        "height": img.get("height", 0),
                        "video": None
                    }
                    vid_data = img.get("video")
                    if vid_data and isinstance(vid_data, dict):
                        best_play_url = DouyinParser._get_best_play_url(vid_data)
                        img_info["video"] = {
                            "url": best_play_url,
                            "width": vid_data.get("width", 0),
                            "height": vid_data.get("height", 0)
                        }
                    media_info["images"].append(img_info)
            else:
                # 普通视频，使用最高码率 & 无水印
                media_info = {
                    "cover_url": DouyinParser._get_first_url(video_info.get("cover", {})),
                    "play_url": DouyinParser._get_best_play_url(video_info),
                    "width": video_info.get("width", 0),
                    "height": video_info.get("height", 0),
                    "duration": duration_s,
                    "images": []
                }

            return {
                "base_info": {
                    "aweme_id": aweme_id,
                    "desc": desc,
                    "create_time": create_time_str
                },
                "author_info": author_info,
                "location_info": location_info,
                "statistics": stats,
                "music_info": music_info,
                "media_info": media_info
            }
        except Exception as e:
            logger.error(f"parse_aweme error: {e}", exc_info=True)
            return {}

# =============== 解析和下载函数 ===============
def parse_douyin_work(resp_data: Dict) -> Dict:
    return DouyinParser.parse_aweme(resp_data)

def call_hybrid_video_data(douyin_url: str) -> Dict[str, Any]:
    try:
        encoded_url = requests.utils.quote(douyin_url, safe='')
        api_url = f'http://localhost:8080/api/hybrid/video_data?url={encoded_url}&minimal=false'
        
        logger.info(f"[call_hybrid_video_data] 原始URL: {douyin_url}")
        logger.info(f"[call_hybrid_video_data] 完整API URL: {api_url}")
        
        r = requests.get(api_url, timeout=30)
        logger.info(f"[call_hybrid_video_data] 状态码: {r.status_code}")
        logger.info(f"[call_hybrid_video_data] 响应内容(前200字): {r.text[:200]}...")
        
        r.raise_for_status()
        return r.json()
    except Exception as e:
        logger.error(f"获取视频信息失败: {str(e)}", exc_info=True)
        return {}

def download_file(url: str, max_retries=3) -> Optional[Dict[str, Any]]:
    """
    通用下载函数；如需避免水印URL，可在外层就过滤掉含 watermark 字样的 url
    """
    for attempt in range(1, max_retries + 1):
        try:
            logger.info(f"[download_file] GET {url}, attempt {attempt}")
            resp = requests.get(url, timeout=30, stream=True)
            resp.raise_for_status()
            content = resp.content
            ct = resp.headers.get("content-type", "")
            return {"binary_data": content, "content_type": ct}
        except Exception as e:
            logger.warning(f"[download_file] Error: {e}", exc_info=True)
            if attempt < max_retries:
                wait_sec = 2 ** attempt
                logger.info(f"等待 {wait_sec} 秒后重试...")
                time.sleep(wait_sec)
    return None

def build_caption_for_single(parsed_data: Dict) -> str:
    base = parsed_data.get("base_info", {})
    author = parsed_data.get("author_info", {})
    stats = parsed_data.get("statistics", {})
    music = parsed_data.get("music_info", {})
    location = parsed_data.get("location_info", {})

    lines = []
    if base.get("aweme_id"):
        lines.append(f"作品ID: {base['aweme_id']}")
    if base.get("desc"):
        lines.append(f"描述: {base['desc']}")
    if base.get("create_time"):
        lines.append(f"发布时间: {base['create_time']}")
    if author.get("nickname"):
        lines.append(f"作者昵称: {author['nickname']}")
    if author.get("unique_id"):
        lines.append(f"抖音号: {author['unique_id']}")
    if author.get("uid"):
        lines.append(f"作者UID: {author['uid']}")

    fc = author.get("follower_count", None)
    tf = author.get("total_favorited", None)
    if fc is not None and tf is not None:
        lines.append(f"粉丝数: {fc} | 获赞: {tf}")

    province = location.get("province", "")
    city = location.get("city", "")
    if province or city:
        lines.append(f"地点: {province} {city}".strip())

    digg = stats.get("digg_count")
    cmt = stats.get("comment_count")
    shr = stats.get("share_count")
    col = stats.get("collect_count")
    stats_parts = []
    if digg is not None:
        stats_parts.append(f"点赞: {digg}")
    if cmt is not None:
        stats_parts.append(f"评论: {cmt}")
    if shr is not None:
        stats_parts.append(f"分享: {shr}")
    if col is not None:
        stats_parts.append(f"收藏: {col}")
    if stats_parts:
        lines.append(" | ".join(stats_parts))

    if music.get("title") and music.get("author"):
        lines.append(f"音乐: {music['title']} - {music['author']}")

    return "\n".join(lines).strip()

# =============== 文件处理函数 ===============
def is_image_file(filename: str) -> bool:
    ext = os.path.splitext(filename.lower())[1]
    return ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp']

def is_video_file(filename: str) -> bool:
    ext = os.path.splitext(filename.lower())[1]
    return ext in ['.mp4', '.mov', '.avi', '.mkv', '.webm']

def is_audio_file(filename: str) -> bool:
    ext = os.path.splitext(filename.lower())[1]
    return ext in ['.mp3', '.wav', '.m4a', '.flac', '.aac']

def get_video_dimensions(video_path: str) -> Tuple[Optional[int], Optional[int]]:
    try:
        cmd = [
            'ffprobe',
            '-v', 'error',
            '-select_streams', 'v:0',
            '-show_entries', 'stream=width,height',
            '-of', 'json',
            video_path
        ]
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            data = json.loads(result.stdout)
            stream = data.get('streams', [{}])[0]
            return stream.get('width'), stream.get('height')
    except:
        pass
    return None, None

def extract_video_thumbnail(video_path: str) -> Optional[str]:
    """从视频中提取缩略图"""
    try:
        thumbnail_path = f"{os.path.splitext(video_path)[0]}_thumb.jpg"
        cmd = [
            'ffmpeg',
            '-i', video_path,
            '-ss', '00:00:01',  # 从视频的1秒处截取
            '-vframes', '1',    # 只截取一帧
            '-q:v', '2',        # 高质量
            thumbnail_path
        ]
        result = subprocess.run(cmd, capture_output=True)
        if result.returncode == 0 and os.path.exists(thumbnail_path) and os.path.getsize(thumbnail_path) > 0:
            return thumbnail_path
    except Exception as e:
        logger.error(f"提取视频缩略图失败: {e}", exc_info=True)
    return None

async def send_single_file(client, chat_id: int, file_path: str, caption: str, reply_markup=None):
    if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
        logger.info(f"文件无效或大小为0: {file_path}")
        return

    if is_image_file(file_path):
        await client.send_photo(chat_id, photo=file_path, caption=caption, reply_markup=reply_markup)
    elif is_video_file(file_path):
        w, h = get_video_dimensions(file_path)
        thumb_path = extract_video_thumbnail(file_path)
        
        try:
            if thumb_path and os.path.exists(thumb_path):
                # 使用生成的缩略图
                await client.send_video(chat_id, video=file_path, caption=caption, 
                                       reply_markup=reply_markup, width=w, height=h, 
                                       thumb=thumb_path)
            else:
                # 无缩略图时的回退方案
                await client.send_video(chat_id, video=file_path, caption=caption, 
                                       reply_markup=reply_markup, width=w, height=h)
        finally:
            # 清理缩略图
            if thumb_path and os.path.exists(thumb_path):
                try:
                    os.remove(thumb_path)
                except:
                    pass
    elif is_audio_file(file_path):
        await client.send_audio(chat_id, audio=file_path, caption=caption, reply_markup=reply_markup)
    else:
        await client.send_document(chat_id, document=file_path, caption=caption, reply_markup=reply_markup)

def build_input_media(path: str):
    if is_image_file(path):
        return InputMediaPhoto(path)
    elif is_video_file(path):
        w, h = get_video_dimensions(path)
        thumb_path = extract_video_thumbnail(path)
        try:
            if thumb_path and os.path.exists(thumb_path):
                # 读取缩略图数据
                with open(thumb_path, "rb") as thumb_file:
                    thumb_data = thumb_file.read()
                # 使用缩略图创建媒体对象
                return InputMediaVideo(path, width=w, height=h, thumb=thumb_data)
            else:
                return InputMediaVideo(path, width=w, height=h)
        finally:
            # 清理缩略图
            if thumb_path and os.path.exists(thumb_path):
                try:
                    os.remove(thumb_path)
                except:
                    pass
    elif is_audio_file(path):
        return InputMediaAudio(path)
    else:
        return InputMediaDocument(path)

def chunk_list(lst, n):
    for i in range(0, len(lst), n):
        yield lst[i:i+n]

# =============== 数据库相关函数 ===============
def get_or_create_user(chat_id: int, username: str = "") -> dict:
    """
    只插入 user_id, username，其他字段(如 send_file, send_any, today, already) 让数据库用默认值
    """
    try:
        result = supabase.table("users2").select("*").eq("user_id", chat_id).execute()
        rows = result.data
        if rows:
            existing_user = rows[0]
            # 如果 username 不一致，更新一下
            if username and existing_user.get("username") != username:
                supabase.table("users2").update({"username": username}).eq("user_id", chat_id).execute()
            return existing_user
        else:
            insert_data = {
                "user_id": chat_id,
                "username": username
            }
            insert_result = supabase.table("users2").insert(insert_data).execute()
            return insert_result.data[0]
    except Exception as e:
        logger.error(f"数据库查询/插入用户失败: {e}", exc_info=True)
        return {
            "user_id": chat_id,
            "username": username
        }

def update_user(chat_id: int, updates: dict):
    try:
        supabase.table("users2").update(updates).eq("user_id", chat_id).execute()
    except Exception as e:
        logger.error(f"更新用户 {chat_id} 信息失败: {e}", exc_info=True)

def save_douyin_to_database(parsed_data: Dict) -> bool:
    """
    将解析后的抖音作品信息保存到数据库
    """
    try:
        base_info = parsed_data.get("base_info", {})
        author_info = parsed_data.get("author_info", {})
        location_info = parsed_data.get("location_info", {})
        music_info = parsed_data.get("music_info", {})
        media_info = parsed_data.get("media_info", {})
        
        # 准备要插入的数据
        data = {
            "aweme_id": base_info.get("aweme_id", ""),
            "description": base_info.get("desc", ""),
            "create_time": base_info.get("create_time", ""),
            "nickname": author_info.get("nickname", ""),
            "uid": author_info.get("uid", ""),
            "sec_uid": author_info.get("sec_uid", ""),
            "unique_id": author_info.get("unique_id", ""),
            "follower_count": author_info.get("follower_count", 0),
            "total_favorited": author_info.get("total_favorited", 0),
            "province": location_info.get("province", ""),
            "city": location_info.get("city", ""),
            "music_title": music_info.get("title", ""),
            "music_author": music_info.get("author", ""),
            "music_play_url": music_info.get("play_url", ""),
            "cover_url": media_info.get("cover_url", ""),
            "media_play_url": media_info.get("play_url", ""),
            "duration": media_info.get("duration", 0)
        }
        
        # 确保数据完整性
        if not data["aweme_id"]:
            logger.error("保存数据库失败: 缺少作品ID")
            return False
            
        # 使用upsert方法保存数据 (如果已存在则更新)
        result = supabase.table("douyin").upsert(data).execute()
        
        if hasattr(result, "error") and result.error:
            logger.error(f"保存数据到Supabase失败: {result.error}")
            return False
            
        logger.info(f"成功保存作品 {data['aweme_id']} 到数据库")
        return True
        
    except Exception as e:
        logger.error(f"保存数据库异常: {str(e)}", exc_info=True)
        return False

# =============== URL解析函数 ===============
def extract_douyin_video_url(text: str) -> str:
    pattern = r"(https?://v\.douyin\.com/\S+|https?://www\.douyin\.com/video/\S+)"
    m = re.search(pattern, text)
    if m:
        return m.group(1).strip()
    return ""

def extract_douyin_user_url(text: str) -> str:
    pattern = r"(https://www\.douyin\.com/user/[^\s]+)"
    m = re.search(pattern, text)
    if m:
        return m.group(1).strip()
    return ""

def extract_sec_user_id_from_user_url(url: str) -> str:
    pattern = r"https://www\.douyin\.com/user/([^/?]+)"
    m = re.search(pattern, url)
    if m:
        return m.group(1)
    return ""

# =============== 媒体发送函数 ===============
async def send_media_files(
    client,
    chat_id: int,
    media_files: List[str],
    douyin_url: str,
    caption_text: str,
    sec_uid: str,
    music_link: str = "",
    from_author_posts=False,
    user_settings=None
):
    """
    批量发送图片/视频，然后如果 user_settings["send_file"] 为 True，再打包ZIP发送。
    """
    if user_settings is None:
        user_settings = {}

    music_key = ""
    if music_link:
        music_key = str(uuid4())[:8]
        MUSIC_CACHE[music_key] = {
            "url": music_link,
            "caption": caption_text
        }

    if from_author_posts:
        if music_key:
            markup = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("打开", url=douyin_url),
                    InlineKeyboardButton("获取音乐", callback_data=f"music_download:{music_key}")
                ]
            ])
        else:
            markup = InlineKeyboardMarkup([
                [InlineKeyboardButton("打开", url=douyin_url)]
            ])
    else:
        if sec_uid:
            short_id = str(uuid4())[:8]
            AUTHOR_SEC_UID_MAP[short_id] = sec_uid
            if music_key:
                markup = InlineKeyboardMarkup([
                    [
                        InlineKeyboardButton("打开", url=douyin_url),
                        InlineKeyboardButton("获取音乐", callback_data=f"music_download:{music_key}"),
                        InlineKeyboardButton("获取全部作品", callback_data=f"author_more:{short_id}")
                    ]
                ])
            else:
                markup = InlineKeyboardMarkup([
                    [
                        InlineKeyboardButton("打开", url=douyin_url),
                        InlineKeyboardButton("获取全部作品", callback_data=f"author_more:{short_id}")
                    ]
                ])
        else:
            if music_key:
                markup = InlineKeyboardMarkup([
                    [
                        InlineKeyboardButton("打开", url=douyin_url),
                        InlineKeyboardButton("获取音乐", callback_data=f"music_download:{music_key}")
                    ]
                ])
            else:
                markup = InlineKeyboardMarkup([
                    [InlineKeyboardButton("打开", url=douyin_url)]
                ])

    valid_files = [f for f in media_files if os.path.exists(f) and os.path.getsize(f) > 0]
    if not valid_files:
        logger.info("无可发送文件。")
        return

    if len(valid_files) == 1:
        await send_single_file(client, chat_id, valid_files[0], caption_text, reply_markup=markup)
    else:
        *initial_files, last_file = valid_files
        for chunk in chunk_list(initial_files, 10):
            input_medias = [build_input_media(fpath) for fpath in chunk]
            await client.send_media_group(chat_id=chat_id, media=input_medias)
        await send_single_file(client, chat_id, last_file, caption_text, reply_markup=markup)

    if user_settings.get("send_file", False):
        try:
            base_name = os.path.basename(valid_files[0]).split('_')[0] or "douyin"
            zip_path = os.path.join(os.path.dirname(valid_files[0]), f"{base_name}.zip")
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file in valid_files:
                    zipf.write(file, os.path.basename(file))

            if os.path.exists(zip_path) and os.path.getsize(zip_path) > 0:
                await client.send_document(chat_id=chat_id, document=zip_path)

            if DELETE_FILES and os.path.exists(zip_path):
                os.remove(zip_path)
        except Exception as e:
            logger.error(f"打包发送ZIP失败: {e}", exc_info=True)

# =============== 抖音API调用函数 ===============
def fetch_user_post_videos_api(sec_user_id: str, max_cursor: int = 0, count: int = 40) -> dict:
    base_url = "http://localhost:8080/api/douyin/web/fetch_user_post_videos"
    params = {
        "sec_user_id": sec_user_id,
        "max_cursor": max_cursor,
        "count": count
    }
    try:
        r = requests.get(base_url, params=params, timeout=30)
        r.raise_for_status()
        return r.json()
    except Exception as e:
        logger.error(f"fetch_user_post_videos_api error: {e}", exc_info=True)
        return {}

def fetch_one_video_api(aweme_id: str) -> dict:
    base_url = "http://localhost:8080/api/hybrid/video_data"
    douyin_url = f"https://www.douyin.com/video/{aweme_id}"
    params = {
        "url": douyin_url,
        "minimal": "false"
    }
    try:
        r = requests.get(base_url, params=params, timeout=30)
        r.raise_for_status()
        return r.json()
    except Exception as e:
        logger.error(f"fetch_one_video_api error: {e}", exc_info=True)
        return {}

async def fetch_author_posts_and_send_all(
    client,
    chat_id: int,
    sec_user_id: str,
    retry_list: Optional[List[str]] = None,
    progress_callback=None,
    user_settings=None
):
    logger.info(f"[fetch_author_posts_and_send_all] sec_user_id={sec_user_id}, retry_list={retry_list}")

    if user_settings is None:
        user_settings = {}

    if retry_list:
        aweme_ids = retry_list
    else:
        if progress_callback:
            progress_callback("正在获取作者作品列表...")
        aweme_ids = []
        max_cursor = 0
        has_more = True
        while has_more:
            resp_json = fetch_user_post_videos_api(sec_user_id, max_cursor=max_cursor, count=40)
            data = resp_json.get("data", {})
            aweme_list = data.get("aweme_list", [])
            for item in aweme_list:
                if isinstance(item, dict):
                    _id = item.get("aweme_id")
                    if _id:
                        aweme_ids.append(_id)
            has_more = data.get("has_more", False)
            max_cursor = data.get("max_cursor", 0)
            logger.info(f"本页获取到 {len(aweme_list)} 个, 累计 {len(aweme_ids)}, has_more={has_more}")
            if progress_callback:
                progress_callback(f"已获取到 {len(aweme_ids)} 个作品...")

    if not aweme_ids:
        return {"ok": False, "msg": "没有找到任何作品。"}

    failed_ids = []
    total = len(aweme_ids)
    processed = 0

    for i, aweme_id in enumerate(aweme_ids, start=1):
        if progress_callback:
            progress_callback(f"正在处理第 {i}/{total} 个作品...\n已发送: {processed}个 | 失败: {len(failed_ids)}个")
        resp_data = fetch_one_video_api(aweme_id)
        parsed_data = parse_douyin_work(resp_data)
        if not parsed_data or not parsed_data.get("base_info", {}).get("aweme_id"):
            failed_ids.append(aweme_id)
            continue
        
        # 保存到数据库
        save_douyin_to_database(parsed_data)
        
        wdir = os.path.join("downloads", f"{chat_id}_{aweme_id}")
        os.makedirs(wdir, exist_ok=True)
        downloaded_files = []
        try:
            caption_text = build_caption_for_single(parsed_data)
            media_info = parsed_data["media_info"]
            images = media_info.get("images", [])
            play_url = media_info.get("play_url", "")
            sec_uid_in_work = parsed_data["author_info"].get("sec_uid", "")
            if images:
                for idx, img_obj in enumerate(images):
                    if progress_callback:
                        progress_callback(f"正在处理第 {i}/{total} 个作品...\n"
                                          f"已发送: {processed}个 | 失败: {len(failed_ids)}个\n"
                                          f"当前: 下载图集第{idx+1}张...")
                    img_url = img_obj.get("url")
                    if img_url:
                        ddata = download_file(img_url)
                        if ddata:
                            fpath = os.path.join(wdir, f"{aweme_id}_{idx}.jpg")
                            with open(fpath, "wb") as f:
                                f.write(ddata["binary_data"])
                            downloaded_files.append(fpath)
                    vid = img_obj.get("video")
                    if vid and vid.get("url"):
                        ddata = download_file(vid["url"])
                        if ddata:
                            vpath = os.path.join(wdir, f"{aweme_id}_{idx}.mp4")
                            with open(vpath, "wb") as f:
                                f.write(ddata["binary_data"])
                            downloaded_files.append(vpath)
            else:
                if play_url:
                    if progress_callback:
                        progress_callback(f"正在处理第 {i}/{total} 个作品...\n"
                                          f"已发送: {processed}个 | 失败: {len(failed_ids)}个\n"
                                          f"当前: 下载视频...")
                    ddata = download_file(play_url)
                    if ddata:
                        fpath = os.path.join(wdir, f"{aweme_id}.mp4")
                        with open(fpath, "wb") as f:
                            f.write(ddata["binary_data"])
                        downloaded_files.append(fpath)
                    else:
                        failed_ids.append(aweme_id)
            if downloaded_files:
                if progress_callback:
                    progress_callback(f"正在处理第 {i}/{total} 个作品...\n"
                                      f"已发送: {processed}个 | 失败: {len(failed_ids)}个\n"
                                      f"当前: 发送中...")
                music_url = parsed_data["music_info"].get("play_url", "")
                await send_media_files(
                    client=client,
                    chat_id=chat_id,
                    media_files=downloaded_files,
                    douyin_url=f"https://www.douyin.com/video/{aweme_id}",
                    caption_text=caption_text,
                    sec_uid=sec_uid_in_work,
                    music_link=music_url,
                    from_author_posts=True,
                    user_settings=user_settings
                )
                processed += 1
            else:
                failed_ids.append(aweme_id)
        finally:
            if DELETE_FILES and os.path.exists(wdir):
                shutil.rmtree(wdir)
    if failed_ids:
        short_id = str(uuid4())[:8]
        RETRY_CACHE[short_id] = {
            "sec_user_id": sec_user_id,
            "videos": failed_ids
        }
        if progress_callback:
            progress_callback(f"全部处理完成！\n成功: {processed}个 | 失败: {len(failed_ids)}个")
        return {"ok": True, "total": total, "failed_num": len(failed_ids), "retry_key": short_id}
    else:
        if progress_callback:
            progress_callback(f"全部处理完成！\n成功: {processed}个 | 失败: 0个")
        return {"ok": True, "total": total, "failed_num": 0}

# =============== 延迟删除消息函数 ===============
async def cleanup_messages(user_msg: Message, process_msg: Message, delay: float = 5.0):
    """
    delay秒后依次删除提示消息和用户输入消息。
    """
    await asyncio.sleep(delay)
    # 尝试删除处理进度消息
    try:
        await process_msg.delete()
    except:
        pass
    # 尝试删除用户的原输入
    try:
        await user_msg.delete()
    except:
        pass

# =============== Bot 配置 ===============
api_id = '25432929'
api_hash = '965c5d22f0b9d1d0326e84bbb2bb18c1'
bot_token = '7963921117:AAEqRwM8kIhm1IjgFFfKx07hL171PoJ0Ayo'

app = PyroClient(
    "my_douyin_bot",
    api_id=api_id,
    api_hash=api_hash,
    bot_token=bot_token
)

# =============== 回调按钮处理 ===============
@app.on_callback_query()
async def handle_callback_query(client: PyroClient, callback_query: CallbackQuery):
    chat_id = callback_query.message.chat.id
    from_username = callback_query.from_user.username or ""
    user_record = get_or_create_user(chat_id, username=from_username)

    # 可能数据库里没该字段时，给默认
    send_any = user_record.get("send_any", True)
    today_limit = user_record.get("today", 10)
    already_used = user_record.get("already", 0)
    send_file = user_record.get("send_file", False)

    if not send_any or already_used >= today_limit:
        await callback_query.answer("你已达到每日配额上限，使用量将在下个23:00清空。", show_alert=True)
        return

    data = callback_query.data

    # ------ 获取更多作者作品 ------
    if data.startswith("author_more:"):
        # 新增判断：如果免费用户（fetchall==0），提示premium信息
        if user_record.get("fetchall", 0) == 0:
            premium_msg = '您好，获取全部作品功能已向premium用户开放，获取premium请联系<a href="https://t.me/juaer">@juaer</a>，premium用户每月可处理1000条作品链接并有15次获取全部作品按钮的使用机会。'
            await callback_query.message.reply_text(premium_msg, parse_mode=ParseMode.HTML)
            return

        short_id = data.split(":", 1)[1]
        sec_user_id = AUTHOR_SEC_UID_MAP.get(short_id)
        if not sec_user_id:
            await callback_query.answer("无效sec_user_id", show_alert=True)
            return

        await callback_query.answer("开始获取作者全部作品...", show_alert=False)

        async def progress_cb(info):
            # 以 Toast 形式更新进度
            try:
                await callback_query.answer(info, show_alert=False)
            except:
                pass

        # 将用户的send_file等传下去
        user_settings = {
            "send_file": send_file
        }
        result = await fetch_author_posts_and_send_all(
            client,
            chat_id,
            sec_user_id,
            progress_callback=lambda msg: asyncio.create_task(progress_cb(msg)),
            user_settings=user_settings
        )

        if not result.get("ok"):
            msg = result.get("msg", "获取失败")
            await callback_query.message.reply_text(f"发生错误: {msg}")
        else:
            total = result.get("total", 0)
            failed_num = result.get("failed_num", 0)
            if failed_num > 0:
                retry_key = result.get("retry_key")
                btn = InlineKeyboardButton("重试失败作品", callback_data=f"retry_failed:{retry_key}")
                markup = InlineKeyboardMarkup([[btn]])
                await callback_query.message.reply_text(
                    f"作品发送完毕！共处理 {total} 个，其中 {failed_num} 个失败。",
                    reply_markup=markup
                )
            else:
                await callback_query.message.reply_text(f"作品发送完毕！共处理 {total} 个。")

        # 更新配额
        update_user(chat_id, {"already": already_used + 1})

    # ------ 下载音乐 ------
    elif data.startswith("music_download:"):
        music_key = data.split(":", 1)[1]
        music_info = MUSIC_CACHE.get(music_key)
        if not music_info:
            await callback_query.answer("音乐链接已失效或不存在。", show_alert=True)
            return

        if not send_any or already_used >= today_limit:
            update_user(chat_id, {"send_any": False})
            await callback_query.answer("你已达到每日配额上限。", show_alert=True)
            return

        await callback_query.answer("正在下载音乐...", show_alert=False)

        music_url = music_info["url"]
        caption_text = music_info["caption"]
        
        # 提取音乐标题
        music_title = ""
        music_author = ""
        for line in caption_text.split('\n'):
            if line.startswith("音乐:"):
                try:
                    parts = line.split("音乐:", 1)[1].strip().split(" - ", 1)
                    music_title = parts[0]
                    if len(parts) > 1:
                        music_author = parts[1]
                except:
                    pass
        if not music_title:
            music_title = f"music_{music_key}"

        ddata = download_file(music_url)
        if not ddata:
            await callback_query.message.reply_text("音乐下载失败或已失效。")
            return

        ct = ddata.get("content_type", "").lower()
        bdata = ddata["binary_data"]

        dpath = os.path.join("downloads", f"music_{music_key}")
        os.makedirs(dpath, exist_ok=True)
        ext = ".mp3"
        if "aac" in ct:
            ext = ".aac"
        elif "m4a" in ct:
            ext = ".m4a"
        elif "wav" in ct:
            ext = ".wav"
        elif "mpeg" in ct:
            ext = ".mp3"

        safe_filename = re.sub(r'[<>:"/\\|?*]', '_', f"{music_title} - {music_author}".strip())
        music_file = os.path.join(dpath, f"{safe_filename}{ext}")
        with open(music_file, "wb") as f:
            f.write(bdata)

        await client.send_audio(chat_id, audio=music_file)

        update_user(chat_id, {"already": already_used + 1})
        if os.path.exists(dpath):
            shutil.rmtree(dpath)

    # ------ 重试失败的作品 ------
    elif data.startswith("retry_failed:"):
        retry_key = data.split(":", 1)[1]
        retry_info = RETRY_CACHE.get(retry_key)
        if not retry_info:
            await callback_query.answer("重试信息已过期", show_alert=True)
            return
        
        if not send_any or already_used >= today_limit:
            update_user(chat_id, {"send_any": False})
            await callback_query.answer("你已达到每日配额上限。", show_alert=True)
            return

        sec_user_id = retry_info["sec_user_id"]
        failed_ids = retry_info["videos"]

        await callback_query.answer("开始重试失败的作品...", show_alert=False)

        async def progress_cb(info):
            try:
                await callback_query.answer(info, show_alert=False)
            except:
                pass

        user_settings = {
            "send_file": send_file
        }
        result = await fetch_author_posts_and_send_all(
            client,
            chat_id,
            sec_user_id,
            retry_list=failed_ids,
            progress_callback=lambda msg: asyncio.create_task(progress_cb(msg)),
            user_settings=user_settings
        )
        RETRY_CACHE.pop(retry_key, None)

        if result.get("ok"):
            fn = result.get("failed_num", 0)
            tot = result.get("total", len(failed_ids))
            if fn > 0:
                await callback_query.message.reply_text(f"重试完成，处理 {tot} 个作品，仍有 {fn} 个失败。")
            else:
                await callback_query.message.reply_text(f"重试完成，共处理 {tot} 个，全部成功。")
        else:
            msg = result.get("msg", "重试失败")
            await callback_query.message.reply_text(f"重试出错: {msg}")

        update_user(chat_id, {"already": already_used + 1})

    else:
        await callback_query.answer("未知操作", show_alert=True)

# =============== 命令处理 ===============
@app.on_message(filters.command(["start"]))
async def start_command(client, message: Message):
    chat_id = message.chat.id
    username = message.from_user.username or ""
    user_record = get_or_create_user(chat_id, username=username)

    welcome_text = (
        f"👋 你好！这是抖音下载Bot。\n"
        f"你的 Chat ID: <code>{chat_id}</code>\n"
        f"当前数据库记录的用户名: @{user_record.get('username', '')}"
    )
    await message.reply_text(welcome_text, parse_mode=ParseMode.HTML)

@app.on_message(filters.command(["help"]))
async def help_command(client, message: Message):
    chat_id = message.chat.id
    user_record = get_or_create_user(chat_id, username=message.from_user.username or "")
    if not user_record.get("send_any", True):
        return

    await message.reply_text(
        "【使用指南】\n"
        "1) 直接发送抖音作品链接(视频/图集)给我。\n"
        "2) 或发送作者主页链接 (https://www.douyin.com/user/xxxx)，获取全部作品。\n"
        "3) 如看到「获取音乐」按钮，可点击获取背景音乐。\n"
        "4) 如果数据库中 send_file=true，则会额外打包原文件ZIP给你。\n"
        "5) 若超过每日配额，会被提示明日再来~"
    )

@app.on_message(filters.command(["id"]))
async def id_command(client, message: Message):
    chat_id = message.chat.id
    username = message.from_user.username or ""
    user_record = get_or_create_user(chat_id, username=username)

    await message.reply_text(
        f"你的 Chat ID: <code>{chat_id}</code>\n"
        f"数据库中的用户名: @{user_record.get('username', '')}",
        parse_mode=ParseMode.HTML
    )

@app.on_message(filters.command(["premium"]))
async def premium_command(client, message: Message):
    premium_msg = '您好，获取全部作品功能已向premium用户开放，获取premium请联系<a href="https://t.me/juaer">@juaer</a>，premium用户每月可处理1000条作品链接并有15次获取全部作品按钮的使用机会。'
    await message.reply_text(premium_msg, parse_mode=ParseMode.HTML)

# =============== 处理文本消息：抖音链接 ===============
@app.on_message(filters.text & ~filters.command(["start", "help", "id"]))
async def handle_text_message(client: PyroClient, message: Message):
    try:
        chat_id = message.chat.id
        from_username = message.from_user.username or ""
        user_record = get_or_create_user(chat_id, from_username)

        send_any = user_record.get("send_any", True)
        today_limit = user_record.get("today", 10)
        already_used = user_record.get("already", 0)
        send_file = user_record.get("send_file", False)

        if not send_any or already_used >= today_limit:
            update_user(chat_id, {"send_any": False})
            await message.reply_text("你已达到每日配额上限，使用量将在下一个23:00清空。")
            return

        text = message.text.strip()
        if not text:
            return

        # 1) 如果是作者主页链接
        user_url = extract_douyin_user_url(text)
        if user_url:
            sec_user_id = extract_sec_user_id_from_user_url(user_url)
            if not sec_user_id:
                await message.reply_text("无法从链接中提取sec_user_id")
                return

            # 新增判断：如果免费用户（fetchall==0），提示premium信息
            if user_record.get("fetchall", 0) == 0:
                premium_msg = '您好，获取全部作品功能已向premium用户开放，获取premium请联系<a href="https://t.me/juaer">@juaer</a>，premium用户每月可处理1000条作品链接并有15次获取全部作品按钮的使用机会。'
                await message.reply_text(premium_msg, parse_mode=ParseMode.HTML)
                return

            processing_msg = await message.reply_text("正在获取作者全部作品，请稍候...")

            async def progress_cb(info):
                try:
                    await processing_msg.edit_text(info)
                except:
                    pass

            user_settings = {
                "send_file": send_file
            }
            result = await fetch_author_posts_and_send_all(
                client,
                chat_id,
                sec_user_id,
                progress_callback=lambda msg: asyncio.create_task(progress_cb(f"进度: {msg}")),
                user_settings=user_settings
            )

            if not result.get("ok"):
                msg = result.get("msg", "获取失败")
                await processing_msg.edit_text(f"错误: {msg}")
            else:
                tot = result.get("total", 0)
                fn = result.get("failed_num", 0)
                if fn > 0:
                    rk = result.get("retry_key")
                    btn = InlineKeyboardButton("重试失败作品", callback_data=f"retry_failed:{rk}")
                    markup = InlineKeyboardMarkup([[btn]])
                    await processing_msg.edit_text(
                        f"作品发送完毕！共处理 {tot} 个，其中 {fn} 个失败。",
                        reply_markup=markup
                    )
                else:
                    await processing_msg.edit_text(f"作品发送完毕！共处理 {tot} 个。")

            update_user(chat_id, {"already": already_used + 1})

            await cleanup_messages(message, processing_msg, delay=5.0)
            return

        # 2) 如果是单作品链接
        douyin_url = extract_douyin_video_url(text)
        if douyin_url:
            processing_msg = await message.reply_text("正在解析...")

            resp_data = call_hybrid_video_data(douyin_url)
            parsed_data = parse_douyin_work(resp_data)
            if not parsed_data or not parsed_data.get("base_info", {}).get("aweme_id"):
                await processing_msg.edit_text("解析失败，链接无效或视频无法获取。")
                await cleanup_messages(message, processing_msg, delay=5.0)
                return
                
            # 保存到数据库
            save_douyin_to_database(parsed_data)

            caption_text = build_caption_for_single(parsed_data)
            sec_uid = parsed_data["author_info"].get("sec_uid", "")
            aweme_id = parsed_data["base_info"]["aweme_id"]

            wdir = os.path.join("downloads", f"single_{chat_id}_{aweme_id}")
            os.makedirs(wdir, exist_ok=True)
            downloaded_files = []

            try:
                media_info = parsed_data["media_info"]
                images = media_info.get("images", [])
                play_url = media_info.get("play_url", "")

                if images:
                    await processing_msg.edit_text("检测到图集，正在下载...")
                    for idx, img_obj in enumerate(images):
                        img_url = img_obj.get("url")
                        if img_url:
                            ddata = download_file(img_url)
                            if ddata:
                                fpath = os.path.join(wdir, f"{aweme_id}_{idx}.jpg")
                                with open(fpath, "wb") as f:
                                    f.write(ddata["binary_data"])
                                downloaded_files.append(fpath)
                        vid = img_obj.get("video")
                        if vid and vid.get("url"):
                            ddata = download_file(vid["url"])
                            if ddata:
                                vpath = os.path.join(wdir, f"{aweme_id}_{idx}.mp4")
                                with open(vpath, "wb") as f:
                                    f.write(ddata["binary_data"])
                                downloaded_files.append(vpath)
                else:
                    if play_url:
                        await processing_msg.edit_text("检测到单视频，正在下载...")
                        ddata = download_file(play_url)
                        if ddata:
                            fpath = os.path.join(wdir, f"{aweme_id}.mp4")
                            with open(fpath, "wb") as f:
                                f.write(ddata["binary_data"])
                            downloaded_files.append(fpath)

                if downloaded_files:
                    music_url = parsed_data["music_info"].get("play_url", "")
                    user_settings = {"send_file": send_file}
                    await send_media_files(
                        client=client,
                        chat_id=chat_id,
                        media_files=downloaded_files,
                        douyin_url=douyin_url,
                        caption_text=caption_text,
                        sec_uid=sec_uid,
                        music_link=music_url,
                        from_author_posts=False,
                        user_settings=user_settings
                    )
                    await processing_msg.edit_text("发送完成。")
                    update_user(chat_id, {"already": already_used + 1})
                else:
                    await processing_msg.edit_text("下载失败或没有可下载的媒体。")
            finally:
                if os.path.exists(wdir):
                    shutil.rmtree(wdir)

            await cleanup_messages(message, processing_msg, delay=5.0)
            return

        await message.reply_text("未检测到有效的抖音链接。")

    except Exception as e:
        logger.error(f"处理文本消息出错: {e}", exc_info=True)
        await message.reply_text(f"❌ 出错: {e}")

# =============== 入口 ===============
def main():
    logger.info("Starting douyin_bot...")
    app.run()

if __name__ == "__main__":
    main()