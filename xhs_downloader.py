import requests
import json
import os
import hashlib
from urllib.parse import urlparse, parse_qs
from pathlib import Path

class XHSDownloader:
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',
            'Referer': 'https://www.xiaohongshu.com',
            'Cookie': 'web_session=030037a0cf1f7f262b68470d84204a3151f954;acw_tc=0a00dbf117445875281751033ef019b18c6fe15f161235a3532a623efd9934;abRequestId=d064e2d7-f19e-5caa-82e7-266dff9a94a5;webBuild=4.47.1;xsecappid=xhs-pc-web;a1=19631851712axFYd3M5UWwjyORFXJNrWEV4G2oonz50000294141;webId=68f9d2120d4121f229442867dc53f076;sec_poison_id=b4b17e98-123c-4df0-add3-06c0df23ca45',
            'xsecappid': 'xhs-pc-web',
            'X-B3-TraceId': f'{os.urandom(16).hex()}',
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Sec-Ch-Ua': '"Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"macOS"',
            'Sec-Fetch-Dest': 'image',
            'Sec-Fetch-Mode': 'no-cors',
            'Sec-Fetch-Site': 'cross-site'
        }

    def download_image(self, url, save_dir='.'):
        try:
            Path(save_dir).mkdir(parents=True, exist_ok=True)

            parsed_url = urlparse(url)
            path_parts = [part for part in parsed_url.path.split('/') if part]
            if path_parts:
                filename_base = path_parts[-1]
            else:
                filename_base = hashlib.md5(url.encode()).hexdigest()

            filename_base = filename_base.split('?')[0]

            print(f"尝试下载: {url}")
            current_headers = self.headers.copy()
            current_headers['X-B3-TraceId'] = f'{os.urandom(16).hex()}'

            response = requests.get(url, headers=current_headers, timeout=20)
            response.raise_for_status()

            content_type = response.headers.get('Content-Type')
            file_extension = '.jpg'
            if content_type:
                print(f"服务器返回 Content-Type: {content_type}")
                if 'image/jpeg' in content_type: file_extension = '.jpg'
                elif 'image/png' in content_type: file_extension = '.png'
                elif 'image/gif' in content_type: file_extension = '.gif'
                elif 'image/webp' in content_type: file_extension = '.webp'
                elif 'image/avif' in content_type: file_extension = '.avif'
                elif 'image/bmp' in content_type: file_extension = '.bmp'
                elif 'image/tiff' in content_type: file_extension = '.tiff'
            else:
                print("警告：服务器未返回 Content-Type 或无法识别，将使用默认 .jpg 扩展名。")

            filename = os.path.join(save_dir, filename_base + file_extension)

            counter = 1
            original_filename = filename
            while os.path.exists(filename):
                name, ext = os.path.splitext(original_filename)
                filename = f"{name}_{counter}{ext}"
                counter += 1

            with open(filename, 'wb') as f:
                f.write(response.content)
            print(f'成功下载: {filename}')
            file_size = os.path.getsize(filename)
            print(f"文件大小: {file_size} 字节")
            if file_size < 1000:
                print("警告：下载的文件非常小，可能不是预期的图片文件。请检查文件内容。")
            return True
        except requests.exceptions.RequestException as e:
            print(f'下载失败 (请求错误) {url}: {str(e)}')
            if hasattr(e, 'response') and e.response is not None:
                print(f"响应状态码: {e.response.status_code}")
                try:
                    print(f"响应内容预览: {e.response.text[:500]}...")
                except Exception:
                    print("无法预览响应内容。")
            return False
        except Exception as e:
            print(f'下载失败 (其他错误) {url}: {str(e)}')
            return False

def main():
    downloader = XHSDownloader()
    target_url = "http://sns-na-i4.xhscdn.com/notes_pre_post/1040g3k031g6189j116005o635tc09cvgh1pjaf8?imageView2/2/w/5000/h/5000/format/reif/q/99&redImage/frame/0&ap=1&sc=ORIGINAL"

    print(f"开始下载指定链接: {target_url}")
    print("使用的 Headers (部分):")
    print(f"  User-Agent: {downloader.headers.get('User-Agent')}")
    print(f"  Referer: {downloader.headers.get('Referer')}")
    print(f"  Cookie: {downloader.headers.get('Cookie', '未设置')[:50]}...")

    if downloader.download_image(target_url):
        print('\n指定链接下载任务完成，请检查文件。')
    else:
        print('\n指定链接下载失败。')

if __name__ == '__main__':
    main() 