# -*- coding: utf-8 -*-

"""
bilibot_combined.py

在原有基础上进行了如下改动：
1. 只有当用户发送 /start 命令后才开始监听收藏夹；
2. /reset 命令可清空所有进度记录并从头开始；
3. /check 命令分成多条消息发送：先发送收藏夹状态，再分批发送失败的 bvid 列表（无错误原因）；
4. 任何时候只允许一个下载任务并发；
5. 若监听过程中用户又发送 BV 链接，则暂停监听，先处理用户链接任务，再继续监听；
6. 错误内容不再包括具体错误原因，只显示对应的 bvid。
"""

import os
import re
import json
import time
import math
import shutil
import asyncio
import requests
import subprocess
import concurrent.futures
import logging

from urllib.parse import urlparse
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass
from typing import List, Dict, Optional, Tuple
from uuid import uuid4

# Pyrogram 相关
from pyrogram import Client as PyroClient, filters
from pyrogram.enums import ParseMode
from pyrogram.types import (
    CallbackQuery,
    Message,
    InlineKeyboardMarkup,
    InlineKeyboardButton
)
from pyrogram.errors import FloodWait

# ========== 全局配置区域 ==========
api_id = '25432929'  # 请根据实际情况修改
api_hash = '965c5d22f0b9d1d0326e84bbb2bb18c1'  # 请根据实际情况修改
bot_token = '8006255931:AAFtMEamuaou_j73ExUhDv-aceMYpmoaT4M'  # 请根据实际情况修改

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 上传进度回调（简单打印）
def progress_callback(current, total):
    print(f"Uploading: {current / total * 100:.2f}%")


# ========== 1) bdown.py: 下载器及下载结果定义 ==========

@dataclass
class DownloadResult:
    bvid: str
    cid: str
    file_path: str
    success: bool
    error: Optional[str] = None


class AsyncBiliDownloader:
    """
    异步下载 B 站视频。
    1. 获取分P信息
    2. 获取播放信息 (DASH流)
    3. 根据清晰度&编码优先级选择最优流
    4. 分段并发下载 + FFmpeg合并
    """

    headers = {
        "User-Agent": ("Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                       "AppleWebKit/537.36 (KHTML, like Gecko) "
                       "Chrome/91.0.4472.124 Safari/537.36"),
        "Referer": "https://www.bilibili.com",
        # 下面的 Cookie 仅作示例，请自行替换为可用值
        "Cookie": (
            "buvid3=D92C01E2-A008-7ACA-D0A1-E2A7EBFAE6E484047infoc; b_nut=1728225384; _uuid=3DD69A1F-1C67-3B7E-98F3-2AB5C594AAD484584infoc; buvid_fp=f6751bde8a0c31a18cd7860cca8c7b8d; enable_web_push=DISABLE; home_feed_column=5; browser_resolution=1800-576; buvid4=903F7FA1-116A-0103-8942-CE956A797A6585260-024100614-CXx%2BSNP8kSYbEF7Wp%2BGj8g%3D%3D; PVID=2; LIVE_BUVID=AUTO2117282253876564; fingerprint=f6751bde8a0c31a18cd7860cca8c7b8d; buvid_fp_plain=undefined; SESSDATA=743a1f40%2C1750956870%2Cbe24a%2Ac2CjCXTGVXW2zeRMgSaf-kdnstaCsrRa2oIM1y0nxI-ejMwuhqkTS-uoyCrOw27mu9iywSVnU2QlJPTlgyeGdkUTFONmJtdV9FWjE0RjJuOVBhWmQzUWJobjBOaGM4WGtxbWY0M2dwRlQ0bDR5bEFyTWNJNlhWc094Ymt2MTVyb2FTTi1qbXRHNUJnIIEC; bili_jct=37b458961583e7e5d64087952ca265a7; DedeUserID=12954775; DedeUserID__ckMd5=7730e80f8c2b89f9; header_theme_version=CLOSE; CURRENT_FNVAL=4048; rpdid=|(um|)m)Jmll0J'u~JlR|l||l; bili_ticket=eyJhbGciOiJIUzI1NiIsImtpZCI6InMwMyIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NDExNjY0MjAsImlhdCI6MTc0MDkwNzE2MCwicGx0IjotMX0.D49tlDZRREW6S92aD9aSoL6CgzVWUfZoWBec6gRTCAk; bili_ticket_expires=1741166360; b_lsid=B2B210BB3_19556275021"

        )
    }

    # 优先匹配 codec: hev1 > avc1 > av01
    quality_priorities = {
        127: ['hev1', 'avc1', 'av01'],  # 8K
        126: ['hev1', 'avc1', 'av01'],  # 杜比视界
        125: ['hev1', 'avc1', 'av01'],  # HDR真彩
        120: ['hev1', 'avc1', 'av01'],  # 4K
        116: ['hev1', 'avc1', 'av01'],  # 1080P60
        112: ['hev1', 'avc1', 'av01'],  # 1080P+
        80:  ['hev1', 'avc1', 'av01'],  # 1080P
        64:  ['hev1', 'avc1', 'av01'],  # 720P
        32:  ['hev1', 'avc1', 'av01'],  # 480P
        16:  ['hev1', 'avc1', 'av01']   # 360P
    }

    quality_names = {
        127: "8K",
        126: "杜比视界",
        125: "HDR真彩",
        120: "4K",
        116: "1080P60",
        112: "1080P+",
        80:  "1080P",
        64:  "720P",
        32:  "480P",
        16:  "360P"
    }

    def __init__(self):
        self.download_path = Path("downloads")
        self.download_path.mkdir(exist_ok=True)

    @staticmethod
    def set_cookie(cookie: str):
        AsyncBiliDownloader.headers["Cookie"] = cookie

    async def get_video_parts(self, bvid: str) -> List[Dict]:
        url = "https://api.bilibili.com/x/web-interface/view"
        params = {"bvid": bvid}

        for attempt in range(3):
            try:
                response = await asyncio.get_event_loop().run_in_executor(
                    None, lambda: requests.get(url, params=params, headers=self.headers, timeout=10)
                )
                response.raise_for_status()
                data = response.json()
                if data["code"] != 0:
                    raise ValueError(f"API error: {data['message']}")
                pages = data["data"]["pages"]
                return pages
            except Exception as e:
                logger.warning(f"[get_video_parts] 重试中... {e}")
                await asyncio.sleep(1)
        raise RuntimeError(f"Failed to get video parts for {bvid}")

    async def get_play_info(self, bvid: str, cid: str) -> dict:
        url = "https://api.bilibili.com/x/player/wbi/playurl"
        params = {
            "bvid": bvid,
            "cid": cid,
            "qn": 127,
            "fnval": 4048,
            "fnver": 0,
            "fourk": 1
        }
        for attempt in range(3):
            try:
                response = await asyncio.get_event_loop().run_in_executor(
                    None, lambda: requests.get(url, params=params, headers=self.headers, timeout=10)
                )
                response.raise_for_status()
                data = response.json()
                if data["code"] != 0:
                    raise ValueError(f"API error: {data['message']}")
                return data
            except Exception as e:
                logger.warning(f"[get_play_info] 重试中... {e}")
                await asyncio.sleep(1)
        raise RuntimeError(f"Failed to get play info for bvid={bvid}, cid={cid}")

    def find_best_stream(self, play_info: dict) -> Tuple[Optional[dict], Optional[dict]]:
        dash_data = play_info["data"].get("dash", None)
        if not dash_data:
            return None, None

        video_streams = dash_data.get("video", [])
        video_streams = sorted(video_streams, key=lambda x: x.get("bandwidth", 0), reverse=True)

        video_stream = None
        for qn in sorted(self.quality_priorities.keys(), reverse=True):
            candidate = [v for v in video_streams if v["id"] == qn]
            if candidate:
                # 按 codec 优先级匹配
                for codec_prefix in self.quality_priorities[qn]:
                    for v in candidate:
                        if v["codecs"].startswith(codec_prefix):
                            video_stream = v
                            break
                    if video_stream:
                        break
            if video_stream:
                break

        audio_stream = None
        if dash_data.get("flac") and dash_data["flac"].get("audio"):
            audio_stream = dash_data["flac"]["audio"]
        else:
            audio_candidates = dash_data.get("audio", [])
            audio_candidates = sorted(audio_candidates, key=lambda x: x.get("bandwidth", 0), reverse=True)
            if audio_candidates:
                audio_stream = audio_candidates[0]

        return video_stream, audio_stream

    def list_all_streams(self, play_info: dict) -> List[dict]:
        dash_data = play_info["data"].get("dash", None)
        if not dash_data:
            return []
        video_streams = dash_data.get("video", [])
        result = []
        for v in video_streams:
            qn = v["id"]
            codec = v["codecs"]
            if qn not in self.quality_names:
                continue
            result.append({
                "qn": qn,
                "codec": codec,
                "quality_name": self.quality_names[qn],
                "base_url": v["baseUrl"],
                "bandwidth": v.get("bandwidth", 0)
            })
        # 按清晰度 & bandwidth 排序
        result = sorted(result, key=lambda x: (x["qn"], x["bandwidth"]), reverse=True)
        return result

    async def download_file(self, url: str, filename: str) -> bool:
        """单个分段下载到文件，内置多次重试。"""
        try:
            response = await asyncio.get_event_loop().run_in_executor(
                None, lambda: requests.head(url, headers=self.headers)
            )
            response.raise_for_status()
            file_size = int(response.headers.get('content-length', 0))
            if file_size == 0:
                raise ValueError("无法获取文件大小")

            # 降低分段大小到 1MB
            chunk_size = 1 * 1024 * 1024
            chunks = [(i, min(i + chunk_size - 1, file_size - 1))
                      for i in range(0, file_size, chunk_size)]

            async def download_chunk(start: int, end: int) -> Tuple[int, bytes]:
                range_headers = dict(self.headers)
                range_headers['Range'] = f'bytes={start}-{end}'
                for _ in range(3):
                    try:
                        resp = await asyncio.get_event_loop().run_in_executor(
                            None, lambda: requests.get(url, headers=range_headers, timeout=30)
                        )
                        resp.raise_for_status()
                        return start, resp.content
                    except Exception as e:
                        logger.warning(f"分段下载失败 ({start}-{end}): {e}, 重试中...")
                        await asyncio.sleep(1)
                raise Exception(f"分段下载失败: {start}-{end}")

            temp_filename = f"{filename}.temp"
            with open(temp_filename, 'wb') as f:
                f.truncate(file_size)

            tasks = [download_chunk(s, e) for (s, e) in chunks]
            chunk_results = await asyncio.gather(*tasks, return_exceptions=True)

            with open(temp_filename, 'r+b') as f:
                for result in chunk_results:
                    if isinstance(result, Exception):
                        raise result
                    start, data = result
                    f.seek(start)
                    f.write(data)

            os.replace(temp_filename, filename)
            return True
        except Exception as e:
            logger.error(f"下载失败: {filename}, {e}")
            if os.path.exists(f"{filename}.temp"):
                os.remove(f"{filename}.temp")
            return False

    async def download_stream(self, bvid: str, cid: str, video_stream: dict, audio_stream: dict) -> DownloadResult:
        temp_dir = Path("temp") / f"{bvid}_{cid}_{uuid4().hex[:6]}"
        temp_dir.mkdir(parents=True, exist_ok=True)
        try:
            video_path = temp_dir / "video.m4s"
            audio_path = temp_dir / "audio.m4s"

            v_task = asyncio.create_task(self.download_file(video_stream["baseUrl"], str(video_path)))
            a_task = asyncio.create_task(self.download_file(audio_stream["baseUrl"], str(audio_path)))
            v_ok, a_ok = await asyncio.gather(v_task, a_task)
            if not (v_ok and a_ok):
                raise RuntimeError("视频或音频分段下载失败")

            output_path = self.download_path / f"{bvid}-{cid}.mp4"
            cmd = [
                "ffmpeg", "-i", str(video_path), "-i", str(audio_path),
                "-c:v", "copy", "-c:a", "copy", "-y", str(output_path)
            ]
            proc = await asyncio.create_subprocess_exec(
                *cmd, stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE
            )
            _, stderr = await proc.communicate()
            if proc.returncode != 0:
                raise RuntimeError(f"FFmpeg合并失败: {stderr.decode('utf-8', 'ignore')}")

            # 清理临时文件
            for f in temp_dir.iterdir():
                f.unlink()
            temp_dir.rmdir()

            return DownloadResult(bvid, cid, str(output_path), True)
        except Exception as e:
            return DownloadResult(bvid, cid, "", False, error=str(e))

    async def download_part(self, bvid: str, cid: str) -> DownloadResult:
        try:
            play_info = await self.get_play_info(bvid, cid)
            v_stream, a_stream = self.find_best_stream(play_info)
            if not v_stream or not a_stream:
                raise ValueError("未找到可用的视频或音频流")

            return await self.download_stream(bvid, cid, v_stream, a_stream)
        except Exception as e:
            return DownloadResult(bvid, cid, "", False, error=str(e))

    async def download_video(self, bvid: str) -> List[DownloadResult]:
        """下载一个BV所有分P，串行处理。"""
        results = []
        try:
            parts = await self.get_video_parts(bvid)
            for p in parts:
                cid = str(p["cid"])
                r = await self.download_part(bvid, cid)
                results.append(r)
            return results
        except Exception as e:
            return [DownloadResult(bvid, "unknown", "", False, error=str(e))]


# ========== 2) bilibili.py: 获取视频信息、解析等 ==========

@dataclass
class VideoInfo:
    bvid: str
    aid: int
    title: str
    duration: int
    publish_time: datetime
    view_count: int
    danmaku_count: int
    like_count: int
    coin_count: int
    favorite_count: int
    share_count: int
    reply_count: int
    dimension: Dict[str, int]


def parse_bilibili_video_info(response_json: dict) -> dict:
    if "data" not in response_json or "data" not in response_json["data"]:
        raise ValueError("Invalid response: 缺少 data.data 节点")

    vd = response_json["data"]["data"]
    dur = vd.get("duration", 0)
    cover_url = vd.get("pic", "")

    basic = {
        "title": vd.get("title", ""),
        "bvid": vd.get("bvid", ""),
        "aid": vd.get("aid", 0),
        "cid": vd.get("cid", 0),
        "duration": {
            "seconds": dur,
            "formatted": f"{dur // 60}:{str(dur % 60).zfill(2)}"
        },
        "resolution": {
            "width": vd.get("dimension", {}).get("width", 0),
            "height": vd.get("dimension", {}).get("height", 0),
        },
        "category": {
            "main": vd.get("tname", ""),
            "sub": vd.get("tname_v2", "")
        },
        "publishTime": datetime.fromtimestamp(vd.get("pubdate", 0)).isoformat(),
        "description": vd.get("desc", ""),
        "coverUrl": cover_url
    }

    owner = vd.get("owner", {})
    owner_info = {
        "uid": owner.get("mid", 0),
        "username": owner.get("name", ""),
        "avatarUrl": owner.get("face", "")
    }

    stat = vd.get("stat", {})
    statistics = {
        "views": stat.get("view", 0),
        "danmaku": stat.get("danmaku", 0),
        "comments": stat.get("reply", 0),
        "favorites": stat.get("favorite", 0),
        "coins": stat.get("coin", 0),
        "shares": stat.get("share", 0),
        "likes": stat.get("like", 0),
        "historicalRank": stat.get("his_rank", 0)
    }

    parsed_info = {
        "basic": basic,
        "owner": owner_info,
        "statistics": statistics
    }
    return parsed_info


def build_bilibili_caption(video_info: dict) -> str:
    lines = []
    b = video_info["basic"]
    lines.append(f"<b>标题</b>: {b['title']}")
    lines.append(f"BV号: {b['bvid']}")
    lines.append(f"AID: {b['aid']}")
    lines.append(f"CID: {b['cid']}")
    lines.append(f"时长: {b['duration']['formatted']} ({b['duration']['seconds']} 秒)")
    lines.append(f"分辨率: {b['resolution']['width']} x {b['resolution']['height']}")
    lines.append(f"分区: {b['category']['main']}")
    lines.append(f"发布时间: {b['publishTime']}")
    lines.append(b['description'])

    o = video_info["owner"]
    lines.append(f"\n<b>UP主</b>: {o['username']} (UID: {o['uid']})")

    s = video_info["statistics"]
    stat_line = (f"播放: {s['views']} | 弹幕: {s['danmaku']} | 评论: {s['comments']} | "
                 f"收藏: {s['favorites']} | 投币: {s['coins']} | 分享: {s['shares']} | 点赞: {s['likes']}")
    if s['historicalRank'] > 0:
        stat_line += f" | 最高排行: {s['historicalRank']}"
    lines.append(f"\n{stat_line}")
    return "\n".join(lines)


def sync_fetch_one_video_info_and_cid(bv_id: str):
    """同步获取B站视频信息，以便在子线程池内调用。"""
    url = f"http://localhost:80/api/bilibili/web/fetch_one_video?bv_id={bv_id}"
    try:
        resp = requests.get(url, timeout=10).json()
    except Exception as ex:
        return None, 0, None, False

    # 可能返回 data.code=62002 => 稿件不可见或已删除
    if resp.get("data", {}).get("code") == 62002:
        return None, 0, None, True

    video_data = resp.get("data", {}).get("data", {})
    if not video_data or not video_data.get("bvid"):
        return None, 0, None, False

    parsed_data = parse_bilibili_video_info(resp)

    cid = video_data.get("cid", 0)
    from datetime import datetime
    vi = VideoInfo(
        bvid=video_data.get("bvid", ""),
        aid=video_data.get("aid", 0),
        title=video_data.get("title", ""),
        duration=video_data.get("duration", 0),
        publish_time=datetime.fromtimestamp(video_data.get("pubdate", 0)),
        view_count=video_data.get("stat", {}).get("view", 0),
        danmaku_count=video_data.get("stat", {}).get("danmaku", 0),
        like_count=video_data.get("stat", {}).get("like", 0),
        coin_count=video_data.get("stat", {}).get("coin", 0),
        favorite_count=video_data.get("stat", {}).get("favorite", 0),
        share_count=video_data.get("stat", {}).get("share", 0),
        reply_count=video_data.get("stat", {}).get("reply", 0),
        dimension=video_data.get("dimension", {})
    )
    return vi, cid, parsed_data, False


def _sync_fetch_all_bvids(uid: str) -> List[str]:
    base_url = "http://localhost:80/api/bilibili/web/fetch_user_post_videos"
    page_size = 20
    all_bvids = []
    page = 1
    while True:
        try:
            url = f"{base_url}?uid={uid}&pn={page}"
            r = requests.get(url, timeout=10).json()
            data_data = r.get("data", {}).get("data", {})
            page_info = data_data.get("page", {})
            list_data = data_data.get("list", {})
            vlist = list_data.get("vlist", [])

            if not vlist:
                break

            for v in vlist:
                all_bvids.append(v["bvid"])

            total_count = page_info.get("count", 0)
            total_pages = math.ceil(total_count / page_size)
            if page >= total_pages:
                break
            page += 1
        except:
            break
    return all_bvids


async def fetch_all_bvids(uid: str) -> List[str]:
    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(thread_pool, _sync_fetch_all_bvids, uid)


async def fetch_user_collect_folders(uid: str) -> List[dict]:
    def _inner(uid: str):
        try:
            url = f"http://localhost:80/api/bilibili/web/fetch_collect_folders?uid={uid}"
            r = requests.get(url, timeout=10).json()
            return r.get("data", {}).get("data", {}).get("list", [])
        except:
            return []
    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(thread_pool, _inner, uid)


async def fetch_folder_bvids(folder_id: str) -> List[str]:
    def _inner(folder_id: str) -> List[str]:
        base_url = "http://localhost:80/api/bilibili/web/fetch_user_collection_videos"
        out = []
        page = 1
        while True:
            try:
                url = f"{base_url}?folder_id={folder_id}&pn={page}"
                j = requests.get(url, timeout=10).json()
                dd = j.get("data", {}).get("data", {})
                has_more = dd.get("has_more", False)
                medias = dd.get("medias", [])
                for m in medias:
                    if "bvid" in m:
                        out.append(m["bvid"])
                if not has_more:
                    break
                page += 1
            except:
                break
        return out
    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(thread_pool, _inner, folder_id)


# ========== 3) Telegram Bot 逻辑（含持久化、监听单线程调度等） ==========

def get_random_session_name():
    return f"bot_session_{uuid4().hex[:8]}"


app = PyroClient(
    name=get_random_session_name(),
    api_id=api_id,
    api_hash=api_hash,
    bot_token=bot_token,
    no_updates=False,
    workers=4,
    in_memory=True
)

def get_or_create_user(chat_id, **kwargs):
    return {'chat_id': chat_id, 'username': kwargs.get('username', '')}

async def safe_send_message(client: PyroClient, chat_id, text, **kwargs):
    # 避免 MESSAGE_TOO_LONG，需要切割 text
    # 但本函数我们只对单次调用做简单的 try-catch
    # /check 命令的分割在命令处理时自己完成
    while True:
        try:
            return await client.send_message(chat_id, text, **kwargs)
        except FloodWait as e:
            logger.warning(f"send_message FloodWait: 等待 {e.value} 秒...")
            await asyncio.sleep(e.value + 1)
        except Exception as ex:
            logger.warning(f"send_message 出错: {ex}")
            break

async def safe_edit_text(message: Message, text, **kwargs):
    while True:
        try:
            return await message.edit_text(text, **kwargs)
        except FloodWait as e:
            logger.warning(f"edit_text FloodWait: 等待 {e.value} 秒...")
            await asyncio.sleep(e.value + 1)
        except Exception as ex:
            logger.warning(f"edit_text 出错: {ex}")
            break

async def safe_send_video(client: PyroClient, chat_id, video, **kwargs):
    while True:
        try:
            return await client.send_video(chat_id, video, **kwargs)
        except FloodWait as e:
            logger.warning(f"send_video FloodWait: 等待 {e.value} 秒...")
            await asyncio.sleep(e.value + 1)
        except Exception as ex:
            logger.warning(f"send_video 出错: {ex}")
            break

async def safe_send_document(client: PyroClient, chat_id, document, **kwargs):
    while True:
        try:
            return await client.send_document(chat_id, document, **kwargs)
        except FloodWait as e:
            logger.warning(f"send_document FloodWait: 等待 {e.value} 秒...")
            await asyncio.sleep(e.value + 1)
        except Exception as ex:
            logger.warning(f"send_document 出错: {ex}")
            break


BV_PATTERN = r"(BV[0-9a-zA-Z]+)"
def extract_bv_id(text: str) -> str:
    match = re.search(BV_PATTERN, text)
    if match:
        return match.group(1)
    return ""


STREAMS_CACHE: Dict[Tuple[str, str], dict] = {}

async def _generate_thumbnail_and_ffprobe(video_path: Path, bvid: str, cid: str):
    jpg_path = video_path.with_suffix('.jpg')
    cmd_thumb = [
        'ffmpeg', '-y', '-i', str(video_path),
        '-ss', '00:00:01', '-vframes', '1', '-f', 'image2', str(jpg_path)
    ]
    try:
        subprocess.run(cmd_thumb, check=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
    except Exception as e:
        logger.warning(f"[{bvid}] cid={cid} 生成缩略图失败: {e}")

    cmd_probe = [
        'ffprobe', '-v', 'quiet',
        '-print_format', 'json',
        '-show_format', '-show_streams', str(video_path)
    ]
    probe_json_path = video_path.with_suffix('.json')
    try:
        out = subprocess.check_output(cmd_probe)
        info = json.loads(out)
        with open(probe_json_path, 'w', encoding='utf-8') as f:
            json.dump(info, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logger.warning(f"[{bvid}] cid={cid} ffprobe失败: {e}")


async def send_video_to_telegram(
    client: PyroClient, chat_id: int,
    video_path: str,
    caption: str,
    source_url: str = "",
    cover_url: str = None,
    bvid: str = "",
    cid: int = 0,
    show_quality_buttons: bool = False,
    extra_folder_info: str = None
) -> bool:
    """发送到 Telegram，包括 video 和 file 两种形态。"""
    try:
        vp = Path(video_path)
        if not vp.exists():
            return False

        await _generate_thumbnail_and_ffprobe(vp, bvid, str(cid))
        thumb_path = vp.with_suffix('.jpg')
        params_path = vp.with_suffix('.json')

        format_info = ""
        if params_path.exists():
            with open(params_path, 'r', encoding='utf-8') as f:
                video_params = json.load(f)

            # 获取video流
            v_stream = None
            for s in video_params.get("streams", []):
                if s.get("codec_type") == "video":
                    v_stream = s
                    break
            if v_stream:
                width = v_stream.get("width", 0)
                height = v_stream.get("height", 0)
                codec = v_stream.get("codec_name", "")
                fps_str = v_stream.get("r_frame_rate", "0/0")
                fps_val = 0
                try:
                    num, den = fps_str.split("/")
                    if int(den) != 0:
                        fps_val = round(int(num)/int(den))
                except:
                    pass
                br = float(v_stream.get("bit_rate", 0))
                if br <= 0:
                    br = float(video_params.get("format", {}).get("bit_rate", 0))
                mbps = br / 1024 / 1024 if br else 0
                dur_val = float(video_params.get("format", {}).get("duration", 0))
                size_mb = (br / 8) * dur_val / (1024*1024) if br>0 else 0

                lines = [
                    "<b>[视频流信息]</b>",
                    f"分辨率: {width} x {height}",
                    f"编码格式: {codec}",
                    f"帧率: {fps_val} fps",
                    f"码率: {mbps:.2f} Mbps",
                    f"时长: {int(dur_val)} 秒",
                    f"预计大小: {size_mb:.2f} MB"
                ]
                format_info = "\n".join(lines)

            # 修正 caption 里的时长
            total_duration = int(float(video_params.get("format", {}).get("duration", 0)))
            if total_duration > 0:
                fm = f"{total_duration//60}:{str(total_duration%60).zfill(2)}"
                caption = re.sub(r'时长: \d+:\d+ \(\d+ 秒\)',
                                 f'时长: {fm} ({total_duration} 秒)', caption)

        if extra_folder_info:
            caption += f"\n\n{extra_folder_info}"

        # 视频消息中的按钮
        base_buttons = []
        row1 = [InlineKeyboardButton("🎬 打开作品", url=source_url)]
        if cover_url:
            row1.append(InlineKeyboardButton("🖼 获取封面", url=cover_url))
        base_buttons.append(row1)

        uid_m = re.search(r'UID:\s*(\d+)', caption)
        if uid_m:
            uid_val = uid_m.group(1)
            row2 = [
                InlineKeyboardButton("📚 获取全部作品", callback_data=f"get_all_videos:{uid_val}"),
                InlineKeyboardButton("💾 获取收藏夹", callback_data=f"get_user_favorites:{uid_val}")
            ]
            base_buttons.append(row2)

        # 文件消息中的清晰度选择按钮
        quality_buttons = []
        if show_quality_buttons and (bvid, str(cid)) in STREAMS_CACHE:
            all_streams = STREAMS_CACHE[(bvid, str(cid))]["streams"]
            row = []
            for st in all_streams:
                qn = st["qn"]
                cc = st["codec"]
                callback_data = f"s:{bvid}:{cid}:{qn}:{cc}"
                short_cc = cc.split(".")[0]
                btn_text = f"{st['quality_name']}({short_cc})"
                row.append(InlineKeyboardButton(btn_text, callback_data=callback_data))
                if len(row) == 3:
                    quality_buttons.append(row)
                    row = []
            if row:
                quality_buttons.append(row)

        # 先发送视频
        progress_msg = await safe_send_message(client, chat_id, "正在发送视频...")
        # 获取宽高、时长，用于send_video参数
        width, height, duration = 0, 0, 0
        if params_path.exists():
            with open(params_path, 'r', encoding='utf-8') as f:
                p = json.load(f)
            for s in p.get("streams", []):
                if s.get("codec_type") == "video":
                    width = s.get("width", 0)
                    height = s.get("height", 0)
                    break
            duration = int(float(p.get("format", {}).get("duration", 0)))

        await safe_send_video(
            client, chat_id, str(vp),
            caption=caption, parse_mode=ParseMode.HTML,
            thumb=str(thumb_path) if thumb_path.exists() else None,
            duration=duration if duration>0 else None,
            width=width if width>0 else None,
            height=height if height>0 else None,
            supports_streaming=True,
            reply_markup=InlineKeyboardMarkup(base_buttons),
            progress=progress_callback
        )

        # 再发送文件
        doc_lines = []
        if bvid:
            doc_lines.append(f"bv_id:{bvid}")
        if cid:
            doc_lines.append(f"cid:{cid}")
        if format_info:
            doc_caption = f"{format_info}\n\n" + "\n".join(doc_lines)
        else:
            doc_caption = "\n".join(doc_lines)

        if doc_caption.strip():
            await safe_edit_text(progress_msg, "准备以文件形式发送...")
            await safe_send_document(
                client, chat_id, str(vp),
                caption=doc_caption,
                parse_mode=ParseMode.HTML,
                thumb=str(thumb_path) if thumb_path.exists() else None,
                reply_markup=InlineKeyboardMarkup(quality_buttons) if quality_buttons else None,
                progress=progress_callback
            )

        await safe_edit_text(progress_msg, "发送完成")
        await asyncio.sleep(2)
        await progress_msg.delete()

        # 清理本地文件
        try:
            vp.unlink(missing_ok=True)
            thumb_path.unlink(missing_ok=True)
            params_path.unlink(missing_ok=True)
        except:
            pass

        return True
    except Exception as e:
        logger.warning(f"send_video_to_telegram失败: {e}")
        return False


# 仅允许一个下载流水线任务同时进行
CONCURRENCY = 1
pipeline_semaphore = asyncio.Semaphore(CONCURRENCY)

thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=2)


async def pipeline_single_bvid(client: PyroClient, chat_id: int, bvid: str,
                               folder_name: str = "", folder_id: str = "") -> bool:
    """下载并发送 bvid 全部分P视频。可选传入收藏夹信息用于caption。"""
    async with pipeline_semaphore:
        loop = asyncio.get_running_loop()
        vi, cid, parsed_data, is_deleted = await loop.run_in_executor(
            thread_pool, sync_fetch_one_video_info_and_cid, bvid
        )
        if is_deleted:
            logger.info(f"[{bvid}] 稿件不可见/已删除")
            return False
        if not cid or not vi or not parsed_data:
            return False

        extra_info = None
        if folder_name and folder_id:
            extra_info = f"所属收藏夹: {folder_name} (ID: {folder_id})"

        cover_url = parsed_data["basic"]["coverUrl"]
        caption_text = build_bilibili_caption(parsed_data)
        source_url = f"https://www.bilibili.com/video/{bvid}"

        # 开始真正下载
        from_ff = AsyncBiliDownloader()
        results = await from_ff.download_video(bvid)
        if not results:
            return False

        any_ok = False
        for r in results:
            if not r.success:
                continue  # 跳过失败分P
            mp4_path = r.file_path
            # 获取可选流信息做缓存
            try:
                pi = await from_ff.get_play_info(bvid, r.cid)
                dash = pi["data"].get("dash", {})
                audio_stream = None
                if dash.get("flac") and dash["flac"].get("audio"):
                    audio_stream = dash["flac"]["audio"]
                else:
                    audios = dash.get("audio", [])
                    audios = sorted(audios, key=lambda x: x.get("bandwidth", 0), reverse=True)
                    audio_stream = audios[0] if audios else None
                all_streams = from_ff.list_all_streams(pi)
                if audio_stream and all_streams:
                    STREAMS_CACHE[(bvid, r.cid)] = {
                        "audio_stream": audio_stream,
                        "streams": all_streams
                    }
            except:
                pass
            # 发送到Telegram
            ok = await send_video_to_telegram(
                client, chat_id, mp4_path,
                caption=caption_text,
                source_url=source_url,
                cover_url=cover_url,
                bvid=bvid,
                cid=int(r.cid),
                show_quality_buttons=True,
                extra_folder_info=extra_info
            )
            if ok:
                any_ok = True

        return any_ok


# ========== 监控和统计持久化相关 ==========

FAVORITES_HISTORY_FILE = "favorites_history.json"
PROCESSED_BVID_CIDS: Dict[str, List[str]] = {}
FAILURES: Dict[str, str] = {}
FOLDER_STATS: Dict[str, dict] = {}
# FOLDER_STATS结构:
# {
#   folder_id: {
#       "title": str,
#       "bvids": set([...]),
#       "processed_bvids": set([...]),
#       "failed_bvids": set([...])
#   },
#   ...
# }

def load_processed_entries():
    global PROCESSED_BVID_CIDS, FAILURES, FOLDER_STATS
    if os.path.exists(FAVORITES_HISTORY_FILE):
        try:
            with open(FAVORITES_HISTORY_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
            PROCESSED_BVID_CIDS = data.get("processed", {})
            FAILURES = data.get("failures", {})
            folder_stats = data.get("folder_stats", {})
            # 将 folder_stats 中的列表转回 set
            FOLDER_STATS.clear()
            for fid, info in folder_stats.items():
                FOLDER_STATS[fid] = {
                    "title": info.get("title", ""),
                    "bvids": set(info.get("bvids", [])),
                    "processed_bvids": set(info.get("processed_bvids", [])),
                    "failed_bvids": set(info.get("failed_bvids", []))
                }
            logger.info("已从 JSON 文件加载历史数据。")
        except Exception as e:
            logger.warning(f"加载数据失败: {e}")
    else:
        logger.info("未发现历史记录文件，使用空数据。")


def save_processed_entries():
    try:
        folder_stats_serializable = {}
        for fid, info in FOLDER_STATS.items():
            folder_stats_serializable[fid] = {
                "title": info["title"],
                "bvids": list(info["bvids"]),
                "processed_bvids": list(info["processed_bvids"]),
                "failed_bvids": list(info["failed_bvids"])
            }
        data = {
            "processed": PROCESSED_BVID_CIDS,
            "failures": FAILURES,
            "folder_stats": folder_stats_serializable
        }
        with open(FAVORITES_HISTORY_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logger.warning(f"保存数据失败: {e}")


async def get_cids_for_bvid(bvid: str) -> List[str]:
    d = AsyncBiliDownloader()
    parts = await d.get_video_parts(bvid)
    return [str(p["cid"]) for p in parts]


USER_TASK_QUEUE = asyncio.Queue()  # 用户链接任务队列
monitor_thread = None  # 在 /start 命令中启动线程
monitor_running = False  # 监听是否在运行


async def monitor_favorites():
    """轮询收藏夹，每次处理一个收藏夹，串行处理里面每个 bvid。
       处理过程中若出现用户任务，则先切到用户任务，处理完后再回到当前收藏夹。
       每个收藏夹最多处理1小时，超时后切换到下一个。
    """
    global monitor_running
    monitor_running = True
    uid_target = "12954775"
    while monitor_running:
        try:
            folder_list = await fetch_user_collect_folders(uid_target)
            if not folder_list:
                logger.warning("未获取到任何收藏夹，等待 600s 后重试...")
                await asyncio.sleep(600)
                continue

            # 更新/初始化 FOLDER_STATS
            for folder in folder_list:
                fid = str(folder.get("id"))
                ftitle = folder.get("title", "无标题")
                if not fid:
                    continue
                if fid not in FOLDER_STATS:
                    FOLDER_STATS[fid] = {
                        "title": ftitle,
                        "bvids": set(),
                        "processed_bvids": set(),
                        "failed_bvids": set()
                    }

            idx = 0
            while monitor_running:
                if idx >= len(folder_list):
                    idx = 0
                folder = folder_list[idx]
                idx += 1

                folder_id = str(folder.get("id"))
                folder_title = folder.get("title", "无标题")
                if not folder_id:
                    continue

                bvids = await fetch_folder_bvids(folder_id)
                FOLDER_STATS[folder_id]["bvids"].update(bvids)
                save_processed_entries()

                start_t = time.monotonic()
                logger.info(f"开始处理收藏夹: {folder_title}, ID={folder_id}, 最多1小时")

                for bvid in bvids:
                    if not monitor_running:
                        break
                    if time.monotonic() - start_t >= 3600:
                        logger.info(f"处理 {folder_title} 超过1小时，切到下一个")
                        break

                    # 先处理用户队列
                    while not USER_TASK_QUEUE.empty():
                        if not monitor_running:
                            break
                        user_bv = await USER_TASK_QUEUE.get()
                        await pipeline_single_bvid(app, 235196660, user_bv)
                        USER_TASK_QUEUE.task_done()

                    # 如果该 bvid 已处理或已失败，跳过
                    if (bvid in FOLDER_STATS[folder_id]["processed_bvids"] or
                        bvid in FOLDER_STATS[folder_id]["failed_bvids"]):
                        continue

                    # 获取分P cid 来判断稿件可见性
                    try:
                        _ = await get_cids_for_bvid(bvid)
                    except Exception as e:
                        logger.warning(f"{bvid} => 分P获取失败: {e}")
                        FAILURES[bvid] = str(e)  # 内部记录原因，但 /check 不显示
                        FOLDER_STATS[folder_id]["failed_bvids"].add(bvid)
                        save_processed_entries()
                        continue

                    # 下载并发送
                    success = await pipeline_single_bvid(
                        app, 235196660, bvid,
                        folder_name=folder_title, folder_id=folder_id
                    )
                    if success:
                        FOLDER_STATS[folder_id]["processed_bvids"].add(bvid)
                    else:
                        FAILURES[bvid] = FAILURES.get(bvid, "failed")
                        FOLDER_STATS[folder_id]["failed_bvids"].add(bvid)

                    save_processed_entries()

                # end for bvid
                if not monitor_running:
                    break
            # end while
        except Exception as e:
            logger.exception(f"monitor_favorites发生异常: {e}")
            await asyncio.sleep(600)

        await asyncio.sleep(30)

    logger.info("monitor_favorites 已停止。")


@app.on_callback_query(filters.regex(r"^s:"))
async def handle_select_quality(client: PyroClient, callback_query: CallbackQuery):
    if callback_query.message.chat.id != 235196660:
        return
    try:
        parts = callback_query.data.split(":")
        if len(parts) != 5:
            await callback_query.answer("参数错误", show_alert=True)
            return
        _, bvid, cid, qn_str, codec = parts
        qn = int(qn_str)

        if (bvid, cid) not in STREAMS_CACHE:
            await callback_query.answer("找不到流信息缓存", show_alert=True)
            return
        cached = STREAMS_CACHE[(bvid, cid)]
        audio_stream = cached["audio_stream"]
        all_streams = cached["streams"]
        target_v = None
        for vs in all_streams:
            if vs["qn"] == qn and vs["codec"] == codec:
                target_v = vs
                break
        if not target_v:
            await callback_query.answer("无法匹配该清晰度", show_alert=True)
            return
        await callback_query.answer("开始下载指定清晰度", show_alert=False)

        from_ff = AsyncBiliDownloader()
        video_stream = {"baseUrl": target_v["base_url"]}
        result = await from_ff.download_stream(bvid, cid, video_stream, audio_stream)
        if not result.success:
            await callback_query.message.reply_text(f"下载失败: {result.error}")
            return

        chosen_caption = f"你选择的清晰度: {target_v['quality_name']} / {codec}\nBV: {bvid}\nCID: {cid}"
        cover_url = f"https://www.bilibili.com/video/{bvid}"
        await send_video_to_telegram(
            client,
            callback_query.message.chat.id,
            result.file_path,
            chosen_caption,
            source_url=cover_url,
            cover_url=cover_url,
            bvid=bvid,
            cid=int(cid),
            show_quality_buttons=False
        )
    except Exception as e:
        logger.exception(f"画质选择回调出错: {e}")
        await callback_query.answer(str(e), show_alert=True)


@app.on_callback_query()
async def handle_callback(client: PyroClient, callback_query: CallbackQuery):
    if callback_query.message.chat.id != 235196660:
        return
    data = callback_query.data
    if data.startswith("s:"):
        return

    try:
        chat_id = callback_query.message.chat.id
        if data.startswith("get_all_videos:"):
            uid = data.split(":")[1]
            await callback_query.answer("获取UP主作品列表...", show_alert=False)
            bvids = await fetch_all_bvids(uid)
            if not bvids:
                msg = await safe_send_message(client, chat_id, "未能获取到任何视频")
                await asyncio.sleep(5)
                await msg.delete()
                return
            for bv in bvids:
                await pipeline_single_bvid(client, chat_id, bv)
            return

        elif data.startswith("get_user_favorites:"):
            uid = data.split(":")[1]
            await callback_query.answer("正在获取收藏夹...", show_alert=False)
            folders = await fetch_user_collect_folders(uid)
            if not folders:
                msg = await safe_send_message(client, chat_id, "获取收藏夹为空")
                await asyncio.sleep(5)
                await msg.delete()
                return
            btns = []
            for f in folders:
                fid = f.get("id")
                if not fid:
                    continue
                title = f.get("title","未命名")
                mc = f.get("media_count",0)
                btn_text = f"💾 {title} ({mc}个视频)"
                btns.append([
                    InlineKeyboardButton(btn_text, callback_data=f"get_folder_videos:{uid}:{fid}:{title}")
                ])
            text = f"用户UID={uid}共有 {len(folders)} 个收藏夹，请选择："
            await safe_send_message(client, chat_id, text, reply_markup=InlineKeyboardMarkup(btns))
            return

        elif data.startswith("get_folder_videos:"):
            parts = data.split(":")
            uid = parts[1]
            fid = parts[2]
            ftitle = parts[3] if len(parts)>3 else "收藏夹"
            await callback_query.answer("开始获取收藏夹视频...", show_alert=False)
            bvids = await fetch_folder_bvids(fid)
            if not bvids:
                msg = await safe_send_message(client, chat_id, f"收藏夹 {ftitle} 下没有视频")
                await asyncio.sleep(5)
                await msg.delete()
                return
            for bv in bvids:
                await pipeline_single_bvid(client, chat_id, bv, folder_name=ftitle, folder_id=fid)
            return

        else:
            await callback_query.answer("未匹配的回调", show_alert=False)

    except Exception as e:
        await callback_query.answer(f"处理错误: {e}", show_alert=True)


@app.on_message(filters.command(["start"]))
async def start_command(client: PyroClient, message: Message):
    """用户发送 /start 后才启动监听线程"""
    if message.chat.id != 235196660:
        return
    chat_id = message.chat.id
    username = message.from_user.username or ""
    get_or_create_user(chat_id, username=username)

    text = f"👋 你好！\n你的 Chat ID 是 <code>{chat_id}</code>\n用户名: @{username}\n"
    text += "已启动监听线程..."  # 提示即将开始监控

    # 若监听线程尚未启动，则启动
    global monitor_thread
    if not monitor_thread or not monitor_thread.is_alive():
        def start_monitor():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(monitor_favorites())

        monitor_thread = concurrent.futures.ThreadPoolExecutor(max_workers=1)
        monitor_thread.submit(start_monitor)

    await safe_send_message(client, chat_id, text, parse_mode=ParseMode.HTML)


class B23Expander:
    """处理 b23.tv 短链接的类"""
    def __init__(self):
        self.patterns = {
            'random': r'b23\.tv/([0-9a-zA-Z]{7})',
            'av': r'b23\.tv/av(\d+)',
            'bv': r'b23\.tv/BV([0-9a-zA-Z]+)'
        }

    def extract_from_share_text(self, text):
        share_pat = r'【.*?】\s*(https?://[^\s]+)'
        m = re.search(share_pat, text)
        return m.group(1) if m else text

    def _get_redirect_url(self, short_url):
        try:
            if not short_url.startswith(('http://', 'https://')):
                short_url = 'https://' + short_url
            resp = requests.head(short_url, allow_redirects=True, timeout=10)
            resp.raise_for_status()
            return resp.url
        except Exception as e:
            raise Exception(f"获取重定向URL失败: {e}")

    def expand_url(self, text):
        url = self.extract_from_share_text(text).strip()
        pu = urlparse(url)
        if not (pu.netloc == 'b23.tv' or url.startswith('b23.tv')):
            return url
        for _, pat in self.patterns.items():
            if re.search(pat, url):
                return self._get_redirect_url(url)
        raise ValueError("未识别的 b23.tv 短链接格式")


@app.on_message(filters.text & ~filters.command(["start", "help", "id", "check", "reset"]))
async def handle_url(client: PyroClient, message: Message):
    if message.chat.id != 235196660:
        return
    text_in = message.text.strip()
    if not text_in:
        await message.delete()
        return

    try:
        if "b23.tv" in text_in or "【" in text_in:
            try:
                expander = B23Expander()
                text_in = expander.expand_url(text_in)
            except Exception as e:
                logger.info(f"展开链接失败: {e}")

        if "bilibili.com/video/" in text_in or re.search(BV_PATTERN, text_in):
            bvid = extract_bv_id(text_in)
            if not bvid:
                return
            await USER_TASK_QUEUE.put(bvid)

            info_msg = await safe_send_message(client, message.chat.id, f"已接收BV({bvid})任务，稍后处理。")
            await asyncio.sleep(3)
            await info_msg.delete()
            await message.delete()

    except Exception as e:
        logger.exception(f"处理文本出错: {e}")


@app.on_message(filters.command(["check"]))
async def check_command(client: PyroClient, message: Message):
    """分两部分发送：1) 收藏夹状态；2) 失败的 bvid（如果过长则分条）"""
    if message.chat.id != 235196660:
        return

    # 1) 构造收藏夹状态文本
    folder_lines = ["当前收藏夹处理进度：\n"]
    for fid, info in FOLDER_STATS.items():
        total_bv = len(info["bvids"])
        proc_bv = len(info["processed_bvids"]) + len(info["failed_bvids"])
        fail_bv = len(info["failed_bvids"])
        folder_lines.append(f"收藏夹: {info['title']} (ID: {fid})")
        folder_lines.append(f" - 已处理: {proc_bv}/{total_bv} (失败: {fail_bv})\n")

    folder_text = "\n".join(folder_lines)
    await safe_send_message(client, message.chat.id, folder_text)

    # 2) 发送失败列表（不显示原因）
    if not FAILURES:
        await safe_send_message(client, message.chat.id, "没有失败的BV")
        return

    fail_lines = ["失败的BV号如下："]
    for bvid in FAILURES.keys():
        fail_lines.append(f"- {bvid}")

    fail_text = "\n".join(fail_lines)

    # Telegram 单条消息限制 ~4096 字符，这里写个分割函数
    def chunk_text(text_str, chunk_size=4000):
        lines_all = text_str.split("\n")
        current_chunk = []
        current_len = 0
        for line in lines_all:
            # +1 for newline
            if current_len + len(line) + 1 > chunk_size:
                yield "\n".join(current_chunk)
                current_chunk = [line]
                current_len = len(line)
            else:
                current_chunk.append(line)
                current_len += len(line) + 1
        if current_chunk:
            yield "\n".join(current_chunk)

    for chunk in chunk_text(fail_text):
        await safe_send_message(client, message.chat.id, chunk)


@app.on_message(filters.command(["reset"]))
async def reset_command(client: PyroClient, message: Message):
    """清空所有已完成的任务记录，从头开始"""
    if message.chat.id != 235196660:
        return

    PROCESSED_BVID_CIDS.clear()
    FAILURES.clear()
    FOLDER_STATS.clear()
    save_processed_entries()

    await safe_send_message(client, message.chat.id, "已重置所有任务记录，从头开始。")


def clean_session():
    session_pattern_main = "bot_session_*.session*"
    for sf in Path().glob(session_pattern_main):
        try:
            sf.unlink()
        except:
            pass

    session_pattern_upload = "upload_session_*.session*"
    for sf in Path().glob(session_pattern_upload):
        try:
            sf.unlink()
        except:
            pass


def main():
    print("Starting bilibot (now only starts monitoring after /start)...")

    import atexit
    atexit.register(clean_session)

    # 程序启动后先加载数据，但不启动监听
    load_processed_entries()

    # 直接运行 bot，但监听流程在 /start 命令中才会启动
    app.run()


if __name__ == "__main__":
    main()
