import requests
import json
import subprocess
import os
import time
from typing import Optional, Tuple, List, Dict
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path
import asyncio
from dataclasses import dataclass
import logging

# 1. 设置日志记录器
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)  # 可以根据需要调整日志级别为INFO/DEBUG/WARNING/ERROR等

@dataclass
class DownloadResult:
    bvid: str
    cid: str
    file_path: str
    success: bool
    error: Optional[str] = None

class AsyncBiliDownloader:
    # 这里把headers改为类变量，方便在set_cookie中修改
    headers = {
        "User-Agent": (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/91.0.4472.124 Safari/537.36"
        ),
        "Referer": "https://www.bilibili.com",
        "Cookie": (
            "buvid3=D92C01E2-A008-7ACA-D0A1-E2A7EBFAE6E484047infoc;"
            "b_nut=1728225384;"
            "_uuid=3DD69A1F-1C67-3B7E-98F3-2AB5C594AAD484584infoc;"
            "buvid_fp=f6751bde8a0c31a18cd7860cca8c7b8d;"
            "enable_web_push=DISABLE;"
            "home_feed_column=5;"
            "browser_resolution=1800-576;"
            "buvid4=903F7FA1-116A-0103-8942-CE956A797A6585260-024100614-CXx%2BSNP8kSYbEF7Wp%2BGj8g%3D%3D;"
            "PVID=1;"
            "LIVE_BUVID=AUTO2117282253876564;"
            "fingerprint=f6751bde8a0c31a18cd7860cca8c7b8d;"
            "buvid_fp_plain=undefined;"
            "SESSDATA=743a1f40%2C1750956870%2Cbe24a%2Ac2CjCXTGVXW2zeRMgSaf-kdnstaCsrRa2oIM1y0nxI-"
            "ejMwuhqkTS-uoyCrOw27mu9iywSVnU2QlJPTlgyeGdkUTFONmJtdV9FWjE0RjJuOVBhWmQzUWJobjBOaGM4"
            "WGtxbWY0M2dwRlQ0bDR5bEFyTWNJNlhWc094Ymt2MTVyb2FTTi1qbXRHNUJnIIEC;"
            "bili_jct=37b458961583e7e5d64087952ca265a7;"
            "DedeUserID=12954775;"
            "DedeUserID__ckMd5=7730e80f8c2b89f9;"
            "header_theme_version=CLOSE;"
            "CURRENT_FNVAL=2000;"
            "rpdid=|(um|)m)Jmll0J'u~JlR|l||l;"
            "bili_ticket=eyJhbGciOiJIUzI1NiIsImtpZCI6InMwMyIsInR5cCI6IkpXVCJ9."
            "eyJleHAiOjE3MzYwOTY0NjksImlhdCI6MTczNTgzNzIwOSwicGx0IjotMX0."
            "AFdnAJh4rZoCvLn2DrV5Qql2a0E87uTi_-7izkByw2w;"
            "bili_ticket_expires=1736096409;"
            "b_lsid=71043B102A_19427F6BE27;"
            "sid=53dhlf73"
        ),
    }

    def __init__(self):
        logger.info("初始化 AsyncBiliDownloader 实例...")

        # 视频清晰度优先级
        self.quality_priorities = {
            127: ['hev1', 'av01', 'avc1'],  # 8K
            126: ['hev1', 'avc1'],          # 杜比视界
            125: ['hev1', 'av01'],          # HDR 
            120: ['hev1', 'av01', 'avc1'],  # 4K
            116: ['hev1', 'av01', 'avc1'],  # 1080P60
            112: ['hev1', 'av01', 'avc1'],  # 1080P+
            80: ['hev1', 'av01', 'avc1'],   # 1080P
            64: ['hev1', 'av01', 'avc1'],   # 720P
            32: ['hev1', 'av01', 'avc1'],   # 480P
            16: ['hev1', 'av01', 'avc1'],   # 360P
        }

        self.quality_names = {
            127: "8K超高清", 126: "杜比视界", 125: "HDR真彩",
            120: "4K超清", 116: "1080P60帧", 112: "1080P+",
            80: "1080P", 64: "720P", 32: "480P", 16: "360P"
        }

        # 音频候选列表（一般默认选择带宽最高的即可）
        self.audio_qualities = [30280, 30232, 30216]
        self.audio_names = {
            30280: "192K", 30232: "132K", 30216: "64K",
            30250: "杜比全景声", 30251: "Hi-Res无损"
        }

        # 创建下载目录
        self.download_path = Path("downloads")
        self.download_path.mkdir(exist_ok=True)
        logger.debug(f"下载目录设置为：{self.download_path.resolve()}")

    @staticmethod
    def set_cookie(cookie: str):
        """设置Cookie"""
        AsyncBiliDownloader.headers["Cookie"] = cookie
        logger.info("全局Cookie已更新。")

    async def get_video_parts(self, bvid: str) -> List[Dict]:
        """异步获取视频分P信息"""
        url = f"https://api.bilibili.com/x/web-interface/view"
        params = {"bvid": bvid}

        logger.info(f"开始获取视频分P信息, bvid={bvid}, URL={url}")
        for attempt in range(3):  # 重试3次
            try:
                logger.debug(f"尝试第 {attempt+1} 次获取分P信息...")
                response = await asyncio.get_event_loop().run_in_executor(
                    None, 
                    lambda: requests.get(url, params=params, headers=self.headers, timeout=10)
                )
                response.raise_for_status()
                data = response.json()
                if data["code"] != 0:
                    raise ValueError(f"API error: {data['message']}")
                
                pages = data["data"]["pages"]
                logger.info(f"成功获取到 {len(pages)} 个分P。")
                return pages
            except Exception as e:
                logger.warning(f"获取分P信息失败，重试中...错误原因: {e}")
                await asyncio.sleep(1)

        err_msg = f"Failed to get video parts for {bvid}"
        logger.error(err_msg)
        raise RuntimeError(err_msg)

    async def get_play_info(self, bvid: str, cid: str) -> dict:
        """异步获取播放信息"""
        url = "https://api.bilibili.com/x/player/wbi/playurl"
        params = {
            "bvid": bvid,
            "cid": cid,
            "qn": 127,       # 以 8K 为优先请求
            "fnval": 4048,   # 支持更多音视频编码参数
            "fnver": 0,
            "fourk": 1
        }
        logger.info(f"开始获取视频播放信息, bvid={bvid}, cid={cid}")
        for attempt in range(3):
            try:
                logger.debug(f"尝试第 {attempt+1} 次获取播放信息...")
                response = await asyncio.get_event_loop().run_in_executor(
                    None,
                    lambda: requests.get(url, params=params, headers=self.headers, timeout=10)
                )
                response.raise_for_status()
                data = response.json()
                if data["code"] != 0:
                    raise ValueError(f"API error: {data['message']}")
                
                logger.info(f"成功获取播放信息：bvid={bvid}, cid={cid}")
                return data
            except Exception as e:
                logger.warning(f"获取播放信息失败，重试中...错误原因: {e}")
                await asyncio.sleep(1)

        err_msg = f"Failed to get play info for bvid={bvid}, cid={cid}"
        logger.error(err_msg)
        raise RuntimeError(err_msg)

    def find_best_stream(self, play_info: dict) -> Tuple[Optional[dict], Optional[dict]]:
        """查找最佳质量的视频和音频流"""
        logger.debug("开始匹配最佳视频和音频流...")
        dash_data = play_info["data"].get("dash", None)
        if not dash_data:
            logger.error("PlayInfo 中不包含 dash 数据，无法找到流。")
            return None, None

        # 按码率排序视频流
        video_streams = sorted(
            dash_data["video"],
            key=lambda x: x.get("bandwidth", 0),
            reverse=True
        )

        # 查找最高质量的视频流
        video_stream = None
        for quality in sorted(self.quality_priorities.keys(), reverse=True):
            quality_videos = [v for v in video_streams if v["id"] == quality]
            if quality_videos:
                # 进一步根据codec来匹配
                for codec_prefix in self.quality_priorities[quality]:
                    for video in quality_videos:
                        if video["codecs"].startswith(codec_prefix):
                            video_stream = video
                            logger.info(f"匹配到视频流：清晰度={self.quality_names.get(quality, quality)}, codec={codec_prefix}")
                            break
                    if video_stream:
                        break
            if video_stream:
                break

        # 查找最佳音频流（带宽最高的一个 or flac）
        audio_stream = None
        if dash_data.get("flac") and dash_data["flac"].get("audio"):
            audio_stream = dash_data["flac"]["audio"]
            logger.info("匹配到 FLAC 音频流。")
        else:
            audio_streams = sorted(
                dash_data["audio"],
                key=lambda x: x.get("bandwidth", 0),
                reverse=True
            )
            if audio_streams:
                audio_stream = audio_streams[0]
                logger.info("匹配到普通 DASH 音频流。")

        return video_stream, audio_stream

    async def download_file(self, url: str, filename: str) -> bool:
        """异步多线程分段下载文件"""
        logger.info(f"开始下载文件: {filename}")
        try:
            # 首先发送 HEAD 请求获取文件大小
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: requests.head(url, headers=self.headers)
            )
            response.raise_for_status()
            file_size = int(response.headers.get('content-length', 0))
            
            if file_size == 0:
                raise ValueError("无法获取文件大小")

            # 计算分段大小，这里设置为5MB每段
            chunk_size = 5 * 1024 * 1024  # 5MB
            chunks = [(i, min(i + chunk_size - 1, file_size - 1)) 
                     for i in range(0, file_size, chunk_size)]
            
            logger.debug(f"文件大小: {file_size/1024/1024:.2f}MB, 分为{len(chunks)}段下载")

            async def download_chunk(start: int, end: int) -> Tuple[int, bytes]:
                """下载指定范围的数据"""
                chunk_headers = {**self.headers, 'Range': f'bytes={start}-{end}'}
                for _ in range(3):  # 重试3次
                    try:
                        response = await asyncio.get_event_loop().run_in_executor(
                            None,
                            lambda: requests.get(url, headers=chunk_headers, timeout=30)
                        )
                        response.raise_for_status()
                        return start, response.content
                    except Exception as e:
                        logger.warning(f"分段下载失败 ({start}-{end}): {e}, 重试中...")
                        await asyncio.sleep(1)
                raise Exception(f"分段 {start}-{end} 下载失败")

            # 创建临时文件
            temp_filename = f"{filename}.temp"
            with open(temp_filename, 'wb') as f:
                f.truncate(file_size)  # 预分配文件大小

            # 并发下载所有分段
            tasks = [download_chunk(start, end) for start, end in chunks]
            chunk_results = await asyncio.gather(*tasks, return_exceptions=True)

            # 写入分段数据
            with open(temp_filename, 'r+b') as f:
                for i, result in enumerate(chunk_results):
                    if isinstance(result, Exception):
                        raise result
                    start, data = result
                    f.seek(start)
                    f.write(data)

            # 重命名临时文件
            os.replace(temp_filename, filename)
            logger.info(f"下载完成: {filename}")
            return True

        except Exception as e:
            logger.error(f"下载失败: {filename}, 错误: {e}")
            # 清理临时文件
            if os.path.exists(f"{filename}.temp"):
                os.remove(f"{filename}.temp")
            return False

    async def download_part(self, bvid: str, cid: str) -> DownloadResult:
        """下载单个视频分P"""
        temp_dir = Path("temp") / f"{bvid}-{cid}"
        logger.debug(f"创建临时目录: {temp_dir.resolve()}")
        temp_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            # 1. 获取播放信息
            play_info = await self.get_play_info(bvid, cid)
            video_stream, audio_stream = self.find_best_stream(play_info)
            
            if not video_stream or not audio_stream:
                raise ValueError("没有找到有效的视频或音频流")

            # 2. 下载视频和音频
            video_path = temp_dir / "video.m4s"
            audio_path = temp_dir / "audio.m4s"
            
            logger.info(f"开始下载分P视频文件: bvid={bvid}, cid={cid}")
            await asyncio.gather(
                self.download_file(video_stream["baseUrl"], str(video_path)),
                self.download_file(audio_stream["baseUrl"], str(audio_path))
            )

            # 3. 合并文件
            output_path = self.download_path / f"{bvid}-{cid}.mp4"
            logger.info(f"使用 ffmpeg 合并视频和音频文件到: {output_path}")
            process = await asyncio.create_subprocess_exec(
                "ffmpeg", "-i", str(video_path), "-i", str(audio_path),
                "-c:v", "copy", "-c:a", "copy", "-y", str(output_path),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout_data, stderr_data = await process.communicate()
            if process.returncode != 0:
                logger.error(f"ffmpeg 合并失败，stderr: {stderr_data.decode('utf-8', 'ignore')}")
                raise RuntimeError("FFmpeg merge failed")
            else:
                logger.info(f"ffmpeg 合并成功: {output_path}")

            # 4. 清理临时文件
            logger.debug(f"清理临时文件: {temp_dir.resolve()}")
            for file in temp_dir.glob("*"):
                file.unlink()
            temp_dir.rmdir()

            return DownloadResult(
                bvid=bvid,
                cid=cid,
                file_path=str(output_path),
                success=True
            )

        except Exception as e:
            logger.exception(f"分P下载流程出现异常: bvid={bvid}, cid={cid}")
            return DownloadResult(
                bvid=bvid,
                cid=cid,
                file_path="",
                success=False,
                error=str(e)
            )

    async def download_video(self, bvid: str) -> List[DownloadResult]:
        """下载整个视频的所有分P"""
        logger.info(f"开始下载整个视频所有分P, bvid={bvid}")
        try:
            # 1. 获取所有分P
            parts = await self.get_video_parts(bvid)
            logger.info(f"共 {len(parts)} 个分P, 准备并行下载...")

            # 2. 并行下载所有分P
            tasks = [self.download_part(bvid, str(part["cid"])) for part in parts]
            results = await asyncio.gather(*tasks)
            
            logger.info(f"所有分P下载任务完成，bvid={bvid}")
            return results
            
        except Exception as e:
            logger.exception(f"下载视频出现异常, bvid={bvid}")
            return [DownloadResult(
                bvid=bvid,
                cid="unknown",
                file_path="",
                success=False,
                error=str(e)
            )]
