// bot.js - 抖音下载 Telegram Bot - 第一部分
const { Telegraf, Markup } = require('telegraf');
const { createClient } = require('@supabase/supabase-js');
const fetch = require('node-fetch');
const fs = require('fs/promises');
const fsSync = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const archiver = require('archiver');
const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);
// 添加dotenv加载环境变量
require('dotenv').config();

/**
 * 环境变量说明:
 * 
 * SUPABASE_URL - Supabase数据库URL
 * SUPABASE_KEY - Supabase匿名密钥
 * BOT_TOKEN - Telegram Bot Token
 * TELEGRAM_CHAT_ID - 存储频道ID，用于保存媒体文件
 * API_BASE_URL - API基本URL，默认为http://localhost:8080
 * 
 * 其他可选的环境变量:
 * TIKHUB_API_KEY - TikHub API密钥
 */

// =============== 硬编码 Bot Token ===============
const BOT_TOKEN = '6563835132:AAFKsMY5_arWjw26YqbAA_eycJ6jhEwbrao';

// =============== 环境变量校验 ===============
function checkRequiredEnvVars() {
  const requiredVars = [
    'SUPABASE_URL', 
    'SUPABASE_KEY',
    'BUTHISBOT', // 添加BUTHISBOT检查
    'TELEGRAM_CHAT_ID'
  ];
  
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.error('错误: 以下必要的环境变量未设置:');
    missingVars.forEach(varName => console.error(`- ${varName}`));
    console.error('请在.env文件中设置这些变量');
    process.exit(1);
  }
}

// 执行环境变量检查
checkRequiredEnvVars();

// =============== 日志配置 ===============
const logger = {
  info: (message) => console.log(`INFO: ${message}`),
  error: (message, error) => console.error(`ERROR: ${message}`, error),
  warning: (message, error) => console.warn(`WARNING: ${message}`, error)
};

// =============== Supabase 配置 ===============
// 从环境变量获取Supabase配置
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// =============== 全局变量 ===============
const DELETE_FILES = true;
// 从环境变量获取存储频道ID
const STORAGE_CHANNEL_ID = parseInt(process.env.TELEGRAM_CHAT_ID);
const AUTHOR_SEC_UID_MAP = {};
const RETRY_CACHE = {};
const MUSIC_CACHE = {};
// 从环境变量获取API基本URL
const API_BASE_URL = process.env.API_BASE_URL || "http://localhost:8080";

// =============== 抖音解析器 ===============
class DouyinParser {
  static _sortBitrateItems(bitRateList) {
    // Sort by resolution, FPS, and bitrate
    return bitRateList.sort((a, b) => {
      const aPlayAddr = a.play_addr || {};
      const bPlayAddr = b.play_addr || {};
      const aResolution = (aPlayAddr.width || 0) * (aPlayAddr.height || 0);
      const bResolution = (bPlayAddr.width || 0) * (bPlayAddr.height || 0);
      
      if (aResolution !== bResolution) return bResolution - aResolution;
      
      const aFps = a.FPS || 0;
      const bFps = b.FPS || 0;
      if (aFps !== bFps) return bFps - aFps;
      
      const aBitrate = a.bit_rate || 0;
      const bBitrate = b.bit_rate || 0;
      return bBitrate - aBitrate;
    });
  }

  static _getFirstUrl(urlData) {
    const urlList = urlData?.url_list || [];
    if (Array.isArray(urlList) && urlList.length > 0) {
      return urlList[0];
    }
    return "";
  }

  static _getBestPlayUrl(videoInfo) {
    const bitRateList = videoInfo?.bit_rate || [];
    if (bitRateList.length > 0) {
      const sortedItems = DouyinParser._sortBitrateItems(bitRateList);
      for (const item of sortedItems) {
        const playAddr = item.play_addr || {};
        const bestUrl = DouyinParser._getFirstUrl(playAddr);
        if (bestUrl && !bestUrl.includes("watermark")) {
          return bestUrl;
        }
      }
    }

    // Fallback to default play_addr
    const fallbackUrl = DouyinParser._getFirstUrl(videoInfo?.play_addr || {});
    if (fallbackUrl && !fallbackUrl.includes("watermark")) {
      return fallbackUrl;
    }
    return fallbackUrl;
  }

  static _getBestImageUrl(imgData) {
    const urlList = imgData?.url_list || [];
    // Prefer URLs without "water" (watermark)
    const noWaterUrls = urlList.filter(u => !u.includes("water"));
    if (noWaterUrls.length > 0) {
      return noWaterUrls[0];
    }
    if (urlList.length > 0) {
      return urlList[0];
    }
    return "";
  }

  static parseAweme(respData) {
    try {
      const data = respData?.data || {};
      if (typeof data !== 'object') {
        return {};
      }

      const awemeId = data.aweme_id || "";
      const desc = data.desc || "";
      const createTimeTs = data.create_time || 0;
      
      let createTimeStr = "";
      try {
        createTimeStr = new Date(createTimeTs * 1000).toISOString()
          .replace('T', ' ').slice(0, 19);
      } catch (e) {}

      const author = data.author || {};
      const authorInfo = {
        nickname: author.nickname || "",
        uid: author.uid || "",
        sec_uid: author.sec_uid || "",
        unique_id: author.unique_id || "",
        follower_count: author.follower_count || 0,
        total_favorited: author.total_favorited || 0,
      };

      // Location
      let locationInfo = {};
      const anchorInfo = data.anchor_info || {};
      try {
        if (typeof anchorInfo.extra === 'string') {
          const extraData = JSON.parse(anchorInfo.extra);
          if (typeof extraData === 'object') {
            const addressInfo = extraData.address_info || {};
            locationInfo = {
              province: addressInfo.province || "",
              city: addressInfo.city || ""
            };
          }
        }
      } catch (e) {}

      // Statistics
      const statistics = data.statistics || {};
      const stats = {
        comment_count: statistics.comment_count || 0,
        digg_count: statistics.digg_count || 0,
        collect_count: statistics.collect_count || 0,
        share_count: statistics.share_count || 0
      };

      // Music
      const music = data.music || {};
      const musicInfo = {
        title: music.title || "",
        author: music.author || "",
        play_url: DouyinParser._getFirstUrl(music.play_url || {})
      };

      // Media
      const images = data.images || [];
      const videoInfo = data.video || {};
      const durationMs = data.duration || 0;
      const durationS = durationMs / 1000.0;

      let mediaInfo;
      if (images.length > 0) {
        // Image collection
        mediaInfo = {
          cover_url: "",  // Image collections don't have a separate cover
          play_url: "",
          width: 0,
          height: 0,
          duration: durationS,
          images: []
        };
        
        for (const img of images) {
          const imgInfo = {
            url: DouyinParser._getBestImageUrl(img),
            width: img.width || 0,
            height: img.height || 0,
            video: null
          };
          
          const vidData = img.video;
          if (vidData && typeof vidData === 'object') {
            const bestPlayUrl = DouyinParser._getBestPlayUrl(vidData);
            imgInfo.video = {
              url: bestPlayUrl,
              width: vidData.width || 0,
              height: vidData.height || 0
            };
          }
          
          mediaInfo.images.push(imgInfo);
        }
      } else {
        // Regular video, use highest bitrate & no watermark
        mediaInfo = {
          cover_url: DouyinParser._getFirstUrl(videoInfo.cover || {}),
          play_url: DouyinParser._getBestPlayUrl(videoInfo),
          width: videoInfo.width || 0,
          height: videoInfo.height || 0,
          duration: durationS,
          images: []
        };
      }

      return {
        base_info: {
          aweme_id: awemeId,
          desc: desc,
          create_time: createTimeStr
        },
        author_info: authorInfo,
        location_info: locationInfo,
        statistics: stats,
        music_info: musicInfo,
        media_info: mediaInfo
      };
    } catch (error) {
      logger.error("parse_aweme error:", error);
      return {};
    }
  }

  static parseUser(respData) {
    try {
      const data = respData?.data || {};
      if (typeof data !== 'object') {
        return null;
      }
      
      const author = data.author || {};
      if (!author || !author.uid || !author.sec_uid) {
        return null;
      }
      
      // 提取与数据库表结构匹配的字段
      const userData = {
        uid: author.uid,
        sec_uid: author.sec_uid,
        short_id: author.short_id || null,
        unique_id: author.unique_id || null,
        
        // 基本信息
        nickname: author.nickname || null,
        signature: author.signature || null,
        user_age: author.user_age || null,
        avatar_thumb_uri: author.avatar_thumb?.uri || null,
        avatar_thumb_url: author.avatar_thumb?.url_list?.[0] || null,
        create_time: author.create_time ? new Date(author.create_time * 1000).toISOString() : null,
        
        // 统计信息
        follower_count: author.follower_count || 0,
        following_count: author.following_count || 0,
        total_favorited: author.total_favorited || 0,
        favoriting_count: author.favoriting_count || 0,
        
        // 账号状态
        status: author.status || 1,
        verification_type: author.verification_type || null,
        user_canceled: author.user_canceled || false,
        mate_add_permission: author.mate_add_permission || null,
        
        // 认证信息
        custom_verify: author.custom_verify || null,
        enterprise_verify_reason: author.enterprise_verify_reason || null,
        
        // 附加设置
        prevent_download: author.prevent_download || false,
        contacts_status: author.contacts_status || null,
        
        // 封面信息
        cover_url: author.cover_url?.[0]?.url_list?.[0] || null,
        
        // 最后获取时间
        last_fetched_at: new Date().toISOString()
      };
      
      return userData;
    } catch (error) {
      logger.error("parse_user error:", error);
      return null;
    }
  }
}

// =============== 解析和下载函数 ===============
function parseDouyinWork(respData) {
  return DouyinParser.parseAweme(respData);
}

function parseDouyinUser(respData) {
  return DouyinParser.parseUser(respData);
}

// 在API调用函数中添加重试机制的通用函数
async function retryApiCallWithBackoff(apiCall, maxRetries = 3, initialBackoffMs = 1000) {
  let retries = 0;
  let backoffTime = initialBackoffMs;
  
  while (retries < maxRetries) {
    try {
      return await apiCall();
    } catch (error) {
      // 检查是否是429错误（Too Many Requests）
      const is429 = error.status === 429 || 
                   (error.message && error.message.includes('429')) || 
                   (error.message && error.message.toLowerCase().includes('too many requests'));
      
      if (is429 && retries < maxRetries - 1) {
        // 针对429错误的指数退避重试
        retries++;
        logger.warning(`API限流(429)，第${retries}次重试，等待${backoffTime/1000}秒...`);
        await new Promise(resolve => setTimeout(resolve, backoffTime));
        // 指数增加退避时间
        backoffTime *= 2;
      } else {
        // 其他错误或已达到最大重试次数，抛出异常
        throw error;
      }
    }
  }
}

// 修改saveDouyinToDatabase函数，添加is_available字段
async function saveDouyinToDatabase(parsedData, isAvailable = true) {
  try {
    const baseInfo = parsedData?.base_info || {};
    const authorInfo = parsedData?.author_info || {};
    const locationInfo = parsedData?.location_info || {};
    const musicInfo = parsedData?.music_info || {};
    const mediaInfo = parsedData?.media_info || {};
    
    // 准备要插入的数据
    const data = {
      aweme_id: baseInfo.aweme_id || "",
      description: baseInfo.desc || "",
      create_time: baseInfo.create_time || "",
      nickname: authorInfo.nickname || "",
      uid: authorInfo.uid || "",
      sec_uid: authorInfo.sec_uid || "",
      unique_id: authorInfo.unique_id || "",
      follower_count: authorInfo.follower_count || 0,
      total_favorited: authorInfo.total_favorited || 0,
      province: locationInfo.province || "",
      city: locationInfo.city || "",
      music_title: musicInfo.title || "",
      music_author: musicInfo.author || "",
      music_play_url: musicInfo.play_url || "",
      cover_url: mediaInfo.cover_url || "",
      media_play_url: mediaInfo.play_url || "",
      duration: mediaInfo.duration || 0,
      file_id: "", // 初始化为空字符串，稍后更新
      large: null, // 修改large字段为空字符串，用于存储chatId列表 -> 改为null
      bot_token: process.env.BUTHISBOT // 添加bot_token字段
    };
    
    // 确保数据完整性
    if (!data.aweme_id) {
      logger.error("保存数据库失败: 缺少作品ID");
      return false;
    }
    
    // 先检查是否存在该记录
    const { data: existingRecord, error: checkError } = await supabase
      .from("douyin")
      .select("aweme_id")
      .eq("aweme_id", data.aweme_id)
      .limit(1);
    
    // 如果存在，先删除该记录
    if (!checkError && existingRecord && existingRecord.length > 0) {
      logger.info(`作品 ${data.aweme_id} 已存在，先删除旧记录`);
      const { error: deleteError } = await supabase
        .from("douyin")
        .delete()
        .eq("aweme_id", data.aweme_id);
      
      if (deleteError) {
        logger.error(`删除作品 ${data.aweme_id} 的旧记录失败: ${deleteError.message}`, deleteError);
        // 继续执行，尝试插入新记录
      }
    }
    
    // 插入新记录
    const { error: insertError } = await supabase
      .from("douyin")
      .insert([data]);
    
    if (insertError) {
      logger.error(`保存作品 ${data.aweme_id} 到数据库失败: ${insertError.message}`, insertError);
      return false;
    }
    
    logger.info(`成功保存作品 ${data.aweme_id} 到数据库${!isAvailable ? ' (标记为不可用)' : ''}`);
    return true;
  } catch (error) {
    logger.error(`保存数据库异常: ${error.message}`, error);
    return false;
  }
}

async function saveDouyinUserToDatabase(userData) {
  try {
    if (!userData || !userData.uid || !userData.sec_uid) {
      logger.error("保存用户数据失败: 缺少必要字段uid或sec_uid");
      return false;
    }
    
    // 使用upsert方法保存数据 (如果已存在则更新)
    const { error } = await supabase
      .from("douyin_user")
      .upsert(userData);
    
    if (error) {
      logger.error(`保存用户数据到Supabase失败: ${error.message}`, error);
      return false;
    }
    
    logger.info(`成功保存用户 ${userData.uid} (${userData.nickname || "未知昵称"}) 到数据库`);
    return true;
  } catch (error) {
    logger.error(`保存用户数据库异常: ${error.message}`, error);
    return false;
  }
}

// 新增：增强版保存用户数据函数，合并基础数据和个人资料数据
async function saveEnhancedUserToDatabase(baseUserData, profileData = null) {
  try {
    if (!baseUserData || !baseUserData.uid || !baseUserData.sec_uid) {
      logger.error("保存增强用户数据失败: 缺少必要的基础字段uid或sec_uid");
      return false;
    }
    
    // 合并两个数据源
    let userData = { ...baseUserData };
    
    // 如果有profileData，合并信息
    if (profileData && typeof profileData === 'object') {
      userData = { ...userData, ...profileData };
      logger.info(`合并用户 ${userData.uid} 的基础数据和个人资料数据`);
    }
    
    // 使用upsert方法保存数据 (如果已存在则更新)
    const { error } = await supabase
      .from("douyin_user")
      .upsert(userData);
    
    if (error) {
      logger.error(`保存增强用户数据到Supabase失败: ${error.message}`, error);
      return false;
    }
    
    logger.info(`成功保存增强用户 ${userData.uid} (${userData.nickname || "未知昵称"}) 到数据库`);
    return true;
  } catch (error) {
    logger.error(`保存增强用户数据库异常: ${error.message}`, error);
    return false;
  }
}

// =============== URL解析函数 ===============
function extractDouyinVideoUrl(text) {
  const pattern = /(https?:\/\/v\.douyin\.com\/\S+|https?:\/\/www\.douyin\.com\/video\/\S+|https?:\/\/www\.douyin\.com\/note\/\S+)/;
  const match = text.match(pattern);
  return match ? match[1].trim() : "";
}

function extractDouyinUserUrl(text) {
  const pattern = /(https:\/\/www\.douyin\.com\/user\/[^\s]+)/;
  const match = text.match(pattern);
  return match ? match[1].trim() : "";
}

function extractSecUserIdFromUserUrl(url) {
  const pattern = /https:\/\/www\.douyin\.com\/user\/([^/?]+)/;
  const match = url.match(pattern);
  return match ? match[1] : "";
}

// =============== 抖音API调用函数 ===============
async function fetchUserPostVideosApi(secUserId, maxCursor = 0, count = 40) {
  return retryApiCallWithBackoff(async () => {
    try {
      const baseUrl = `${API_BASE_URL}/api/douyin/web/fetch_user_post_videos`;
      const params = new URLSearchParams({
        sec_user_id: secUserId,
        max_cursor: maxCursor,
        count: count
      });
      
      const response = await fetch(`${baseUrl}?${params}`, { timeout: 30000 });
      
      // 检查429状态码
      if (response.status === 429) {
        throw { status: 429, message: "API限流，请求过于频繁" };
      }
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      logger.error(`fetch_user_post_videos_api error: ${error.message}`, error);
      throw error; // 重新抛出以便重试机制捕获
    }
  }, 3, 2000);
}

// 新增用户资料API调用函数
async function fetchUserProfileApi(secUserId) {
  return retryApiCallWithBackoff(async () => {
    try {
      const baseUrl = `${API_BASE_URL}/api/douyin/web/handler_user_profile`;
      const params = new URLSearchParams({
        sec_user_id: secUserId
      });
      
      logger.info(`[fetchUserProfileApi] 正在获取用户资料，sec_user_id: ${secUserId}`);
      const response = await fetch(`${baseUrl}?${params}`, { timeout: 30000 });
      
      // 检查429状态码
      if (response.status === 429) {
        throw { status: 429, message: "API限流，请求过于频繁" };
      }
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      logger.info(`[fetchUserProfileApi] 成功获取用户资料: ${secUserId}`);
      return data;
    } catch (error) {
      logger.error(`fetchUserProfileApi error: ${error.message}`, error);
      throw error; // 重新抛出以便重试机制捕获
    }
  }, 3, 2000);
}

// 解析用户资料数据
function parseUserProfileData(respData) {
  try {
    const user = respData?.data?.user;
    if (!user || typeof user !== 'object') {
      return null;
    }
    
    // 提取各类新增信息
    return {
      // 地理位置信息
      ip_location: user.ip_location || null,
      gender: user.gender,
      school_name: user.school_name,
      country: user.country || "",
      province: user.province || "",
      city: user.city || "",
      district: user.district || "",
      
      // 数据统计
      aweme_count: user.aweme_count || 0,
      max_follower_count: user.max_follower_count || 0,
      mplatform_followers_count: user.mplatform_followers_count || 0,
      mix_count: user.mix_count || 0,
      series_count: user.series_count || 0,
      dongtai_count: user.dongtai_count || 0,
      forward_count: user.forward_count || 0,
      public_collects_count: user.public_collects_count || 0,
      
      // 账号属性
      is_mix_user: user.is_mix_user || false,
      is_star: user.is_star || false,
      is_ban: user.is_ban || false,
      is_blocked: user.is_blocked || false,
      message_chat_entry: user.message_chat_entry || false,
      live_status: user.live_status || 0,
      has_e_account_role: user.has_e_account_role || false,
      is_effect_artist: user.is_effect_artist || false,
      is_gov_media_vip: user.is_gov_media_vip || false,
      is_activity_user: user.is_activity_user || false,
      is_series_user: user.is_series_user || false,
      secret: user.secret || 0,
      
      // 直播相关
      room_id: user.room_id || null,
      room_id_str: user.room_id_str || null,
      
      // 权限设置
      favorite_permission: user.favorite_permission || 0,
      follower_status: user.follower_status || 0,
      follow_status: user.follow_status || 0,
      show_favorite_list: user.show_favorite_list || false,
      show_subscription: user.show_subscription || false,
      
      // 封面信息
      cover_colour: user.cover_colour || null,
      
      // 商业相关
      commerce_user_level: user.commerce_user_level || 0,
      with_commerce_entry: user.with_commerce_entry || false,
      with_fusion_shop_entry: user.with_fusion_shop_entry || false,
      with_commerce_enterprise_tab_entry: user.with_commerce_enterprise_tab_entry || false,
      
      // 头像扩展
      avatar_larger_url: user.avatar_larger?.url_list?.[0] || null,
      avatar_thumb_url: user.avatar_thumb?.url_list?.[0] || null,
      avatar_medium_url: user.avatar_medium?.url_list?.[0] || null
    };
  } catch (error) {
    logger.error("parseUserProfileData error:", error);
    return null;
  }
}

async function fetchOneVideoApi(awemeId) {
  return retryApiCallWithBackoff(async () => {
    try {
      const baseUrl = `${API_BASE_URL}/api/hybrid/video_data`;
      const douyinUrl = `https://www.douyin.com/video/${awemeId}`;
      const params = new URLSearchParams({
        url: douyinUrl,
        minimal: "false"
      });
      
      const response = await fetch(`${baseUrl}?${params}`, { timeout: 30000 });
      
      // 检查429状态码
      if (response.status === 429) {
        throw { status: 429, message: "API限流，请求过于频繁" };
      }
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const jsonData = await response.json();
      
      // 检查作品是否不可用
      const isNotAvailable = (
        !jsonData || 
        !jsonData.data || 
        jsonData.status_code === 404 ||
        jsonData.status_code === 10000 ||
        (jsonData.data && jsonData.data.status_code === 404) ||
        (jsonData.data && jsonData.data.error_code === 10000) ||
        (jsonData.data && jsonData.data.aweme_id === undefined)
      );
      
      if (isNotAvailable) {
        logger.warning(`检测到不可用作品: ${awemeId}，将标记为not available`);
        
        // 创建一个最小化的数据对象，用于标记不可用作品
        const minimalData = {
          base_info: { aweme_id: awemeId, desc: "Not Available" },
          author_info: {},
          location_info: {},
          music_info: {},
          media_info: {}
        };
        
        // 保存到数据库，标记为不可用
        await saveDouyinToDatabase(minimalData, false);
        
        // 返回含有标记的结果
        return { 
          data: { aweme_id: awemeId },
          not_available: true,
          status_message: "作品不可用或已删除"
        };
      }
      
      return jsonData;
    } catch (error) {
      logger.error(`fetch_one_video_api error: ${error.message}`, error);
      throw error; // 重新抛出以便重试机制捕获
    }
  }, 3, 2000);
}

// 修改downloadFile函数，添加对429错误的特殊处理
async function downloadFile(url, maxRetries = 3, timeoutMs = 60000) {
  let currentRetry = 0;
  let backoffTime = 2000; // 初始等待时间2秒

  while (currentRetry < maxRetries) {
    try {
      logger.info(`[downloadFile] GET ${url}, attempt ${currentRetry + 1}`);
      
      // 创建带超时的fetch请求
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeoutMs);
      
      try {
        const response = await fetch(url, { 
          signal: controller.signal,
          timeout: timeoutMs
        });
        
        clearTimeout(timeoutId);
        
        // 检查429状态码
        if (response.status === 429) {
          // 特殊处理限流错误
          logger.warning(`[downloadFile] 遇到API限流(429)，等待${backoffTime/1000}秒后重试...`);
          await new Promise(resolve => setTimeout(resolve, backoffTime));
          backoffTime *= 2; // 指数增加退避时间
          currentRetry++;
          continue;
        }
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const buffer = await response.buffer();
        const contentType = response.headers.get("content-type") || "";
        
        return { 
          binary_data: buffer, 
          content_type: contentType, 
          size: buffer.length
        };
      } catch (fetchError) {
        clearTimeout(timeoutId);
        throw fetchError; // 重新抛出以便外层catch处理
      }
    } catch (error) {
      const isTimeout = error.name === 'AbortError' || error.type === 'aborted' || 
                        error.message.includes('timeout') || error.message.includes('Timeout');
      
      const is429 = error.status === 429 || 
                    (error.message && error.message.includes('429')) || 
                    (error.message && error.message.toLowerCase().includes('too many requests'));
      
      const errorMsg = isTimeout ? 
        `下载超时 (${timeoutMs/1000}秒)` : is429 ?
        `API限流(429)` : error.message;
      
      logger.warning(`[downloadFile] Error: ${errorMsg}`, error);
      
      if (currentRetry < maxRetries - 1) {
        // 对429错误使用更长的退避时间
        const waitSec = is429 ? Math.pow(2, currentRetry + 2) : Math.pow(2, currentRetry + 1);
        logger.info(`等待 ${waitSec} 秒后重试...`);
        await new Promise(resolve => setTimeout(resolve, waitSec * 1000));
        currentRetry++;
      } else if (currentRetry === maxRetries - 1) {
        logger.error(`[downloadFile] 达到最大重试次数 (${maxRetries})，下载失败: ${url}`);
        currentRetry++;
      }
    }
  }
  
  return null;
}

function buildCaptionForSingle(parsedData) {
  const base = parsedData?.base_info || {};
  const author = parsedData?.author_info || {};
  const stats = parsedData?.statistics || {};
  const music = parsedData?.music_info || {};
  const location = parsedData?.location_info || {};

  const lines = [];
  if (base.aweme_id) {
    lines.push(`作品ID: ${base.aweme_id}`);
  }
  if (base.desc) {
    lines.push(`描述: ${base.desc}`);
  }
  if (base.create_time) {
    lines.push(`发布时间: ${base.create_time}`);
  }
  if (author.nickname) {
    lines.push(`作者昵称: ${author.nickname}`);
  }
  if (author.unique_id) {
    lines.push(`抖音号: ${author.unique_id}`);
  }
  if (author.uid) {
    lines.push(`作者UID: ${author.uid}`);
  }

  const fc = author.follower_count;
  const tf = author.total_favorited;
  if (fc !== undefined && tf !== undefined) {
    lines.push(`粉丝数: ${fc} | 获赞: ${tf}`);
  }

  const province = location.province || "";
  const city = location.city || "";
  if (province || city) {
    lines.push(`地点: ${province} ${city}`.trim());
  }

  const digg = stats.digg_count;
  const cmt = stats.comment_count;
  const shr = stats.share_count;
  const col = stats.collect_count;
  const statsParts = [];
  
  if (digg !== undefined) {
    statsParts.push(`点赞: ${digg}`);
  }
  if (cmt !== undefined) {
    statsParts.push(`评论: ${cmt}`);
  }
  if (shr !== undefined) {
    statsParts.push(`分享: ${shr}`);
  }
  if (col !== undefined) {
    statsParts.push(`收藏: ${col}`);
  }
  
  if (statsParts.length > 0) {
    lines.push(statsParts.join(" | "));
  }

  if (music.title && music.author) {
    lines.push(`音乐: ${music.title} - ${music.author}`);
  }

  return lines.join("\n").trim();
}

// =============== 文件处理函数 ===============
function isImageFile(filename) {
  const ext = path.extname(filename.toLowerCase());
  return ['.jpg', '.jpeg', '.png', '.gif', '.webp'].includes(ext);
}

function isVideoFile(filename) {
  const ext = path.extname(filename.toLowerCase());
  return ['.mp4', '.mov', '.avi', '.mkv', '.webm'].includes(ext);
}

function isAudioFile(filename) {
  const ext = path.extname(filename.toLowerCase());
  return ['.mp3', '.wav', '.m4a', '.flac', '.aac'].includes(ext);
}

async function getVideoDimensions(videoPath) {
  try {
    const { stdout } = await execPromise(
      `ffprobe -v error -select_streams v:0 -show_entries stream=width,height -of json "${videoPath}"`
    );
    
    const data = JSON.parse(stdout);
    const stream = data.streams?.[0] || {};
    return [stream.width, stream.height];
  } catch (error) {
    logger.error(`Error getting video dimensions: ${error.message}`, error);
    return [null, null];
  }
}

async function getVideoFileSizeInMB(filePath) {
  try {
    const stats = await fs.stat(filePath);
    const fileSizeMB = stats.size / (1024 * 1024);
    return fileSizeMB;
  } catch (error) {
    logger.error(`获取文件大小失败: ${error.message}`, error);
    return 0;
  }
}

async function createZipArchive(files, outputPath) {
  return new Promise((resolve, reject) => {
    const output = fsSync.createWriteStream(outputPath);
    const archive = archiver('zip', { zlib: { level: 9 } });
    
    output.on('close', () => resolve(outputPath));
    archive.on('error', err => reject(err));
    
    archive.pipe(output);
    
    for (const file of files) {
      archive.file(file, { name: path.basename(file) });
    }
    
    archive.finalize();
  });
}

function chunkList(array, size) {
  const chunks = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}

// =============== 数据库相关函数 ===============
async function getOrCreateUser(chatId, username = "") {
  try {
    const { data, error } = await supabase
      .from("users2")
      .select("*")
      .eq("user_id", chatId)
      .limit(1);
    
    if (error) throw error;
    
    if (data && data.length > 0) {
      const existingUser = data[0];
      let updates = {};
      
      // 如果 username 不一致，更新一下
      if (username && existingUser.username !== username) {
        updates.username = username;
      }
      
      // 检查douyin字段，如果为空或不存在，则设置为tier0
      if (existingUser.douyin === null || existingUser.douyin === undefined || existingUser.douyin === '') {
        updates.douyin = "tier0";
        // 同时确保普通用户的配额存在
        if (existingUser.today === null || existingUser.today === undefined) {
          updates.today = 20;
        }
        if (existingUser.already === null || existingUser.already === undefined) {
          updates.already = 0;
        }
      }
      
      // 如果有需要更新的字段，执行更新操作
      if (Object.keys(updates).length > 0) {
        await supabase
          .from("users2")
          .update(updates)
          .eq("user_id", chatId);
        
        // 更新后重新获取用户信息，确保返回的是最新数据
        const { data: updatedData, error: updatedError } = await supabase
          .from("users2")
          .select("*")
          .eq("user_id", chatId)
          .limit(1);
          
        if (updatedError) throw updatedError;
        return updatedData?.[0] || existingUser; // 返回更新后的数据，失败则返回原始数据
      }
      
      // 如果没有需要更新的，直接返回现有用户数据
      return existingUser;
    } else {
      const insertData = {
        user_id: chatId,
        username: username,
        douyin: "tier0", // 设置默认为tier0（普通用户）
        today: 20,      // 默认每日处理限额为20个链接
        already: 0       // 初始化已使用数量为0
      };
      
      const { data: insertResult, error: insertError } = await supabase
        .from("users2")
        .insert([insertData])
        .select();
      
      if (insertError) throw insertError;
      
      return insertResult?.[0] || insertData;
    }
  } catch (error) {
    logger.error(`数据库查询/插入用户失败: ${error.message}`, error);
    return {
      user_id: chatId,
      username: username,
      douyin: "tier0", // 设置默认为tier0（普通用户）
      today: 20,       // 默认每日处理限额为20个链接
      already: 0        // 初始化已使用数量为0
    };
  }
}

async function updateUser(chatId, updates) {
  try {
    await supabase
      .from("users2")
      .update(updates)
      .eq("user_id", chatId);
  } catch (error) {
    logger.error(`更新用户 ${chatId} 信息失败: ${error.message}`, error);
  }
}

// 检查用户是否是premium用户
function isPremiumUser(userRecord) {
  return userRecord && userRecord.douyin === "tier1";
}

// 检查普通用户的订阅数量是否达到限制
async function checkSubscriptionLimit(chatId) {
  try {
    const { count, error } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("user_id", chatId);
    
    if (error) {
      logger.error(`检查用户 ${chatId} 订阅数量失败: ${error.message}`, error);
      return { success: false, message: "检查订阅数量失败，请稍后再试。" };
    }
    
    // 普通用户订阅上限为2个
    if (count >= 2) {
      return { 
        success: false, 
        message: "您是普通用户，最多可以订阅2个作者。升级到Premium可获得更高配额，请联系@juaer了解详情。" 
      };
    }
    
    return { success: true, count };
  } catch (error) {
    logger.error(`检查订阅数量异常: ${error.message}`, error);
    return { success: false, message: "检查订阅数量出错，请稍后再试。" };
  }
}

// =============== 数据库相关函数(续) ===============
async function saveDouyinToDatabase(parsedData) {
  try {
    const baseInfo = parsedData?.base_info || {};
    const authorInfo = parsedData?.author_info || {};
    const locationInfo = parsedData?.location_info || {};
    const musicInfo = parsedData?.music_info || {};
    const mediaInfo = parsedData?.media_info || {};
    
    // 准备要插入的数据
    const data = {
      aweme_id: baseInfo.aweme_id || "",
      description: baseInfo.desc || "",
      create_time: baseInfo.create_time || "",
      nickname: authorInfo.nickname || "",
      uid: authorInfo.uid || "",
      sec_uid: authorInfo.sec_uid || "",
      unique_id: authorInfo.unique_id || "",
      follower_count: authorInfo.follower_count || 0,
      total_favorited: authorInfo.total_favorited || 0,
      province: locationInfo.province || "",
      city: locationInfo.city || "",
      music_title: musicInfo.title || "",
      music_author: musicInfo.author || "",
      music_play_url: musicInfo.play_url || "",
      cover_url: mediaInfo.cover_url || "",
      media_play_url: mediaInfo.play_url || "",
      duration: mediaInfo.duration || 0,
      file_id: "", // 初始化为空字符串，稍后更新
      large: null, // 修改large字段为空字符串，用于存储chatId列表 -> 改为null
      bot_token: process.env.BUTHISBOT // 添加bot_token字段
    };
    
    // 确保数据完整性
    if (!data.aweme_id) {
      logger.error("保存数据库失败: 缺少作品ID");
      return false;
    }
    
    // 先检查是否存在该记录
    const { data: existingRecord, error: checkError } = await supabase
      .from("douyin")
      .select("aweme_id")
      .eq("aweme_id", data.aweme_id)
      .limit(1);
    
    // 如果存在，先删除该记录
    if (!checkError && existingRecord && existingRecord.length > 0) {
      logger.info(`作品 ${data.aweme_id} 已存在，先删除旧记录`);
      const { error: deleteError } = await supabase
        .from("douyin")
        .delete()
        .eq("aweme_id", data.aweme_id);
      
      if (deleteError) {
        logger.error(`删除作品 ${data.aweme_id} 的旧记录失败: ${deleteError.message}`, deleteError);
        // 继续执行，尝试插入新记录
      }
    }
    
    // 插入新记录
    const { error: insertError } = await supabase
      .from("douyin")
      .insert([data]);
    
    if (insertError) {
      logger.error(`保存作品 ${data.aweme_id} 到数据库失败: ${insertError.message}`, insertError);
      return false;
    }
    
    logger.info(`成功保存作品 ${data.aweme_id} 到数据库`);
    return true;
  } catch (error) {
    logger.error(`保存数据库异常: ${error.message}`, error);
    return false;
  }
}

async function saveDouyinUserToDatabase(userData) {
  try {
    if (!userData || !userData.uid || !userData.sec_uid) {
      logger.error("保存用户数据失败: 缺少必要字段uid或sec_uid");
      return false;
    }
    
    // 使用upsert方法保存数据 (如果已存在则更新)
    const { error } = await supabase
      .from("douyin_user")
      .upsert(userData);
    
    if (error) {
      logger.error(`保存用户数据到Supabase失败: ${error.message}`, error);
      return false;
    }
    
    logger.info(`成功保存用户 ${userData.uid} (${userData.nickname || "未知昵称"}) 到数据库`);
    return true;
  } catch (error) {
    logger.error(`保存用户数据库异常: ${error.message}`, error);
    return false;
  }
}

// 新增：增强版保存用户数据函数，合并基础数据和个人资料数据
async function saveEnhancedUserToDatabase(baseUserData, profileData = null) {
  try {
    if (!baseUserData || !baseUserData.uid || !baseUserData.sec_uid) {
      logger.error("保存增强用户数据失败: 缺少必要的基础字段uid或sec_uid");
      return false;
    }
    
    // 合并两个数据源
    let userData = { ...baseUserData };
    
    // 如果有profileData，合并信息
    if (profileData && typeof profileData === 'object') {
      userData = { ...userData, ...profileData };
      logger.info(`合并用户 ${userData.uid} 的基础数据和个人资料数据`);
    }
    
    // 使用upsert方法保存数据 (如果已存在则更新)
    const { error } = await supabase
      .from("douyin_user")
      .upsert(userData);
    
    if (error) {
      logger.error(`保存增强用户数据到Supabase失败: ${error.message}`, error);
      return false;
    }
    
    logger.info(`成功保存增强用户 ${userData.uid} (${userData.nickname || "未知昵称"}) 到数据库`);
    return true;
  } catch (error) {
    logger.error(`保存增强用户数据库异常: ${error.message}`, error);
    return false;
  }
}

// =============== 延迟删除消息函数 ===============
async function cleanupMessages(ctx, processMsg, delay = 5.0) {
  await new Promise(resolve => setTimeout(resolve, delay * 1000));
  
  // 尝试删除处理进度消息
  try {
    await ctx.telegram.deleteMessage(ctx.chat.id, processMsg.message_id);
  } catch (error) {
    logger.warning(`删除进度消息失败: ${error.message}`);
  }
  
  // 尝试删除用户的原输入
  try {
    await ctx.telegram.deleteMessage(ctx.chat.id, ctx.message.message_id);
  } catch (error) {
    logger.warning(`删除用户消息失败: ${error.message}`);
  }
}

// 区分file_id类型的函数
function detectFileType(fileId) {
  if (!fileId || typeof fileId !== 'string') return null; // Basic validation

  if (fileId.startsWith('AgACAg')) {
    return 'photo';
  } else if (fileId.startsWith('BAACAg')) {
    return 'video';
  } else if (fileId.startsWith('BQACAg') || fileId.startsWith('BQADAg') || fileId.startsWith('BQACAgU')) { // Added BQACAgU
    return 'document';
  } else if (fileId.startsWith('CQADAg') || fileId.startsWith('CQACAgU')) { // Keep the audio update
    return 'audio';
  } else if (fileId.startsWith('AwADAg')) {
    return 'voice';
  } else if (fileId.startsWith('CAADAg')) {
    return 'sticker';
  } else if (fileId.startsWith('CgADAg')) {
    return 'animation';
  } else if (fileId.startsWith('DQADAg')) {
    return 'video_note';
  }
  // 如果无法识别，可以取消下面的注释来记录未知前缀，以便未来进一步增强兼容性
  // logger.warning(`无法识别的文件类型，file_id 前缀: ${fileId.substring(0, 10)}...`);
  return null;
}

// =============== 媒体发送函数 ===============
async function sendSingleFile(ctx, filePath, caption, replyMarkup = null) {
  try {
    if (!fsSync.existsSync(filePath) || fsSync.statSync(filePath).size === 0) {
      logger.info(`文件无效或大小为0: ${filePath}`);
      return null;
    }

    // 检查视频文件大小，如果太大则放弃处理
    if (isVideoFile(filePath)) {
      const fileSizeMB = await getVideoFileSizeInMB(filePath);
      
      if (fileSizeMB > 49) {
        // 修改：超过49MB的视频文件不再尝试分割，直接放弃处理
        logger.info(`文件 ${filePath} 大小超过49MB (${fileSizeMB.toFixed(2)}MB)，放弃处理`);
        return null;
      }
      
      // 为所有视频生成缩略图，不只是大于9MB的
      logger.info(`为视频 ${filePath} 生成缩略图`);
      const thumbnailPath = await extractVideoThumbnail(filePath);
      
      const [width, height] = await getVideoDimensions(filePath);
      const options = { 
        caption: caption, 
        width: width,
        height: height,
        parse_mode: 'HTML'
      };
      
      // 如果有缩略图，添加到选项中
      if (thumbnailPath && fsSync.existsSync(thumbnailPath)) {
        options.thumb = { source: thumbnailPath };
      }
      
      // 发送到存储频道
      const message = await ctx.telegram.sendVideo(
        STORAGE_CHANNEL_ID,
        { source: filePath }, 
        options
      );
      
      // 删除缩略图（如果存在）
      if (thumbnailPath && fsSync.existsSync(thumbnailPath)) {
        try {
          await fs.unlink(thumbnailPath);
        } catch (err) {
          logger.warning(`删除缩略图失败: ${err.message}`);
        }
      }
      
      return message?.video?.file_id || null;
    } else if (isImageFile(filePath)) {
      const options = { 
        caption: caption, 
        parse_mode: 'HTML'
      };
      
      // 发送到存储频道
      const message = await ctx.telegram.sendPhoto(
        STORAGE_CHANNEL_ID,
        { source: filePath }, 
        options
      );
      
      return message?.photo ? message.photo[message.photo.length - 1]?.file_id : null;
    } else if (isAudioFile(filePath)) {
      const options = { 
        caption: caption, 
        parse_mode: 'HTML'
      };
      
      // 发送到存储频道
      const message = await ctx.telegram.sendAudio(
        STORAGE_CHANNEL_ID,
        { source: filePath }, 
        options
      );
      
      return message?.audio?.file_id || null;
    } else {
      const options = { 
        caption: caption, 
        parse_mode: 'HTML'
      };
      
      // 发送到存储频道
      const message = await ctx.telegram.sendDocument(
        STORAGE_CHANNEL_ID,
        { source: filePath }, 
        options
      );
      
      return message?.document?.file_id || null;
    }
  } catch (error) {
    logger.error(`sendSingleFile error: ${error.message}`, error);
    
    // 如果是413错误(Entity Too Large)，尝试作为文档发送
    if (error.message && (error.message.includes('413') || error.message.includes('too large'))) {
      try {
        const options = { 
          caption: `${caption}\n[文件太大，作为文档发送]`,
          parse_mode: 'HTML'
        };
        
        // 发送到存储频道
        const message = await ctx.telegram.sendDocument(
          STORAGE_CHANNEL_ID,
          { source: filePath }, 
          options
        );
        
        return message?.document?.file_id || null;
      } catch (docError) {
        logger.error(`尝试作为文档发送也失败: ${docError.message}`, docError);
      }
    }
    return null;
  }
}

// 通过file_id向用户发送媒体的函数 - 重构版本
async function sendMediaByFileIds(ctx, fileIds, caption, markup = null) {
  if (!fileIds || fileIds.length === 0) {
    return;
  }
  
  // 将分号分隔的file_id字符串转为数组
  const fileIdArray = Array.isArray(fileIds) ? fileIds : fileIds.split(';').filter(id => id);
  
  if (fileIdArray.length === 0) {
    return;
  }
  
  const chatId = ctx.chat.id; // Extract chat ID
  
  try {
    // 检查是否发送到存储频道
    const isStorageChannel = (chatId === STORAGE_CHANNEL_ID);
    
    // 如果是发送到存储频道，则发送所有文件类型
    if (isStorageChannel) {
      for (const fileId of fileIdArray) {
        const fileType = detectFileType(fileId);
        // 使用 ctx.telegram.send... 方法
        if (fileType === 'photo') {
          await ctx.telegram.sendPhoto(chatId, fileId);
        } else if (fileType === 'video') {
          await ctx.telegram.sendVideo(chatId, fileId);
        } else if (fileType === 'document') {
          await ctx.telegram.sendDocument(chatId, fileId);
        } else if (fileType === 'audio') {
          await ctx.telegram.sendAudio(chatId, fileId);
        } else if (fileType === 'voice') {
          await ctx.telegram.sendVoice(chatId, fileId);
        } else if (fileType === 'sticker') {
          await ctx.telegram.sendSticker(chatId, fileId);
        } else if (fileType === 'animation') {
          await ctx.telegram.sendAnimation(chatId, fileId);
        } else if (fileType === 'video_note') {
          await ctx.telegram.sendVideoNote(chatId, fileId);
        } else {
          // 默认作为文档发送
          await ctx.telegram.sendDocument(chatId, fileId);
        }
      }
      return;
    }
    
    // 对于普通用户，按类型分类文件
    const photoIds = [];
    const videoIds = [];
    const zipIds = [];
    const audioIds = [];
    const otherIds = [];
    
    for (const fileId of fileIdArray) {
      const fileType = detectFileType(fileId);
      if (fileType === 'photo') {
        photoIds.push(fileId);
      } else if (fileType === 'video') {
        videoIds.push(fileId);
      } else if (fileType === 'document') {
        zipIds.push(fileId);
      } else if (fileType === 'audio') {
        audioIds.push(fileId);
      } else {
        otherIds.push(fileId);
      }
    }
    
    // 合并所有媒体ID (照片和视频)
    const mediaIds = [...photoIds, ...videoIds];
    
    // 如果有媒体文件，以媒体组形式发送 (Telegram限制每组最多10个)
    let mediaSent = false;
    for (let i = 0; i < mediaIds.length; i += 10) {
      const chunk = mediaIds.slice(i, i + 10);
      
      if (chunk.length === 1) {
        // 单个媒体文件 - 使用 ctx.telegram.send...
        const fileId = chunk[0];
        const fileType = detectFileType(fileId);
        
        if (fileType === 'photo') {
          await ctx.telegram.sendPhoto(chatId, fileId);
          mediaSent = true;
        } else if (fileType === 'video') {
          await ctx.telegram.sendVideo(chatId, fileId);
          mediaSent = true;
        }
      } else if (chunk.length > 1) {
        // 多个媒体文件 - 使用 ctx.telegram.sendMediaGroup
        const mediaGroup = chunk.map(fileId => {
          const fileType = detectFileType(fileId);
          return {
            type: fileType === 'photo' ? 'photo' : 'video',
            media: fileId
          };
        });
        
        await ctx.telegram.sendMediaGroup(chatId, mediaGroup);
        mediaSent = true;
      }
    }
    
    // 单独发送caption和按钮 (只有在发送了媒体或有caption的情况下才发送)
    if (caption || mediaSent) { // Avoid sending only buttons if no media was sent and no caption
      if (caption) { 
        await ctx.telegram.sendMessage(chatId, caption, {
          parse_mode: 'HTML',
          ...(markup ? markup : {})
        });
      } else if (markup) { // Send markup only if there is one and media was sent
        await ctx.telegram.sendMessage(chatId, "_", { // Send a minimal text with markup
          parse_mode: 'HTML',
          ...(markup ? markup : {})
        });
      }
    }
    
  } catch (error) {
    logger.error(`通过file_id发送媒体失败: ${error.message}`, error);
    
    // 如果媒体组发送失败，尝试单个发送
    try {
      // 只向用户发送照片和视频
      let sentAny = false;
      for (const fileId of fileIdArray) {
        const fileType = detectFileType(fileId);
        
        // 使用 ctx.telegram.send...
        if (fileType === 'photo') {
          await ctx.telegram.sendPhoto(chatId, fileId);
          sentAny = true;
        } else if (fileType === 'video') {
          await ctx.telegram.sendVideo(chatId, fileId);
          sentAny = true;
        }
        // 忽略其他类型文件
      }
      
      // 单独发送caption和按钮
      if (caption || sentAny) { // Check if caption exists or media was sent
        if (caption) {
          await ctx.telegram.sendMessage(chatId, caption, {
            parse_mode: 'HTML',
            ...(markup ? markup : {})
          });
        } else if (markup) { // Send markup only if media was sent
          await ctx.telegram.sendMessage(chatId, "_", { // Send a minimal text with markup
            parse_mode: 'HTML',
            ...(markup ? markup : {})
          });
        }
      } else { // If nothing was sent (no media, no caption)
        await ctx.telegram.sendMessage(chatId, "无法发送媒体文件，请稍后再试。", {
            ...(markup ? markup : {})
        });
      }
    } catch (fallbackError) {
      logger.error(`单个发送也失败: ${fallbackError.message}`, fallbackError);
      
      // 最后尝试只发送caption和按钮
      try {
        if (caption) {
          await ctx.telegram.sendMessage(chatId, caption, {
            parse_mode: 'HTML',
            ...(markup ? markup : {})
          });
        } else {
          await ctx.telegram.sendMessage(chatId, "发送媒体时出错，请稍后再试。");
        }
      } catch (finalError) {
        logger.error(`发送说明文本也失败: ${finalError.message}`, finalError);
      }
    }
  }
}

async function sendMediaFiles(
  ctx,
  mediaFiles,
  douyinUrl,
  captionText,
  secUid,
  musicLink = "",
  fromAuthorPosts = false,
  userSettings = null,
  awemeId = null // Ensure awemeId is passed correctly
) {
  const chatId = ctx.chat?.id; // 获取chatId用于large字段

  // 0. 检查媒体文件数量
  if (mediaFiles.length > 20) {
    logger.info(`媒体文件数量 (${mediaFiles.length}) 超过 20，将标记为large`);
    if (awemeId) {
      try {
        await updateLargeField(ctx, awemeId, "媒体文件数量过多");
        return ""; // 中止处理
      } catch (dbError) {
        logger.error(`更新large字段时出错 (媒体文件数量过多): ${dbError.message}`, dbError);
        return "";
      }
    }
    return ""; // 如果没有awemeId也中止
  }

  // 1. 检查单个文件大小
  const normalSizeFiles = [];
  let hasLargeIndividualFile = false;
  for (const file of mediaFiles) {
    if (!fsSync.existsSync(file) || fsSync.statSync(file).size === 0) {
      continue;
    }
    const fileSizeMB = await getVideoFileSizeInMB(file);
    if (fileSizeMB > 49) {
      hasLargeIndividualFile = true;
      logger.info(`发现单个大文件: ${file}, 大小: ${fileSizeMB.toFixed(2)}MB, 将放弃处理`);
      break; // 发现一个大文件就无需再检查其他文件
    }
    normalSizeFiles.push(file);
  }

  // 如果发现单个大文件，更新数据库并退出
  if (hasLargeIndividualFile && awemeId) {
    try {
      await updateLargeField(ctx, awemeId, "单个文件过大");
      return ""; // 中止处理
    } catch (dbError) {
      logger.error(`更新large字段时出错 (单个文件过大): ${dbError.message}`, dbError);
      return "";
    }
  }
  
  // 如果没有需要处理的文件（可能是过滤掉了大文件导致），直接退出
  if (normalSizeFiles.length === 0) {
    logger.warning(`作品 ${awemeId} 没有符合大小要求的文件可处理。`);
    return "";
  }

  // 2. 创建并检查ZIP文件大小
  let zipFileId = null;
  let zipPath = null;
  let hasLargeZipFile = false;
  try {
    const baseName = path.basename(normalSizeFiles[0]).split('_')[0] || "douyin";
    const outputDir = path.dirname(normalSizeFiles[0]);
    zipPath = path.join(outputDir, `${baseName}.zip`);
    await createZipArchive(normalSizeFiles, zipPath);
    
    if (fsSync.existsSync(zipPath) && fsSync.statSync(zipPath).size > 0) {
      const zipSizeMB = await getVideoFileSizeInMB(zipPath);
      if (zipSizeMB > 49) {
        hasLargeZipFile = true;
        logger.info(`ZIP文件大小(${zipSizeMB.toFixed(2)}MB)超过49MB，将放弃处理`);
      }
    } else {
      logger.warning(`创建的ZIP文件无效或大小为0: ${zipPath}`);
      // 可选：是否将这种情况视为错误并中止？目前假设继续，但不上传ZIP
      zipPath = null; // 确保不上传无效ZIP
    }
  } catch (error) {
    logger.error(`创建ZIP失败: ${error.message}`, error);
    zipPath = null; // 创建失败，不上传ZIP
  }

  // 如果ZIP文件过大，更新数据库并退出
  if (hasLargeZipFile && awemeId) {
    try {
      await updateLargeField(ctx, awemeId, "ZIP文件过大");
      // 清理已创建的过大ZIP文件
      if (DELETE_FILES && zipPath && fsSync.existsSync(zipPath)) {
         try { await fs.unlink(zipPath); } catch (e) { logger.warn(`清理过大ZIP文件失败: ${e.message}`); }
      }
      return ""; // 中止处理
    } catch (dbError) {
      logger.error(`更新large字段时出错 (ZIP文件过大): ${dbError.message}`, dbError);
      return "";
    }
  }

  // 3. 上传并记录File ID (只有当所有检查通过时才执行)
  const allFileIds = [];
  let audioFileId = null;

  try {
    // 上传有效的ZIP文件 (如果存在且大小合适)
    if (zipPath && fsSync.existsSync(zipPath)) {
      try {
        const message = await ctx.telegram.sendDocument(
          STORAGE_CHANNEL_ID,
          { source: zipPath },
          { caption: captionText, parse_mode: 'HTML' } // ZIP也带完整caption?
        );
        zipFileId = message?.document?.file_id;
        if (zipFileId) {
          allFileIds.push(zipFileId);
          logger.info(`成功上传ZIP文件并获取file_id: ${zipFileId}`);
        }
      } catch (zipUploadError) {
        logger.error(`上传ZIP文件失败: ${zipUploadError.message}`, zipUploadError);
      }
    }

    // 分离媒体文件类型
    const audioFiles = normalSizeFiles.filter(isAudioFile);
    const nonAudioFiles = normalSizeFiles.filter(file => !isAudioFile(file));
    const singleProcessVideos = [];
    const mediaGroupFiles = [];

    for (const filePath of nonAudioFiles) {
      if (isVideoFile(filePath)) {
        const fileSizeMB = await getVideoFileSizeInMB(filePath);
        if (fileSizeMB >= 9 && fileSizeMB <= 49) {
          singleProcessVideos.push(filePath);
        } else {
          mediaGroupFiles.push(filePath);
        }
      } else {
        mediaGroupFiles.push(filePath);
      }
    }

    // 上传需要单独处理的视频
    for (const videoPath of singleProcessVideos) {
      try {
        const fileId = await sendSingleFile(ctx, videoPath, ""); // 单独发送不带caption
        if (fileId) {
          allFileIds.push(fileId);
        }
      } catch (singleVideoError) {
        logger.error(`单独处理视频 ${videoPath} 失败: ${singleVideoError.message}`, singleVideoError);
      }
    }

    // 上传媒体组文件
    if (mediaGroupFiles.length > 0) {
      for (const chunk of chunkList(mediaGroupFiles, 10)) {
        try {
          const mediaGroup = await Promise.all(chunk.map(async (filePath) => {
             if (isImageFile(filePath)) {
               return { type: 'photo', media: { source: filePath } };
             } else if (isVideoFile(filePath)) {
               const [width, height] = await getVideoDimensions(filePath);
               return { type: 'video', media: { source: filePath }, width: width, height: height };
             } else {
               return { type: 'document', media: { source: filePath } };
             }
           }));
           const mediaGroupResult = await ctx.telegram.sendMediaGroup(STORAGE_CHANNEL_ID, mediaGroup);
           if (Array.isArray(mediaGroupResult)) {
             for (const msg of mediaGroupResult) {
               let fileId = null;
               if (msg.photo?.length > 0) fileId = msg.photo[msg.photo.length - 1].file_id;
               else if (msg.video) fileId = msg.video.file_id;
               else if (msg.document) fileId = msg.document.file_id;
               if (fileId) allFileIds.push(fileId);
             }
           }
        } catch (error) {
          logger.error(`发送媒体组出错: ${error.message}`, error);
          // Fallback: try sending individually
          for (const filePath of chunk) {
            try {
              const fileId = await sendSingleFile(ctx, filePath, "");
              if (fileId) allFileIds.push(fileId);
            } catch (singleSendError) {
              logger.error(`单个发送文件失败 ${filePath}: ${singleSendError.message}`, singleSendError);
            }
          }
        }
      }
    }

    // 上传音频文件
    for (const audioFile of audioFiles) {
      try {
        const audioFileCaption = audioFiles.length === 1 && normalSizeFiles.length === 1 ? captionText : `音乐文件 - ${path.basename(audioFile)}`;
        const fileId = await sendSingleFile(ctx, audioFile, audioFileCaption);
        if (fileId) {
          allFileIds.push(fileId);
          audioFileId = fileId; // 保存最后一个音频的file_id
        }
      } catch (audioError) {
        logger.error(`发送音频文件失败 ${audioFile}: ${audioError.message}`, audioError);
      }
    }

    // --- 更新数据库 File ID ---
    if (allFileIds.length > 0 && awemeId) {
      const uniqueFileIds = [...new Set(allFileIds)];
      const fileIdString = uniqueFileIds.join(';');
      try {
         const { error } = await supabase
          .from("douyin")
          .update({ file_id: fileIdString })
          .eq("aweme_id", awemeId);
        if (error) logger.error(`更新作品 ${awemeId} 的file_id失败: ${error.message}`, error);
        else logger.info(`成功更新作品 ${awemeId} 的file_id: ${fileIdString}`);
      } catch (dbUpdateError) {
         logger.error(`更新作品 ${awemeId} 的file_id时发生异常: ${dbUpdateError.message}`, dbUpdateError);
      }
    } else if (awemeId) {
      logger.warning(`作品 ${awemeId} 没有收集到任何有效的 file_id，数据库未更新。`);
    }

    // --- 创建消息按钮 --- (这部分逻辑不变)
    let markup;
    const chunkArray = (array, size) => {
        const result = [];
        for (let i = 0; i < array.length; i += size) {
            result.push(array.slice(i, i + size));
        }
        return result;
    };
  
    if (fromAuthorPosts) {
        const buttons = [];
        buttons.push(Markup.button.url("打开", douyinUrl));
        if (audioFileId && awemeId) buttons.push(Markup.button.callback("获取音乐", `music:${awemeId}`));
        if (zipFileId && awemeId) buttons.push(Markup.button.callback("获取文件", `zip:${awemeId}`));
        const truncatedSecUid = secUid && secUid.length > 40 ? secUid.substring(0, 40) : secUid;
        buttons.push(Markup.button.callback("订阅", `sub:${truncatedSecUid}`));
        markup = Markup.inlineKeyboard(chunkArray(buttons, 3));
    } else {
        const buttons = [];
        buttons.push(Markup.button.url("打开", douyinUrl));
        if (audioFileId && awemeId) buttons.push(Markup.button.callback("获取音乐", `music:${awemeId}`));
        if (zipFileId && awemeId) buttons.push(Markup.button.callback("获取文件", `zip:${awemeId}`));
        if (secUid) {
            const shortId = uuidv4().substring(0, 8);
            AUTHOR_SEC_UID_MAP[shortId] = secUid;
            buttons.push(Markup.button.callback("订阅", `sub:${shortId}`));
        }
        markup = Markup.inlineKeyboard(chunkArray(buttons, 3));
    }

    // --- 使用收集到的file_id向用户发送媒体 ---
    if (allFileIds.length > 0) {
      const finalFileIdsToSend = [...new Set(allFileIds)].filter(id => id);
      if (finalFileIdsToSend.length > 0) {
        await sendMediaByFileIds(ctx, finalFileIdsToSend, captionText, markup);
        return finalFileIdsToSend.join(';');
      } else {
         logger.warning(`作品 ${awemeId} 没有有效的 file_id 可发送给用户 ${chatId}`);
         return "";
      }
    }

    logger.warning(`作品 ${awemeId} 处理完成，但没有收集到任何 file_id 可发送。`);
    return "";

  } finally {
    // 清理ZIP文件（无论成功失败都尝试清理）
    if (DELETE_FILES && zipPath && fsSync.existsSync(zipPath)) {
      try { await fs.unlink(zipPath); } catch (e) { logger.warn(`清理ZIP文件失败: ${e.message}`); }
    }
  }
}

// 辅助函数：更新large字段
async function updateLargeField(ctx, awemeId, reason) {
  const chatId = ctx.chat?.id;
  if (!chatId) {
      logger.warning(`无法更新 large 字段，因为缺少 chatId (awemeId: ${awemeId})`);
      return;
  }
  
  try {
    // 先查询当前的large字段值
    const { data: currentData, error: queryError } = await supabase
      .from("douyin")
      .select("large")
      .eq("aweme_id", awemeId)
      .single();
    
    if (queryError && queryError.code !== 'PGRST116') { // PGRST116 = no rows found
      logger.error(`查询作品 ${awemeId} 的large字段失败: ${queryError.message}`, queryError);
      // 即使查询失败也尝试更新
    }
    
    // 准备新的large字段值
    let newLargeValue = null; // 初始化为null
    const currentChatIdStr = String(chatId);
    const currentValue = currentData?.large; // 获取当前值，可能为 null 或字符串

    if (currentValue) { // 如果当前值存在且不为null/undefined/空字符串
      const chatIds = currentValue.split(';').filter(id => id); // 分割并过滤空字符串
      if (!chatIds.includes(currentChatIdStr)) {
        chatIds.push(currentChatIdStr);
        newLargeValue = chatIds.join(';');
      } else {
        newLargeValue = currentValue; // ID已存在，保持不变
      }
    } else {
      // 如果当前值是null或空字符串，直接设置为新的chatId
      newLargeValue = currentChatIdStr;
    }
    
    // 更新large字段
    const { error: updateError } = await supabase
      .from("douyin")
      .update({ large: newLargeValue }) // 使用 newLargeValue (可能是字符串或null)
      .eq("aweme_id", awemeId);
      
    if (updateError) {
      logger.error(`更新作品 ${awemeId} 的large字段失败 (${reason}): ${updateError.message}`, updateError);
      throw updateError; // 抛出错误以便上层catch
    } else {
      // 根据newLargeValue是否为null调整日志信息
      const logValue = newLargeValue ? `"${newLargeValue}"` : "null";
      logger.info(`已将作品 ${awemeId} 的large字段更新为 ${logValue} (原因: ${reason})`);
      // 发送通知给用户
      if (ctx.reply) { // 确保ctx有reply方法
          try {
            await ctx.reply("文件过大，后台处理中，请稍后再查看。");
          } catch (replyError) {
            logger.error(`发送大文件通知失败: ${replyError.message}`, replyError);
          }
      }
    }
  } catch (error) {
      logger.error(`执行 updateLargeField 时发生未预料错误 (awemeId: ${awemeId}): ${error.message}`, error);
      // 决定是否重新抛出错误，目前仅记录日志
      // throw error; 
  }
}

// =============== Realtime监听相关函数 ===============
async function initRealtimeListener(bot) {
  try {
    logger.info("正在初始化Realtime监听...");

    // 监听douyin表中file_id字段的变化
    const channel = supabase
      .channel('douyin_file_id_changes')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'douyin',
          filter: "file_id=neq.''", // 使用双引号避免转义问题
        },
        async (payload) => {
          try {
            const workData = payload.new;
            const aweme_id = workData.aweme_id;
            const file_id = workData.file_id;
            // 直接从 payload 获取 large 字段，但后续会重新查询以确保最新
            const initialLargeString = workData.large;

            if (!aweme_id || !file_id) {
              logger.warning(`检测到file_id更新，但缺少必要字段: aweme_id=${aweme_id}, file_id长度=${file_id ? file_id.length : 0}`);
              return;
            }

            logger.info(`检测到作品 ${aweme_id} 的 file_id 更新 (large初始值: "${initialLargeString}")`);

            // 重新获取作品完整信息，确保 large 字段是最新的
            const { data: workFullData, error: workError } = await supabase
              .from("douyin")
              .select("*") // 获取所有字段，包括 large 和 uid
              .eq("aweme_id", aweme_id)
              .limit(1);

            if (workError || !workFullData || workFullData.length === 0) {
              logger.error(`获取作品 ${aweme_id} 的完整信息失败: ${workError?.message || "无数据"}`, workError);
              return;
            }

            const work = workFullData[0];
            const uid = work.uid; // 获取 uid
            const actualLargeString = work.large; // 使用数据库中最新的 large 值

            // --- 确定发送目标 ---
            let targetChatIds = [];
            let sendToAllSubscribers = true;
            let clearLargeField = false;

            if (actualLargeString) {
              logger.info(`作品 ${aweme_id} large字段有内容: "${actualLargeString}", 将仅发送给这些用户。`);
              // 解析 large 字段中的 chat ID
              targetChatIds = actualLargeString.split(';')
                                            .map(id => id.trim()) // 去除空格
                                            .filter(id => id)       // 过滤空字符串
                                            .map(id => parseInt(id)) // 转换为数字
                                            .filter(id => !isNaN(id)); // 过滤无效数字

              if (targetChatIds.length > 0) {
                sendToAllSubscribers = false;
                clearLargeField = true; // 标记发送后需要清理 large 字段
                logger.info(`作品 ${aweme_id} 将发送给 ${targetChatIds.length} 个指定用户: ${targetChatIds.join(', ')}`);
              } else {
                 logger.warning(`作品 ${aweme_id} large字段 "${actualLargeString}" 解析后为空或无效, 将尝试按正常订阅逻辑发送。`);
                 // 回退到发送给所有订阅者
                 sendToAllSubscribers = true;
              }
            } else {
                logger.info(`作品 ${aweme_id} large字段为空，将按正常订阅逻辑发送。`);
            }

            // --- 如果需要发送给所有订阅者 ---
            if (sendToAllSubscribers) {
              if (!uid) {
                logger.warning(`作品 ${aweme_id} large字段为空，但缺少uid，无法查找订阅者`);
                return;
              }

              // 检查作者在douyin_user表中once字段是否为true (仅在非large发送时检查)
              const { data: userData, error: userError } = await supabase
                .from("douyin_user")
                .select("once, nickname") // 获取 once 和 nickname
                .eq("uid", uid)
                .limit(1);

              if (userError) {
                logger.error(`查询作者 ${uid} 的once状态失败: ${userError.message}`, userError);
                return;
              }

              if (!userData || userData.length === 0 || !userData[0].once) {
                const nickname = userData?.[0]?.nickname || "未知用户";
                logger.info(`作者 ${nickname} (${uid}) 的once字段不为true，不发送通知`);
                return;
              }

              const nickname = userData[0].nickname || "未知用户";
              logger.info(`作者 ${nickname} (${uid}) 的once字段为true，开始查找订阅用户`);

              // 查找所有订阅了该作者的用户
              const { data: subscribers, error: subsError } = await supabase
                .from("telegram_douyin_map")
                .select("user_id")
                .eq("uid", uid);

              if (subsError) {
                logger.error(`获取作者 ${uid} 的订阅用户失败: ${subsError.message}`, subsError);
                return;
              }

              if (!subscribers || subscribers.length === 0) {
                logger.info(`作者 ${nickname} (${uid}) 没有订阅用户，不处理`);
                return;
              }

              targetChatIds = subscribers.map(s => s.user_id);
              logger.info(`作者 ${nickname} (${uid}) 有 ${targetChatIds.length} 个订阅用户，开始发送更新...`);
            }

            // --- 执行发送逻辑 ---
            const caption = buildCaptionForSingle({
              base_info: {
                aweme_id: work.aweme_id,
                desc: work.description,
                create_time: work.create_time
              },
              author_info: {
                nickname: work.nickname,
                uid: work.uid,
                sec_uid: work.sec_uid,
                unique_id: work.unique_id,
                follower_count: work.follower_count,
                total_favorited: work.total_favorited
              },
              location_info: {
                province: work.province,
                city: work.city
              },
              music_info: {
                title: work.music_title,
                author: work.music_author,
                play_url: work.music_play_url
              }
            });

            const fileIdsToSend = work.file_id ? work.file_id.split(';').filter(id => id) : [];
            if (fileIdsToSend.length === 0) {
              logger.warning(`作品 ${aweme_id} 的file_id为空或无效，不发送`);
              return;
            }

            let zipId = null;
            // 查找ZIP文件ID
            for (const id of fileIdsToSend) {
              if (detectFileType(id) === 'document') {
                zipId = id;
                break;
              }
            }

            // 按钮分组辅助函数
            const chunkArray = (array, size) => {
              const result = [];
              for (let i = 0; i < array.length; i += size) {
                result.push(array.slice(i, i + size));
              }
              return result;
            };

            // 遍历所有目标用户，发送更新
            for (const chatId of targetChatIds) {
              // 跳过向存储频道发送
              if (chatId === STORAGE_CHANNEL_ID) {
                logger.info(`跳过向 STORAGE_CHANNEL_ID (${STORAGE_CHANNEL_ID}) 发送作品 ${aweme_id}`);
                continue;
              }

              try {
                // 构建按钮
                const buttons = [];
                buttons.push(Markup.button.url("打开", `https://www.douyin.com/video/${work.aweme_id}`));

                // 音乐按钮
                if (work.music_play_url) {
                  buttons.push(Markup.button.callback("获取音乐", `music:${work.aweme_id}`));
                }

                // ZIP按钮
                if (zipId) {
                  buttons.push(Markup.button.callback("获取文件", `zip:${work.aweme_id}`));
                }

                // 每行最多4个按钮
                const markup = Markup.inlineKeyboard(chunkArray(buttons, 4));

                // 发送媒体通知 (确保 sendMediaByFileIds 不发送 ZIP 文件)
                await sendMediaByFileIds(
                  { telegram: bot.telegram, chat: { id: chatId }, reply: (text, extra) => bot.telegram.sendMessage(chatId, text, extra) },
                  fileIdsToSend.filter(id => id !== zipId), // 发送除 ZIP 之外的媒体
                  caption,
                  markup
                );

                logger.info(`已向用户 ${chatId} 发送作品 ${aweme_id} 的更新`);
              } catch (userError) {
                logger.error(`向用户 ${chatId} 发送作品 ${aweme_id} 失败: ${userError.message}`, userError);
                // 即使单个用户发送失败，也继续处理其他用户和清理 large 字段
              }
            }

            // --- 清理 large 字段 ---
            if (clearLargeField) {
              logger.info(`尝试清理作品 ${aweme_id} 的 large 字段`);
              try {
                const { error: updateError } = await supabase
                  .from("douyin")
                  .update({ large: null }) // 设置为 null
                  .eq("aweme_id", aweme_id);
                if (updateError) {
                    logger.error(`清理作品 ${aweme_id} 的large字段失败: ${updateError.message}`, updateError);
                } else {
                    logger.info(`成功清理作品 ${aweme_id} 的large字段`);
                }
              } catch (clearError) {
                 logger.error(`清理作品 ${aweme_id} 的large字段时发生异常: ${clearError.message}`, clearError);
              }
            }

          } catch (error) {
            logger.error(`处理file_id变化事件出错: ${error.message}`, error);
          }
        }
      )
      .subscribe();

    logger.info("Realtime监听初始化成功");
    return channel;
  } catch (error) {
    logger.error(`初始化Realtime监听失败: ${error.message}`, error);
    return null;
  }
}

// =============== 创建 Bot 实例 ===============
// 从环境变量获取BOT_TOKEN
const bot = new Telegraf(process.env.BUTHISBOT);

// =============== 命令处理 ===============
bot.command('start', async (ctx) => {
  // --- 增加判断，忽略 STORAGE_CHANNEL_ID 的命令 ---
  if (ctx.chat?.id === STORAGE_CHANNEL_ID) { return; }
  // --- ---
  const chatId = ctx.chat.id;
  const username = ctx.from.username || "";
  const userRecord = await getOrCreateUser(chatId, username);

  const welcomeText = 
    `👋 你好！这是抖音下载Bot。\n` +
    `你的 Chat ID: <code>${chatId}</code>\n` +
    `当前数据库记录的用户名: @${userRecord.username || ''}`;
    
  await ctx.reply(welcomeText, { parse_mode: 'HTML' });
});

bot.command('help', async (ctx) => {
  // --- 增加判断，忽略 STORAGE_CHANNEL_ID 的命令 ---
  if (ctx.chat?.id === STORAGE_CHANNEL_ID) { return; }
  // --- ---
  const chatId = ctx.chat.id;
  const userRecord = await getOrCreateUser(chatId, ctx.from.username || "");
  
  // 安全检查用户权限
  const sendAny = userRecord?.send_any ?? true; // 如果未定义，默认为 true
  if (!sendAny) {
    return;
  }

  // 使用用户指定的新帮助文本
  const helpText = 
    "【功能说明】\n" +
    "- 获取音乐：并非所有作品都提供音乐获取。\n" +
    "- 获取文件：将该作品所有媒体文件打包，避免telegram二次压缩图片文件。\n" +
    "- 订阅功能：抖音作者删除作品是很常见的事，并且随着时间画质会逐渐变差。点击订阅后，程序将在后台值守被订阅作者公开的新作品，我们的数据库将尽最大可能及时保留下作品内容。";

  await ctx.reply(helpText);
});

// 添加 /subscriptions 命令处理
bot.command('subscriptions', async (ctx) => {
  // --- 增加判断，忽略 STORAGE_CHANNEL_ID 的命令 ---
  if (ctx.chat?.id === STORAGE_CHANNEL_ID) { return; }
  // --- ---
  const chatId = ctx.chat.id;
  const username = ctx.from.username || "";
  
  try {
    // 确保用户在我们的系统中
    const userRecord = await getOrCreateUser(chatId, username);
    
    // 查询用户的订阅数量
    const { count, error: countError } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("user_id", chatId);
    
    if (countError) {
      logger.error(`获取订阅数量失败: ${countError.message}`, countError);
      await ctx.reply("获取订阅列表时发生错误，请稍后再试。");
      return;
    }
    
    if (count === 0) {
      await ctx.reply("你当前没有订阅任何抖音创作者。\n\n可以通过点击视频下方的「订阅」按钮来订阅创作者。");
      return;
    }
    
    // 查询用户的订阅关系，包括分页
    const limit = 10; // 每页显示的数量
    const page = 1; // 默认第一页
    
    const { data: subscriptions, error } = await supabase
      .from("telegram_douyin_map")
      .select(`
        uid,
        douyin_user!inner(
          nickname,
          unique_id,
          avatar_thumb_url,
          follower_count,
          total_favorited
        )
      `)
      .eq("user_id", chatId)
      .range((page - 1) * limit, page * limit - 1);
    
    if (error) {
      logger.error(`获取订阅列表失败: ${error.message}`, error);
      await ctx.reply("获取订阅列表时发生错误，请稍后再试。");
      return;
    }
    
    if (!subscriptions || subscriptions.length === 0) {
      await ctx.reply("你当前没有订阅任何抖音创作者。\n\n可以通过点击视频下方的「订阅」按钮来订阅创作者。");
      return;
    }
    
    // 为每个订阅创建一个取消订阅按钮
    const buttons = [];
    let message = `📋 <b>你的订阅列表 (${count} 个订阅)</b>`;
    
    if (count > limit) {
      message += ` - 显示 ${Math.min(limit, subscriptions.length)}/${count}\n`;
    }
    
    message += "\n\n";
    
    for (let i = 0; i < subscriptions.length; i++) {
      const sub = subscriptions[i];
      const creator = sub.douyin_user;
      const uid = sub.uid;
      
      // 为每个创作者添加信息行
      message += `<b>${(page - 1) * limit + i + 1}. ${creator.nickname || "未知用户"}</b>\n`;
      
      if (creator.unique_id) {
        message += `抖音号: ${creator.unique_id}\n`;
      }
      
      if (creator.follower_count) {
        message += `粉丝数: ${numberWithCommas(creator.follower_count)}\n`;
      }
      
      if (creator.total_favorited) {
        message += `获赞数: ${numberWithCommas(creator.total_favorited)}\n`;
      }
      
      message += "\n";
      
      // 添加按钮
      buttons.push([
        Markup.button.callback(`🗑️ 取消订阅: ${creator.nickname || uid}`, `unsub:${uid}`)
      ]);
    }
    
    // 添加一个全部取消的按钮
    buttons.push([
      Markup.button.callback("🗑️ 取消所有订阅", "unsub_all")
    ]);
    
    // 如果有多页，添加翻页按钮
    if (count > limit) {
      const pageButtons = [];
      
      if (page > 1) {
        pageButtons.push(Markup.button.callback("◀️ 上一页", `subs_page:${page - 1}`));
      }
      
      if (page * limit < count) {
        pageButtons.push(Markup.button.callback("▶️ 下一页", `subs_page:${page + 1}`));
      }
      
      if (pageButtons.length > 0) {
        buttons.push(pageButtons);
      }
    }
    
    const markup = Markup.inlineKeyboard(buttons);
    
    await ctx.reply(message, {
      parse_mode: 'HTML',
      ...markup
    });
    
  } catch (error) {
    logger.error(`处理订阅列表命令出错: ${error.message}`, error);
    await ctx.reply("获取订阅列表时发生错误，请稍后再试。");
  }
});

// 添加分页处理
bot.action(/^subs_page:(\d+)$/, async (ctx) => {
  const chatId = ctx.chat.id;
  const page = parseInt(ctx.match[1]);
  
  if (isNaN(page) || page < 1) {
    await ctx.answerCbQuery("无效的页码");
    return;
  }
  
  try {
    // 查询用户的订阅数量
    const { count, error: countError } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("user_id", chatId);
    
    if (countError) {
      await ctx.answerCbQuery("获取订阅数量失败");
      return;
    }
    
    const limit = 10;
    if ((page - 1) * limit >= count) {
      await ctx.answerCbQuery("页码超出范围");
      return;
    }
    
    // 查询当前页的订阅
    const { data: subscriptions, error } = await supabase
      .from("telegram_douyin_map")
      .select(`
        uid,
        douyin_user!inner(
          nickname,
          unique_id,
          avatar_thumb_url,
          follower_count,
          total_favorited
        )
      `)
      .eq("user_id", chatId)
      .range((page - 1) * limit, page * limit - 1);
    
    if (error) {
      await ctx.answerCbQuery("获取订阅列表失败");
      return;
    }
    
    // 为每个订阅创建一个取消订阅按钮
    const buttons = [];
    let message = `📋 <b>你的订阅列表 (${count} 个订阅)</b>`;
    
    if (count > limit) {
      message += ` - 显示 ${(page - 1) * limit + 1}-${Math.min(page * limit, count)}/${count}\n`;
    }
    
    message += "\n\n";
    
    for (let i = 0; i < subscriptions.length; i++) {
      const sub = subscriptions[i];
      const creator = sub.douyin_user;
      const uid = sub.uid;
      
      // 为每个创作者添加信息行
      message += `<b>${(page - 1) * limit + i + 1}. ${creator.nickname || "未知用户"}</b>\n`;
      
      if (creator.unique_id) {
        message += `抖音号: ${creator.unique_id}\n`;
      }
      
      if (creator.follower_count) {
        message += `粉丝数: ${numberWithCommas(creator.follower_count)}\n`;
      }
      
      if (creator.total_favorited) {
        message += `获赞数: ${numberWithCommas(creator.total_favorited)}\n`;
      }
      
      message += "\n";
      
      // 添加按钮
      buttons.push([
        Markup.button.callback(`🗑️ 取消订阅: ${creator.nickname || uid}`, `unsub:${uid}`)
      ]);
    }
    
    // 添加一个全部取消的按钮
    buttons.push([
      Markup.button.callback("🗑️ 取消所有订阅", "unsub_all")
    ]);
    
    // 如果有多页，添加翻页按钮
    if (count > limit) {
      const pageButtons = [];
      
      if (page > 1) {
        pageButtons.push(Markup.button.callback("◀️ 上一页", `subs_page:${page - 1}`));
      }
      
      if (page * limit < count) {
        pageButtons.push(Markup.button.callback("▶️ 下一页", `subs_page:${page + 1}`));
      }
      
      if (pageButtons.length > 0) {
        buttons.push(pageButtons);
      }
    }
    
    const markup = Markup.inlineKeyboard(buttons);
    
    await ctx.editMessageText(message, {
      parse_mode: 'HTML',
      ...markup
    });
    
    await ctx.answerCbQuery();
  } catch (error) {
    logger.error(`处理翻页请求出错: ${error.message}`, error);
    await ctx.answerCbQuery("处理请求失败，请稍后重试");
  }
});

// 工具函数：格式化数字，添加千位分隔符
function numberWithCommas(x) {
  return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// 添加取消订阅的回调处理
bot.action(/^unsub:(.+)$/, async (ctx) => {
  const uid = ctx.match[1];
  const chatId = ctx.chat.id;
  
  try {
    // 查询创作者信息
    const { data, error } = await supabase
      .from("douyin_user")
      .select("nickname, unique_id")
      .eq("uid", uid)
      .limit(1);
    
    if (error) {
      await ctx.answerCbQuery("获取创作者信息失败");
      return;
    }
    
    const creator = data && data.length > 0 ? data[0] : { nickname: "未知用户", unique_id: "" };
    
    // 构建确认消息
    const message = `确定要取消订阅 <b>${creator.nickname || uid}</b> 吗？`;
    const buttons = [
      [
        Markup.button.callback("✅ 确定取消", `confirm_unsub:${uid}`),
        Markup.button.callback("❌ 取消操作", "cancel_unsub")
      ]
    ];
    
    const markup = Markup.inlineKeyboard(buttons);
    
    await ctx.editMessageText(message, {
      parse_mode: 'HTML',
      ...markup
    });
    
    await ctx.answerCbQuery();
  } catch (error) {
    logger.error(`处理取消订阅请求出错: ${error.message}`, error);
    await ctx.answerCbQuery("处理请求失败，请稍后重试");
  }
});

// 添加确认取消订阅的回调处理
bot.action(/^confirm_unsub:(.+)$/, async (ctx) => {
  const uid = ctx.match[1];
  const chatId = ctx.chat.id;
  
  try {
    // 查询创作者信息
    const { data: userData, error: userError } = await supabase
      .from("douyin_user")
      .select("nickname, unique_id")
      .eq("uid", uid)
      .limit(1);
    
    const creator = userData && userData.length > 0 ? userData[0] : { nickname: "未知用户", unique_id: "" };
    
    // 删除订阅关系
    const { error } = await supabase
      .from("telegram_douyin_map")
      .delete()
      .eq("user_id", chatId)
      .eq("uid", uid);
    
    if (error) {
      await ctx.answerCbQuery("取消订阅失败，请稍后重试", { show_alert: true });
      return;
    }
    
    // 检查是否还有其他用户订阅该创作者
    const { count, error: countError } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("uid", uid);
    
    if (!countError && count === 0) {
      // 如果没有其他用户订阅，将surveillance字段设为false
      await supabase
        .from("douyin_user")
        .update({ surveillance: false })
        .eq("uid", uid);
    }
    
    await ctx.editMessageText(
      `✅ 已成功取消订阅 <b>${creator.nickname || uid}</b>！\n\n使用 /subscriptions 查看你的订阅列表。`,
      { parse_mode: 'HTML' }
    );
    
    await ctx.answerCbQuery("已成功取消订阅", { show_alert: true });
  } catch (error) {
    logger.error(`确认取消订阅出错: ${error.message}`, error);
    await ctx.answerCbQuery("处理请求失败，请稍后重试", { show_alert: true });
  }
});

// 添加取消操作的回调处理
bot.action('cancel_unsub', async (ctx) => {
  await ctx.answerCbQuery("已取消操作", { show_alert: true });
  await ctx.editMessageText("操作已取消，订阅保持不变。请使用 /subscriptions 查看订阅列表。");
});

// 添加取消所有订阅的回调处理
bot.action('unsub_all', async (ctx) => {
  const chatId = ctx.chat.id;
  
  try {
    // 查询用户的订阅数量
    const { count, error: countError } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("user_id", chatId);
    
    if (countError) {
      await ctx.answerCbQuery("获取订阅数量失败");
      return;
    }
    
    if (count === 0) {
      await ctx.answerCbQuery("你当前没有订阅任何创作者", { show_alert: true });
      return;
    }
    
    // 构建确认消息
    const message = `⚠️ <b>确定要取消所有 ${count} 个订阅吗？</b>\n\n此操作无法撤销，你将不再收到这些创作者的作品更新。`;
    const buttons = [
      [
        Markup.button.callback("✅ 确定取消所有", "confirm_unsub_all"),
        Markup.button.callback("❌ 取消操作", "cancel_unsub")
      ]
    ];
    
    const markup = Markup.inlineKeyboard(buttons);
    
    await ctx.editMessageText(message, {
      parse_mode: 'HTML',
      ...markup
    });
    
    await ctx.answerCbQuery();
  } catch (error) {
    logger.error(`处理取消所有订阅请求出错: ${error.message}`, error);
    await ctx.answerCbQuery("处理请求失败，请稍后重试");
  }
});

// 添加确认取消所有订阅的回调处理
bot.action('confirm_unsub_all', async (ctx) => {
  const chatId = ctx.chat.id;
  
  try {
    // 获取用户订阅的所有创作者UID
    const { data: subscriptions, error: getError } = await supabase
      .from("telegram_douyin_map")
      .select("uid")
      .eq("user_id", chatId);
    
    if (getError) {
      await ctx.answerCbQuery("获取订阅信息失败，请稍后重试", { show_alert: true });
      return;
    }
    
    // 删除所有订阅关系
    const { error } = await supabase
      .from("telegram_douyin_map")
      .delete()
      .eq("user_id", chatId);
    
    if (error) {
      await ctx.answerCbQuery("取消订阅失败，请稍后重试", { show_alert: true });
      return;
    }
    
    // 检查每个创作者是否还有其他订阅者
    if (subscriptions && subscriptions.length > 0) {
      for (const sub of subscriptions) {
        const { count, error: countError } = await supabase
          .from("telegram_douyin_map")
          .select("*", { count: 'exact', head: true })
          .eq("uid", sub.uid);
        
        if (!countError && count === 0) {
          // 如果没有其他用户订阅，将surveillance字段设为false
          await supabase
            .from("douyin_user")
            .update({ surveillance: false })
            .eq("uid", sub.uid);
        }
      }
    }
    
    await ctx.editMessageText(
      `✅ 已成功取消所有订阅！\n\n你不会再收到这些创作者的作品更新。`,
      { parse_mode: 'HTML' }
    );
    
    await ctx.answerCbQuery("已成功取消所有订阅", { show_alert: true });
  } catch (error) {
    logger.error(`确认取消所有订阅出错: ${error.message}`, error);
    await ctx.answerCbQuery("处理请求失败，请稍后重试", { show_alert: true });
  }
});

// =============== 回调按钮处理 ===============
bot.on('callback_query', async (ctx) => {
  // --- 增加判断，忽略 STORAGE_CHANNEL_ID 的回调 ---
  if (ctx.chat?.id === STORAGE_CHANNEL_ID) {
    logger.info(`忽略来自 STORAGE_CHANNEL_ID (${STORAGE_CHANNEL_ID}) 的回调: ${ctx.callbackQuery.data}`);
    await ctx.answerCbQuery(); // 静默应答，防止按钮一直转圈
    return;
  }
  // --- ---

  const chatId = ctx.chat.id;
  const fromUsername = ctx.from.username || "";
  const userRecord = await getOrCreateUser(chatId, fromUsername);

  // 可能数据库里没该字段时，给默认
  const sendAny = userRecord.send_any !== undefined ? userRecord.send_any : true;
  const todayLimit = userRecord.today || 10;
  const alreadyUsed = userRecord.already || 0;

  if (!sendAny || alreadyUsed >= todayLimit) {
    await ctx.answerCbQuery("你已达到每日配额上限，使用量将在下个23:00清空。", { show_alert: true });
    return;
  }

  const data = ctx.callbackQuery.data;

  // ------ 获取更多作者作品 ------
  if (data.startsWith("author:")) {
    const shortId = data.substring(7); // Remove "author:" prefix
    const secUserId = AUTHOR_SEC_UID_MAP[shortId];
    
    if (!secUserId) {
      await ctx.answerCbQuery("无效sec_user_id", { show_alert: true });
      return;
    }

    await ctx.answerCbQuery("正在处理请求...");

    try {
      // 从douyin_user表中查询作者信息
      const { data: userData, error: userError } = await supabase
        .from("douyin_user")
        .select("*")
        .eq("sec_uid", secUserId)
        .limit(1);
      
      if (userError) {
        logger.error(`查询作者信息失败: ${userError.message}`, userError);
        await ctx.reply("获取作者信息失败，请稍后再试。");
        return;
      }
      
      if (!userData || userData.length === 0) {
        // 如果没有找到作者信息，尝试先获取一个作品来获取作者信息
        try {
          // 获取作者的一个作品以获取作者信息
          const respJson = await fetchUserPostVideosApi(secUserId, 0, 1);
          const data = respJson?.data || {};
          const awemeList = data.aweme_list || [];
          
          if (awemeList.length > 0) {
            const firstVideo = await fetchOneVideoApi(awemeList[0].aweme_id);
            const authorData = parseDouyinUser(firstVideo);
            
            if (authorData) {
              await saveDouyinUserToDatabase(authorData);
              userData = [authorData];
            } else {
              await ctx.reply("无法获取作者信息，请稍后再试。");
              return;
            }
          } else {
            await ctx.reply("未找到该作者的任何作品。");
            return;
          }
        } catch (error) {
          logger.error(`获取作者信息失败: ${error.message}`, error);
          await ctx.reply("处理请求时出错，请稍后再试。");
          return;
        }
      }
      
      const author = userData[0];
      const uid = author.uid;
      const nickname = author.nickname || "未知用户";
      
      if (!uid) {
        await ctx.reply("作者信息不完整，无法处理请求。");
        return;
      }
      
      // 检查用户是否已经发出过获取全部作品的请求
      const { data: existingMap, error: checkError } = await supabase
        .from("telegram_douyin_map")
        .select("*")
        .eq("user_id", chatId)
        .eq("uid", uid)
        .limit(1);
        
      if (checkError) {
        logger.error(`检查映射关系失败: ${checkError.message}`, checkError);
        await ctx.reply("检查请求状态失败，请稍后再试。");
        return;
      }
      
      // 1. 将surveillance字段标记为true
      const { error: updateError } = await supabase
        .from("douyin_user")
        .update({ surveillance: true })
        .eq("sec_uid", secUserId);
      
      if (updateError) {
        logger.error(`更新作者surveillance字段失败: ${updateError.message}`, updateError);
        await ctx.reply("处理请求时出错，请稍后再试。");
        return;
      }
      
      // 2. 在telegram_douyin_map表中添加对应关系（如果不存在）
      if (!existingMap || existingMap.length === 0) {
        const { error: mapError } = await supabase
          .from("telegram_douyin_map")
          .insert([{
            user_id: chatId,
            uid: uid
          }]);
            
        if (mapError) {
          logger.error(`创建用户映射关系失败: ${mapError.message}`, mapError);
          await ctx.reply("处理请求时出错，请稍后再试。");
          return;
        }
      }
      
      // 3. 发送提示消息
      await ctx.reply(
        `已记录您获取 <b>${nickname}</b> 全部作品的请求。\n\n` +
        `系统将在后台抓取该作者的所有作品，完成后会自动发送给您。\n\n` +
        `请耐心等待，无需重复操作。`,
        { parse_mode: 'HTML' }
      );
      
      // 更新配额
      await updateUser(chatId, { already: alreadyUsed + 1 });
      
      logger.info(`用户 ${chatId} 请求获取作者 ${uid} (${nickname}) 的全部作品，已设置监听`);
      
    } catch (error) {
      logger.error(`处理获取全部作品请求出错: ${error.message}`, error);
      await ctx.reply("处理请求时发生错误，请稍后再试。");
    }
  }
  // ------ 下载音乐 ------
  else if (data.startsWith("music:")) {
    const awemeId = data.substring(6); // Get awemeId from callback data

    if (!awemeId) {
      await ctx.answerCbQuery("无效的作品ID", { show_alert: true });
      return;
    }

    // Check quota again specifically for music/zip actions if needed
    if (!sendAny || alreadyUsed >= todayLimit) {
      await updateUser(chatId, { send_any: false });
      await ctx.answerCbQuery("你已达到每日配额上限。", { show_alert: true });
      return;
    }

    await ctx.answerCbQuery("正在查找音乐...");

    try {
      // 1. Query database using awemeId
      const { data: workData, error: dbError } = await supabase
        .from("douyin")
        .select("file_id")
        .eq("aweme_id", awemeId)
        .limit(1);

      if (dbError) {
        logger.error(`回调查询作品 ${awemeId} 的 file_id 失败: ${dbError.message}`, dbError);
        await ctx.answerCbQuery("查找音乐信息失败", { show_alert: true });
        return;
      }

      if (!workData || workData.length === 0 || !workData[0].file_id) {
        await ctx.answerCbQuery("未找到该作品的音乐文件信息", { show_alert: true });
        return;
      }

      // 2. Parse file_id string to find the audio file_id
      const fileIds = workData[0].file_id.split(';');
      let audioFileId = null;
      for (const fileId of fileIds) {
        if (detectFileType(fileId) === 'audio') {
          audioFileId = fileId;
          break;
        }
      }

      if (!audioFileId) {
        await ctx.answerCbQuery("无法从记录中找到音乐文件", { show_alert: true });
        logger.warning(`用户 ${chatId} 请求作品 ${awemeId} 的音乐，但在数据库记录中未找到 audio file_id: ${workData[0].file_id}`);
        return;
      }

      // 3. Send the audio file using the found file_id
      await ctx.replyWithAudio(audioFileId);
      await ctx.answerCbQuery("音乐已发送"); // Optional: remove this if replyWithAudio is enough feedback

      // Update quota
      await updateUser(chatId, { already: alreadyUsed + 1 });
      logger.info(`用户 ${chatId} 点击按钮获取了作品 ${awemeId} 的音乐 (file_id: ${audioFileId})`);

    } catch (error) {
      // Catch potential errors during DB query or sending audio
      logger.error(`发送音乐 (awemeId: ${awemeId}, 从DB获取) 失败: ${error.message}`, error);
      // Try sending an informative message to the user
      try {
          await ctx.reply("抱歉，发送音乐时遇到问题，请稍后再试。");
      } catch (replyError) {
          logger.error(`无法向用户 ${chatId} 发送音乐错误通知: ${replyError.message}`);
      }
      // Avoid answering the callback query again if a reply was sent
      // await ctx.answerCbQuery("发送音乐时出错", { show_alert: true });
    }
  }
  // ------ 订阅作者 ------
  else if (data.startsWith("sub:")) {
    const idOrSecUid = data.substring(4); // Remove "sub:" prefix
    let secUserId = idOrSecUid;
    
    // Check if this is a shortId that needs to be looked up
    if (idOrSecUid.length <= 8) {
      secUserId = AUTHOR_SEC_UID_MAP[idOrSecUid] || idOrSecUid;
    }
    
    if (!secUserId) {
      await ctx.answerCbQuery("无效的作者ID", { show_alert: true });
      return;
    }
    
    // 检查配额
    if (!sendAny || alreadyUsed >= todayLimit) {
      await updateUser(chatId, { send_any: false });
      await ctx.answerCbQuery("你已达到每日配额上限。", { show_alert: true });
      return;
    }
    
    // 检查用户等级和订阅限制
    const isPremium = isPremiumUser(userRecord);
    
    // 如果不是Premium用户，检查订阅数量限制
    if (!isPremium) {
      const subscriptionCheck = await checkSubscriptionLimit(chatId);
      if (!subscriptionCheck.success) {
        await ctx.answerCbQuery(subscriptionCheck.message, { show_alert: true });
        return;
      }
    }
    
    try {
      // 从douyin_user表中查询作者信息
      const { data: userData, error: userError } = await supabase
        .from("douyin_user")
        .select("*")
        .eq("sec_uid", secUserId)
        .limit(1);
      
      if (userError) {
        logger.error(`查询作者信息失败: ${userError.message}`, userError);
        await ctx.answerCbQuery("查询作者信息失败", { show_alert: true });
        return;
      }
      
      if (!userData || userData.length === 0) {
        // 尝试通过API获取用户信息
        try {
          // 尝试直接通过用户资料API获取
          const profileResp = await fetchUserProfileApi(secUserId);
          const profileData = parseUserProfileData(profileResp);
          
          if (profileData) {
            // 可能需要基本用户信息
            // 获取用户的一个作品以提取基本信息
            const respJson = await fetchUserPostVideosApi(secUserId, 0, 1);
            const data = respJson?.data || {};
            const awemeList = data.aweme_list || [];
            
            if (awemeList.length > 0) {
              const firstVideo = await fetchOneVideoApi(awemeList[0].aweme_id);
              const baseUserData = parseDouyinUser(firstVideo);
              
              if (baseUserData) {
                // 合并并保存用户信息
                await saveEnhancedUserToDatabase(baseUserData, profileData);
                
                // 重新查询数据库获取完整信息
                const { data: newUserData, error: newError } = await supabase
                  .from("douyin_user")
                  .select("*")
                  .eq("sec_uid", secUserId)
                  .limit(1);
                  
                if (!newError && newUserData && newUserData.length > 0) {
                  userData = newUserData;
                } else {
                  await ctx.answerCbQuery("保存作者信息后查询失败", { show_alert: true });
                  return;
                }
              } else {
                await ctx.answerCbQuery("未找到该作者信息", { show_alert: true });
                return;
              }
            } else {
              await ctx.answerCbQuery("未找到该作者信息", { show_alert: true });
              return;
            }
          } else {
            await ctx.answerCbQuery("未找到该作者信息", { show_alert: true });
            return;
          }
        } catch (apiError) {
          logger.error(`通过API获取作者信息失败: ${apiError.message}`, apiError);
          await ctx.answerCbQuery("未找到该作者信息", { show_alert: true });
          return;
        }
      }
      
      const author = userData[0];
      const uid = author.uid;
      
      if (!uid) {
        logger.warning(`作者 ${secUserId} 没有uid字段，无法添加映射关系`);
        await ctx.answerCbQuery("订阅失败：作者信息不完整", { show_alert: true });
        return;
      }

      // 检查用户是否已经订阅过这个作者
      const { data: existingMap, error: checkError } = await supabase
        .from("telegram_douyin_map")
        .select("*")
        .eq("user_id", chatId)
        .eq("uid", uid)
        .limit(1);
        
      if (checkError) {
        logger.error(`检查订阅映射失败: ${checkError.message}`, checkError);
        await ctx.answerCbQuery("检查订阅状态失败", { show_alert: true });
        return;
      }
      
      if (existingMap && existingMap.length > 0) {
        // 用户已经订阅了这个作者
        await ctx.answerCbQuery(`你已经订阅了该作者: ${author.nickname || author.unique_id || "未知用户"}`, { show_alert: true });
        return;
      }
      
      // 尝试获取用户的增强资料
      try {
        const profileResp = await fetchUserProfileApi(secUserId);
        const profileData = parseUserProfileData(profileResp);
        
        if (profileData) {
          logger.info(`订阅时成功获取用户 ${author.nickname || uid} 的增强资料`);
          // 合并并保存
          await saveEnhancedUserToDatabase(author, profileData);
        }
      } catch (profileError) {
        logger.error(`订阅时获取用户 ${author.nickname || uid} 的增强资料失败: ${profileError.message}`, profileError);
        // 继续处理，不中断流程
      }
      
      // 更新surveillance字段为true
      const { error: updateError } = await supabase
        .from("douyin_user")
        .update({ surveillance: true })
        .eq("sec_uid", secUserId);
      
      if (updateError) {
        logger.error(`更新作者surveillance字段失败: ${updateError.message}`, updateError);
        await ctx.answerCbQuery("订阅作者失败", { show_alert: true });
        return;
      }
      
      // 创建用户与作者的映射关系
      const { error: mapError } = await supabase
        .from("telegram_douyin_map")
        .insert([{
          user_id: chatId,
          uid: uid
        }]);
          
      if (mapError) {
        logger.error(`创建用户订阅映射失败: ${mapError.message}`, mapError);
        await ctx.answerCbQuery(`订阅关系建立失败: ${author.nickname || author.unique_id || secUserId}`, { show_alert: true });
        return;
      }
      
      logger.info(`已创建用户 ${chatId} 订阅作者 ${uid} (${author.nickname || ""}) 的映射`);
      await ctx.answerCbQuery(`成功订阅作者: ${author.nickname || author.unique_id || secUserId}`, { show_alert: true });
      
    } catch (error) {
      logger.error(`订阅作者出错: ${error.message}`, error);
      await ctx.answerCbQuery("订阅时发生错误", { show_alert: true });
    }
  }
  // ------ 获取ZIP文件 ------
  else if (data.startsWith("zip:")) {
     // Check quota again specifically for music/zip actions if needed
    if (!sendAny || alreadyUsed >= todayLimit) {
      await updateUser(chatId, { send_any: false });
      await ctx.answerCbQuery("你已达到每日配额上限。", { show_alert: true });
      return;
    }
    const awemeId = data.substring(4);

    // --- Fix: Ensure awemeId is valid ---
    if (!awemeId) {
       await ctx.answerCbQuery("无效的作品ID", { show_alert: true });
       return;
    }
    // --- ---

    try {
        // --- Fix: Correct the database query ---
        const { data: workData, error: dbError } = await supabase
            .from("douyin")
            .select("file_id")
            .eq("aweme_id", awemeId)
            .limit(1);
        // --- ---

        if (dbError) {
            logger.error(`回调查询作品 ${awemeId} 的 file_id 失败: ${dbError.message}`, dbError);
            await ctx.answerCbQuery("查找ZIP文件信息失败", { show_alert: true });
            return;
        }
        if (!workData || workData.length === 0 || !workData[0].file_id) {
             await ctx.answerCbQuery("未找到该作品的ZIP文件信息", { show_alert: true });
             logger.warning(`用户 ${chatId} 请求作品 ${awemeId} 的ZIP，但数据库记录未找到或file_id为空`);
             return;
        }

        const fileIds = workData[0].file_id.split(';');
        let zipFileId = null;
        for (const fileId of fileIds) {
            // Ensure fileId is a non-empty string before checking type
            if (fileId && detectFileType(fileId) === 'document') {
                zipFileId = fileId;
                break;
            }
        }

        if (!zipFileId) {
            await ctx.answerCbQuery("无法从记录中找到ZIP文件", { show_alert: true });
            logger.warning(`用户 ${chatId} 请求作品 ${awemeId} 的ZIP，但在数据库记录中未找到 document file_id: ${workData[0].file_id}`);
            return;
        }

        await ctx.answerCbQuery("正在发送ZIP文件...");
        await ctx.replyWithDocument(zipFileId);
        logger.info(`用户 ${chatId} 点击按钮获取了作品 ${awemeId} 的ZIP (file_id: ${zipFileId})`);

        // Update quota *after* successful initiation
        await updateUser(chatId, { already: alreadyUsed + 1 });

    } catch (error) {
         logger.error(`发送ZIP文件 (awemeId: ${awemeId}, 从DB获取) 失败: ${error.message}`, error);
         // Try sending an informative message to the user
         try {
             await ctx.reply("抱歉，发送ZIP文件时遇到问题，请稍后再试。");
         } catch (replyError) {
             logger.error(`无法向用户 ${chatId} 发送ZIP错误通知: ${replyError.message}`);
         }
         // Avoid answering the callback query again if a reply was sent
         // await ctx.answerCbQuery("发送ZIP文件时出错", { show_alert: true });
    }
  }
  else {
    await ctx.answerCbQuery("未知操作", { show_alert: true });
  }
});

// =============== 处理文本消息：抖音链接 ===============
bot.on('text', async (ctx) => {
  // --- 增加判断，忽略 STORAGE_CHANNEL_ID 的消息 ---
  if (ctx.chat?.id === STORAGE_CHANNEL_ID) {
      logger.info(`忽略来自 STORAGE_CHANNEL_ID (${STORAGE_CHANNEL_ID}) 的文本消息: ${ctx.message.text}`);
      return;
  }
  // --- ---

  try {
    if (ctx.message.text.startsWith('/')) {
      // 忽略其他命令
      return;
    }

    const chatId = ctx.chat.id;
    const fromUsername = ctx.from.username || "";
    const userRecord = await getOrCreateUser(chatId, fromUsername);

    const sendAny = userRecord.send_any !== undefined ? userRecord.send_any : true;
    const todayLimit = userRecord.today || 10;
    const alreadyUsed = userRecord.already || 0;

    if (!sendAny || alreadyUsed >= todayLimit) {
      await updateUser(chatId, { send_any: false });
      await ctx.reply("你已达到每日配额上限，使用量将在下一个23:00清空。");
      return;
    }

    const text = ctx.message.text.trim();
    if (!text) {
      return;
    }

    // 1) 如果是作者主页链接
    const userUrl = extractDouyinUserUrl(text);
    if (userUrl) {
      // ... 保持原有的作者主页处理逻辑 ...
      return;
    }

    // 2) 如果是单作品链接
    const douyinUrl = extractDouyinVideoUrl(text);
    if (douyinUrl) {
      // 统一使用"正在处理..."
      const processingMsg = await ctx.reply("正在处理...");

      const respData = await callHybridVideoData(douyinUrl);
      
      // 检查是否是不可用作品
      if (respData.not_available) {
        const awemeId = respData.data?.aweme_id;
        
        await ctx.telegram.editMessageText(
          chatId, 
          processingMsg.message_id, 
          null, 
          `抱歉，该作品${awemeId ? ` (ID: ${awemeId})` : ""}不可用或已被删除。\n\n已在系统中标记该作品状态。`
        );
        
        // 更新配额
        await updateUser(chatId, { already: alreadyUsed + 1 });
        
        // 延迟删除消息
        setTimeout(async () => {
          try {
            await ctx.telegram.deleteMessage(chatId, processingMsg.message_id);
            await ctx.telegram.deleteMessage(chatId, ctx.message.message_id);
          } catch (error) {
            // Ignore errors when deleting messages
          }
        }, 5000);
        
        return;
      }
      
      const parsedData = parseDouyinWork(respData);
      
      if (!parsedData || !parsedData.base_info?.aweme_id) {
        await ctx.telegram.editMessageText(
          chatId, 
          processingMsg.message_id, 
          null, 
          "解析失败，链接无效或视频无法获取。"
        );
        
        // 尝试将作品标记为不可用
        try {
          // 从URL中尝试提取aweme_id
          const videoIdMatch = douyinUrl.match(/\/video\/(\d+)/);
          if (videoIdMatch && videoIdMatch[1]) {
            const awemeId = videoIdMatch[1];
            
            // 创建一个最小化的parsedData对象，只包含必要信息
            const minimalData = {
              base_info: { aweme_id: awemeId, desc: "Not Available" },
              author_info: {},
              location_info: {},
              music_info: {},
              media_info: {}
            };
            
            // 保存到数据库，标记为不可用
            await saveDouyinToDatabase(minimalData, false);
            logger.info(`已将作品 ${awemeId} 标记为不可用`);
          }
        } catch (error) {
          logger.error(`尝试标记不可用作品时出错: ${error.message}`, error);
        }
        
        // 延迟删除消息
        setTimeout(async () => {
          try {
            await ctx.telegram.deleteMessage(chatId, processingMsg.message_id);
            await ctx.telegram.deleteMessage(chatId, ctx.message.message_id);
          } catch (error) {
            // Ignore errors when deleting messages
          }
        }, 5000);
        
        return;
      }

      const awemeId = parsedData.base_info.aweme_id;
      
      // 检查作品是否已存在
      const { data: existingWork, error: checkError } = await supabase
        .from("douyin")
        .select("*")
        .eq("aweme_id", awemeId)
        .limit(1);
      
      if (checkError) {
        logger.error(`检查作品 ${awemeId} 是否存在时出错: ${checkError.message}`, checkError);
        await ctx.telegram.editMessageText(
          chatId, 
          processingMsg.message_id, 
          null, 
          "处理请求时出错，请稍后再试。"
        );
        return;
      }

      // 保存作者信息到数据库 (无论作品是否存在都更新作者信息)
      const userData = parseDouyinUser(respData);
      if (userData) {
        // 获取增强的用户资料数据
        const secUid = userData.sec_uid;
        let profileData = null;
        if (secUid) {
          try {
            const profileResp = await fetchUserProfileApi(secUid);
            profileData = parseUserProfileData(profileResp);
            if (profileData) {
              logger.info(`成功获取用户 ${userData.nickname || userData.uid} (${secUid}) 的增强资料`);
            }
          } catch (profileError) {
            logger.error(`获取用户 ${userData.nickname || userData.uid} 的增强资料失败: ${profileError.message}`, profileError);
          }
        }
        
        // 使用增强版保存函数
        await saveEnhancedUserToDatabase(userData, profileData);
      }

      if (existingWork && existingWork.length > 0) {
        // 作品已存在，直接使用现有记录
        const existingRecord = existingWork[0];
        
        if (existingRecord.file_id) {
          // 构建消息组件
          const captionText = buildCaptionForSingle(parsedData);
          const secUid = parsedData.author_info?.sec_uid || "";
          
          // 使用现有的file_id发送媒体
          await sendMediaByFileIds(
            ctx,
            existingRecord.file_id.split(';'),
            captionText,
            {
              inline_keyboard: [
                [
                  { text: "打开", url: douyinUrl },
                  existingRecord.file_id.includes('CQADAg') && { text: "获取音乐", callback_data: `music:${awemeId}` },
                  existingRecord.file_id.includes('BQACAg') && { text: "获取文件", callback_data: `zip:${awemeId}` },
                  secUid && { text: "订阅", callback_data: `sub:${secUid}` }
                ].filter(Boolean)
              ]
            }
          );
          
          await ctx.telegram.editMessageText(
            chatId, 
            processingMsg.message_id, 
            null, 
            "处理完成。"
          );
          
          // 更新配额
          await updateUser(chatId, { already: alreadyUsed + 1 });
          
          // 延迟删除消息
          setTimeout(async () => {
            try {
              await ctx.telegram.deleteMessage(chatId, processingMsg.message_id);
              await ctx.telegram.deleteMessage(chatId, ctx.message.message_id);
            } catch (error) {
              // Ignore errors when deleting messages
            }
          }, 5000);
          
          return;
        } else {
          logger.warning(`作品 ${awemeId} 存在但没有file_id，将重新处理`);
        }
      }
      
      // 作品不存在或需要重新处理
      // 保存作品到数据库
      await saveDouyinToDatabase(parsedData, true);

      const captionText = buildCaptionForSingle(parsedData);
      const secUid = parsedData.author_info?.sec_uid || "";

      const wdir = path.join("douyin", `single_${chatId}_${awemeId}`);
      await fs.mkdir(wdir, { recursive: true });
      const downloadedFiles = [];

      try {
        // ... 保持原有的媒体下载和处理逻辑 ...
        // 原有的下载、发送媒体文件的代码保持不变
      } finally {
        // 在finally块中的文件清理代码
        if (fsSync.existsSync(wdir)) {
          try {
            await fs.rm(wdir, { recursive: true, force: true });
            logger.info(`成功清理临时目录: ${wdir}`);
          } catch (cleanupError) {
            logger.warning(`清理临时目录 ${wdir} 失败: ${cleanupError.message}，将在后台异步清理`);
            // 使用setTimeout在后台异步尝试再次清理
            setTimeout(() => {
              try {
                fsSync.rmSync(wdir, { recursive: true, force: true });
              } catch (e) {
                // 忽略后台清理错误
              }
            }, 5000);
          }
        }
      }

      // ... 保持原有的延迟删除消息逻辑 ...
      setTimeout(async () => {
        try {
          await ctx.telegram.deleteMessage(chatId, processingMsg.message_id);
          await ctx.telegram.deleteMessage(chatId, ctx.message.message_id);
        } catch (error) {}
      }, 5000);
      
      return;
    }

    // ... 保持原有的未检测到链接逻辑 ...
    await ctx.reply("未检测到有效的抖音链接。");
  } catch (error) {
    // ... 保持原有的错误处理逻辑 ...
    logger.error(`处理文本消息出错: ${error.message}`, error);
    // 修正：只在有上下文的情况下回复
    if (ctx && ctx.reply) {
      await ctx.reply(`❌ 出错: ${error.message}`);
    }
  }
});

// =============== 入口函数 ===============
function main() {
  logger.info("Starting douyin_bot...");
  
  // 确保下载目录存在
  if (!fsSync.existsSync('douyin')) {
    fsSync.mkdirSync('douyin');
  }
  
  // 启动 bot
  bot.launch().then(async () => {
    logger.info("Bot is running!");
    
    // 获取并显示Bot信息
    try {
      const me = await bot.telegram.getMe();
      logger.info(`Logged in as: ${me.username} (ID: ${me.id})`);
    } catch (err) {
      logger.error(`Failed to get bot info: ${err.message}`, err);
    }
    
    // 初始化realtime监听
    const channel = await initRealtimeListener(bot);
    if (channel) {
      logger.info("Realtime监听已激活");
    } else {
      logger.warning("Realtime监听初始化失败");
    }
    
  }).catch(err => {
    logger.error(`Bot 启动失败: ${err.message}`, err);
  });
  
  // 优雅关闭
  process.once('SIGINT', () => bot.stop('SIGINT'));
  process.once('SIGTERM', () => bot.stop('SIGTERM'));
}

// 开始运行
main();

// 添加提取视频第一帧作为缩略图的函数
async function extractVideoThumbnail(videoPath) {
  try {
    const thumbnailPath = `${videoPath}_thumb.jpg`;
    
    // 使用ffmpeg提取第1秒的帧作为缩略图，并确保缩略图尺寸适合Telegram (宽度320px左右)
    // 注意：这里明确指定提取第1秒的帧，而不是第一帧
    const cmd = `ffmpeg -i "${videoPath}" -ss 00:00:01 -vframes 1 -vf "scale=320:-1" -q:v 2 "${thumbnailPath}" -y`;
    
    await execPromise(cmd);
    
    if (fsSync.existsSync(thumbnailPath) && fsSync.statSync(thumbnailPath).size > 0) {
      logger.info(`成功从视频第1秒提取缩略图: ${thumbnailPath}`);
      return thumbnailPath;
    } else {
      // 如果第1秒提取失败，尝试提取视频的第0.1秒作为备选
      const cmd2 = `ffmpeg -i "${videoPath}" -ss 00:00:00.1 -vframes 1 -vf "scale=320:-1" -q:v 2 "${thumbnailPath}" -y`;
      await execPromise(cmd2);
      
      if (fsSync.existsSync(thumbnailPath) && fsSync.statSync(thumbnailPath).size > 0) {
        logger.info(`成功从视频开始处提取缩略图: ${thumbnailPath}`);
        return thumbnailPath;
      }
    }
    
    logger.error(`缩略图提取失败或文件为空: ${thumbnailPath}`);
    return null;
  } catch (error) {
    logger.error(`提取视频缩略图时出错: ${error.message}`, error);
    return null;
  }
}

// 修改订阅按钮回调处理，使用 bot.telegram.sendMessage 发送最终结果
bot.action(/^sub:(.+)$/, async (ctx) => {
  const idOrSecUid = ctx.match[1]; // Remove "sub:" prefix
  let secUserId = idOrSecUid;
  const chatId = ctx.chat.id;
  const userId = ctx.from.id; // 获取用户ID用于日志

  // 立即响应用户
  try {
    await ctx.answerCbQuery("正在处理您的订阅请求...");
  } catch (e) {
    logger.warning(`为用户 ${userId} 应答订阅回调失败 (可能已超时): ${e.message}`);
  }
  
  // Check if this is a shortId that needs to be looked up
  if (idOrSecUid.length <= 8) {
    secUserId = AUTHOR_SEC_UID_MAP[idOrSecUid] || idOrSecUid;
  }
  
  if (!secUserId) {
    // 使用 bot.telegram 发送新消息
    await bot.telegram.sendMessage(chatId, "订阅失败：无效的作者ID").catch(e => logger.error(`发送无效作者ID消息失败: ${e.message}`));
    return;
  }
  
  // 将数据库操作放入异步匿名函数中执行，不阻塞主流程
  (async () => {
    try {
      // 获取用户记录，用于检查权限
      const userRecord = await getOrCreateUser(chatId, "");
      
      // 检查用户等级和订阅限制
      const isPremium = isPremiumUser(userRecord);
      
      // 如果不是Premium用户，检查订阅数量限制
      if (!isPremium) {
        const subscriptionCheck = await checkSubscriptionLimit(chatId);
        if (!subscriptionCheck.success) {
          await bot.telegram.sendMessage(chatId, subscriptionCheck.message).catch(e => logger.error(`发送订阅限制消息失败: ${e.message}`));
          return;
        }
      }
  
      // 检查每日配额
      const sendAny = userRecord.send_any !== undefined ? userRecord.send_any : true;
      const todayLimit = userRecord.today || 20; // 普通用户每日20条处理限额
      const alreadyUsed = userRecord.already || 0;
      
      if (!sendAny || alreadyUsed >= todayLimit) {
        await bot.telegram.sendMessage(chatId, "你已达到每日配额上限，请明天再试。").catch(e => logger.error(`发送配额限制消息失败: ${e.message}`));
        return;
      }
      
      // 从douyin_user表中查询作者信息
      const { data: userData, error: userError } = await supabase
        .from("douyin_user")
        .select("*")
        .eq("sec_uid", secUserId)
        .limit(1);
      
      if (userError) {
        logger.error(`查询作者信息失败 (用户: ${userId}): ${userError.message}`, userError);
        // 使用 bot.telegram 发送新消息
        await bot.telegram.sendMessage(chatId, `订阅失败：查询作者信息时出错`).catch(e => logger.error(`发送查询作者失败消息失败: ${e.message}`));
        return;
      }
      
      if (!userData || userData.length === 0) {
        // 使用 bot.telegram 发送新消息
        await bot.telegram.sendMessage(chatId, `订阅失败：未找到该作者信息`).catch(e => logger.error(`发送未找到作者消息失败: ${e.message}`));
        return;
      }
      
      const author = userData[0];
      const uid = author.uid;
      
      if (!uid) {
        logger.warning(`作者 ${secUserId} 没有uid字段，无法添加映射关系 (用户: ${userId})`);
        // 使用 bot.telegram 发送新消息
        await bot.telegram.sendMessage(chatId, `订阅失败：作者信息不完整`).catch(e => logger.error(`发送作者信息不完整消息失败: ${e.message}`));
        return;
      }

      // 检查用户是否已经订阅过这个作者
      const { count, error: checkError } = await supabase
        .from("telegram_douyin_map")
        .select("*", { count: 'exact', head: true }) // 只需检查是否存在
        .eq("user_id", chatId)
        .eq("uid", uid)
        .limit(1);
        
      if (checkError) {
        logger.error(`检查订阅映射失败 (用户: ${userId}): ${checkError.message}`, checkError);
        // 使用 bot.telegram 发送新消息
        await bot.telegram.sendMessage(chatId, `订阅失败：检查订阅状态时出错`).catch(e => logger.error(`发送检查订阅失败消息失败: ${e.message}`));
        return;
      }
      
      if (count && count > 0) { // 检查 count 而不是 data.length
        // 用户已经订阅了这个作者
        // 使用 bot.telegram 发送新消息
        await bot.telegram.sendMessage(chatId, `您已经订阅了该作者: ${author.nickname || author.unique_id || "未知用户"}`).catch(e => logger.error(`发送已订阅消息失败: ${e.message}`));
        return;
      }
      
      // 更新surveillance字段为true
      const { error: updateError } = await supabase
        .from("douyin_user")
        .update({ surveillance: true })
        .eq("sec_uid", secUserId);
      
      if (updateError) {
        logger.error(`更新作者surveillance字段失败 (用户: ${userId}): ${updateError.message}`, updateError);
        // 使用 bot.telegram 发送新消息
        await bot.telegram.sendMessage(chatId, `订阅失败：更新作者状态时出错`).catch(e => logger.error(`发送更新作者状态失败消息失败: ${e.message}`));
        return;
      }
      
      // 创建用户与作者的映射关系
      const { error: mapError } = await supabase
        .from("telegram_douyin_map")
        .insert([{ user_id: chatId, uid: uid }]);
          
      if (mapError) {
        logger.error(`创建用户订阅映射失败 (用户: ${userId}): ${mapError.message}`, mapError);
        // 使用 bot.telegram 发送新消息
        await bot.telegram.sendMessage(chatId, `订阅失败：创建订阅关系时出错`).catch(e => logger.error(`发送创建订阅关系失败消息失败: ${e.message}`));
        return;
      }
      
      logger.info(`已创建用户 ${userId} (chatId: ${chatId}) 订阅作者 ${uid} (${author.nickname || ""}) 的映射`);
      // 使用 bot.telegram 发送新消息
      await bot.telegram.sendMessage(chatId, `✅ 成功订阅作者: ${author.nickname || author.unique_id || secUserId}`).catch(e => logger.error(`发送订阅成功消息失败: ${e.message}`));
      
    } catch (error) {
      logger.error(`订阅作者出错 (用户: ${userId}): ${error.message}`, error);
      // 使用 bot.telegram 发送新消息
      await bot.telegram.sendMessage(chatId, `订阅时发生内部错误`).catch(e => logger.error(`发送订阅内部错误消息失败: ${e.message}`));
    }
  })(); // 立即执行异步函数
});

// 在代码中，添加一个函数来查找音频文件
function findAudioFileInFiles(files) {
  for (const file of files) {
    if (isAudioFile(file)) {
      return file;
    }
  }
  return null;
}

// 添加一个全局的未捕获异常处理器
process.on('uncaughtException', (error) => {
  try {
    logger.error(`全局未捕获异常: ${error.message}`, error);
    logger.error(`堆栈: ${error.stack}`);
  } catch (logError) {
    console.error('记录错误日志时出错:', logError);
    console.error('原始错误:', error);
  }
  // 不退出进程，让程序继续运行
});

process.on('unhandledRejection', (reason, promise) => {
  try {
    logger.error(`未处理的Promise拒绝: ${reason instanceof Error ? reason.message : String(reason)}`);
    if (reason instanceof Error) {
      logger.error(`堆栈: ${reason.stack}`);
    }
  } catch (logError) {
    console.error('记录错误日志时出错:', logError);
    console.error('原始拒绝原因:', reason);
  }
  // 不退出进程，让程序继续运行
});

// 修改callHybridVideoData函数，添加重试机制和不可用作品检测
async function callHybridVideoData(douyinUrl) {
  return retryApiCallWithBackoff(async () => {
    try {
      const encodedUrl = encodeURIComponent(douyinUrl);
      const apiUrl = `${API_BASE_URL}/api/hybrid/video_data?url=${encodedUrl}&minimal=false`;
      
      logger.info(`[callHybridVideoData] 原始URL: ${douyinUrl}`);
      logger.info(`[callHybridVideoData] 完整API URL: ${apiUrl}`);
      
      const response = await fetch(apiUrl, { timeout: 30000 });
      logger.info(`[callHybridVideoData] 状态码: ${response.status}`);
      
      // 检查429状态码
      if (response.status === 429) {
        throw { status: 429, message: "API限流，请求过于频繁" };
      }
      
      const text = await response.text();
      logger.info(`[callHybridVideoData] 响应内容(前200字): ${text.substring(0, 200)}...`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const jsonData = JSON.parse(text);
      
      // 检查作品是否不可用 - 通常API会返回一些特定的错误信息或空数据
      const isNotAvailable = (
        !jsonData || 
        !jsonData.data || 
        jsonData.status_code === 404 ||
        jsonData.status_code === 10000 || // 抖音API常见错误码
        (jsonData.data && jsonData.data.status_code === 404) ||
        (jsonData.data && jsonData.data.error_code === 10000) ||
        (jsonData.data && jsonData.data.aweme_id === undefined) ||
        (text.includes("Not Found") && text.includes("error")) ||
        text.includes("作品不存在") ||
        text.includes("视频不见了")
      );
      
      if (isNotAvailable) {
        // 如果作品不可用，提取aweme_id (如果可能)
        let awemeId = null;
        
        // 从URL中尝试提取aweme_id
        const videoIdMatch = douyinUrl.match(/\/video\/(\d+)/);
        const noteIdMatch = douyinUrl.match(/\/note\/(\d+)/);
        
        if (videoIdMatch && videoIdMatch[1]) {
          awemeId = videoIdMatch[1];
        } else if (noteIdMatch && noteIdMatch[1]) {
          awemeId = noteIdMatch[1];
        }
        
        if (awemeId) {
          logger.warning(`检测到不可用作品: ${awemeId}，将标记为not available`);
          
          // 创建一个最小化的parsedData对象，只包含必要信息
          const minimalData = {
            base_info: { aweme_id: awemeId, desc: "Not Available" },
            author_info: {},
            location_info: {},
            music_info: {},
            media_info: {}
          };
          
          // 保存到数据库，标记为不可用
          await saveDouyinToDatabase(minimalData, false);
          
          // 返回含有标记的结果，以便上层函数知道该作品不可用
          return { 
            data: { aweme_id: awemeId },
            not_available: true,
            status_message: "作品不可用或已删除"
          };
        }
      }
      
      return jsonData;
    } catch (error) {
      logger.error(`获取视频信息失败: ${error.message}`, error);
      throw error; // 重新抛出以便重试机制捕获
    }
  }, 3, 2000);
}