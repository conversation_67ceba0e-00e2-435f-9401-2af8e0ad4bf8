import os
import time
import hashlib
import requests
from tqdm import tqdm
from threading import Thread, Lock

# --- 配置区域 ---

VIDEO_URL = "https://v5-dy-o-abtest.zjcdn.com/4268adc05362cdafe1076c0ec6d0b18a/67f1de1d/video/tos/cn/tos-cn-ve-0015c800/oEGeOUha7rQzPgCJAnnpO3gIztA0fAQh3BTyBE/?a=6383&ch=26&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C3&cv=1&br=3348&bt=3348&cs=2&ft=CZcaELDcDCnNWJVQ9wiRcHahd.24orXR3-ApQX&mime_type=video_mp4&qs=15&rc=NjRpMzY8NjhpN2U5Z2QzOEBpMzRpPGg6Zm42bDMzNGkzM0AtLWAtNi81Xi4xLTVeLTFhYSMwY3M1cjRvNGRgLS1kLS9zcw%3D%3D&btag=c0000e00028000&cc=46&cquery=100x_100z_100o_100w_100B&dy_q=1743893380&feature_id=0f7ebde441698e4656a3890b7527f982&l=20250406064940EADD013B453BE18C3C66&req_cdn_type="  # 替换为实际链接
OUTPUT_DIR = "downloads"
MAX_RETRIES = 3
NUM_THREADS = 4

USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36"
REFERER = "https://www.douyin.com/"
COOKIE = (
    "odin_tt=44bc9a14a5a5d08eb300f4944a656a1eb453e59c89b6eef1fee3251a631d54f8;"
    "passport_fe_beating_status=false;"
    "sid_guard=2516bd7e85d2f90209d0aad8642d2b99%7C1743891086%7C21600%7CSun%2C+06-Apr-2025+04%3A11%3A26+GMT;"
    "uid_tt=4834c4867dbe5b48940a7a994980a160;"
    "uid_tt_ss=4834c4867dbe5b48940a7a994980a160;"
    "sid_tt=2516bd7e85d2f90209d0aad8642d2b99;"
    "sessionid=2516bd7e85d2f90209d0aad8642d2b99;"
    "sessionid_ss=2516bd7e85d2f90209d0aad8642d2b99;"
    "is_staff_user=false;"
    "sid_ucp_v1=1.0.0-KDQzN2Y2OWE2YmUyMzJjNTkwNWNkYzcxYzM4NWY5M2Q2M2VjN2ZlZTYKCBCO1ca_BhgNGgJobCIgMjUxNmJkN2U4NWQyZjkwMjA5ZDBhYWQ4NjQyZDJiOTk;"
    "ssid_ucp_v1=1.0.0-KDQzN2Y2OWE2YmUyMzJjNTkwNWNkYzcxYzM4NWY5M2Q2M2VjN2ZlZTYKCBCO1ca_BhgNGgJobCIgMjUxNmJkN2U4NWQyZjkwMjA5ZDBhYWQ4NjQyZDJiOTk;"
    "passport_assist_user=;"
    "ttwid=1%7CeRIW6YeZmoOUtIulN4iTmGuZGhGlB4Xfc_S2zBV_Ch4%7C1743891086%7C743f5174046506a2e014019e177e20f930de3b7a02ae8433bd591c3a5a080ca2;"
)

HEADERS = {
    "User-Agent": USER_AGENT,
    "Referer": REFERER,
    "Cookie": COOKIE
}

# --- 工具函数 ---

def get_content_length(url, headers):
    resp = requests.head(url, headers=headers, timeout=10)
    if resp.status_code in [200, 206]:
        return int(resp.headers.get("Content-Length", 0))
    return 0

def compute_sha256(filename):
    sha256 = hashlib.sha256()
    with open(filename, "rb") as f:
        for chunk in iter(lambda: f.read(8192), b""):
            sha256.update(chunk)
    return sha256.hexdigest()

def retry_request(url, headers, start, end, attempt=1):
    for i in range(MAX_RETRIES):
        try:
            headers["Range"] = f"bytes={start}-{end}"
            resp = requests.get(url, headers=headers, stream=True, timeout=15)
            if resp.status_code in [200, 206]:
                return resp
        except Exception:
            continue
    return None

# --- 多线程下载逻辑 ---

def download_chunk(url, headers, start, end, filename, index, lock, results):
    part_file = f"{filename}.part{index}"
    if os.path.exists(part_file):
        existing = os.path.getsize(part_file)
        start += existing
    with open(part_file, "ab") as f:
        resp = retry_request(url, headers.copy(), start, end)
        if resp:
            for chunk in resp.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    with lock:
                        results["downloaded"] += len(chunk)

def merge_parts(filename, num_parts):
    with open(filename, "wb") as output:
        for i in range(num_parts):
            part_file = f"{filename}.part{i}"
            with open(part_file, "rb") as pf:
                output.write(pf.read())
            os.remove(part_file)

# --- 主下载器 ---

def threaded_download(name, url, headers):
    filename = os.path.join(OUTPUT_DIR, f"{name}.mp4")
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    total = get_content_length(url, headers)
    if total == 0:
        print(f"❌ 获取文件大小失败：{url}")
        return None

    part_size = total // NUM_THREADS
    lock = Lock()
    threads = []
    results = {"downloaded": 0}

    print(f"\n▶ 开始下载 [{name}]，总大小：{total / 1024:.2f} KB，分片：{NUM_THREADS} 个")

    for i in range(NUM_THREADS):
        start = i * part_size
        end = total - 1 if i == NUM_THREADS - 1 else (i + 1) * part_size - 1
        t = Thread(target=download_chunk, args=(url, headers, start, end, filename, i, lock, results))
        threads.append(t)
        t.start()

    pbar = tqdm(total=total, unit="B", unit_scale=True, unit_divisor=1024)
    prev = 0
    while any(t.is_alive() for t in threads):
        time.sleep(0.5)
        with lock:
            delta = results["downloaded"] - prev
            prev = results["downloaded"]
        pbar.update(delta)
    pbar.close()

    for t in threads:
        t.join()

    merge_parts(filename, NUM_THREADS)

    if os.path.getsize(filename) == total:
        print(f"✅ 下载完成：{filename}")
        hash_val = compute_sha256(filename)
        print(f"🧬 SHA256 校验: {hash_val}")
        return {"filename": filename, "size": total, "hash": hash_val}
    else:
        print("⚠️ 文件大小不一致，可能下载不完整")
        return None

# --- 入口 ---

if __name__ == "__main__":
    result = threaded_download("douyin_test", VIDEO_URL, HEADERS)
