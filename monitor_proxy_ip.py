#!/usr/bin/env python3
import requests
import time
import datetime
import json
from collections import defaultdict

# 代理配置
PROXY = {
    'http': 'http://127.0.0.1:1080',
    'https': 'http://127.0.0.1:1080'
}

# IP检测API
IP_API = 'https://api.ipify.org?format=json'

def get_current_ip():
    """通过代理获取当前出口IP"""
    try:
        response = requests.get(IP_API, proxies=PROXY, timeout=10)
        if response.status_code == 200:
            return response.json().get('ip')
        else:
            return f"错误: {response.status_code}"
    except Exception as e:
        return f"异常: {str(e)}"

def main():
    """监测IP变化并统计"""
    print("开始监测代理出口IP变化...")
    print(f"时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    # 记录IP变化
    ip_history = []
    ip_count = defaultdict(int)
    
    try:
        while True:
            current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            current_ip = get_current_ip()
            
            ip_history.append((current_time, current_ip))
            ip_count[current_ip] += 1
            
            print(f"[{current_time}] 当前IP: {current_ip}")
            
            # 显示统计信息
            if len(ip_history) % 5 == 0:
                print("\n--- IP统计信息 ---")
                for ip, count in ip_count.items():
                    print(f"IP: {ip}, 出现次数: {count}")
                print("=" * 30)
            
            # 等待30秒再次检查
            time.sleep(30)
    except KeyboardInterrupt:
        print("\n监测已停止")
        
        # 保存结果到文件
        result = {
            "开始时间": ip_history[0][0] if ip_history else "",
            "结束时间": ip_history[-1][0] if ip_history else "",
            "IP历史": ip_history,
            "IP统计": {ip: count for ip, count in ip_count.items()}
        }
        
        with open("proxy_ip_monitor_result.json", "w", encoding="utf-8") as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"结果已保存到 proxy_ip_monitor_result.json")

if __name__ == "__main__":
    main()
